package com.wcp.sso.service;


import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.auth.entitiy.ZtreList;
import com.wcp.auth.util.ValidateCodeUtil;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.http.HttpStatus;
import com.wcp.redis.RedisCache;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.AESUtil;
import com.wcp.utils.DateUtil;
import com.wcp.utils.StringUtil;
import com.wcp.utils.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Description: 发布应用用户相关业务处理
 * Author: qianchao
 * Date: 2024/2/19 11:27
 */
@ApiGroup(value = "应用模块用户服务", serviceKey = "user", classify = "user", classifyName = "应用模块用户服务")
@Slf4j
public class UserInfoService {
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private TokenUtil tokenUtil;
    @Value("${spring.sso.captcha}")
    private boolean captcha;

    @ApiService(value = "用户登录", serviceKey = "login", signType = 0, notes = "用户登录", params = {
            @ApiParam(value = "用户名", name = "username", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "用户密码", name = "password", clazz = Object.class, paramType = "body", required = true)})
    public void login(Map<String, Object> result, JSONObject object) {
        String account = object.getString("account");
        String password = object.getString("password");
        String projectId = WcpThreadLocal.getProjectId();
        String uuid = object.getString("uuid");
        String captchaCode = object.getString("captchaCode");
        if (projectId == null) projectId = "default";
        //启用验证码
        if (captcha) {
            //获取缓存中的验证码
            String cacheCaptchaCode = redisCache.getCacheObject(uuid);
            if (!captchaCode.toUpperCase().equals(cacheCaptchaCode.toUpperCase())) {
                result.put(HttpStatus.MSG, "验证码错误!");
                return;
            }
        }
        System.out.println(AESUtil.decrypt(account));
        if (StringUtil.isNotEmpty(account) || StringUtil.isNotEmpty(password)) {
            String dePassword = AESUtil.decrypt(password);
            String timestampStr = dePassword.substring(dePassword.length() - 13, dePassword.length());
            String realPassword = dePassword.replace(timestampStr, "");
            password = AESUtil.encrypt(realPassword);
            Date date = new Date(Long.parseLong(timestampStr));
            Date curr = new Date();
            if (date.getSeconds() + 5 < curr.getSeconds()) {
                result.put(HttpStatus.CODE, HttpStatus.ERROR);
                result.put(HttpStatus.MSG, "密码验证失败!");
                return;
            }

            Map<String, Object> userInfo = WcpDataBase.querySingleMap("wcp", "SELECT ID,ACCOUNT,USERNAME FROM WCP_AUTH_USER WHERE ACCOUNT=? AND PWD=? AND PROJECT=?", new Object[]{AESUtil.decrypt(account), AESUtil.encrypt(password), projectId}, null);
            if (userInfo != null) {
                String tokenData = tokenUtil.createToken(userInfo);
                result.put("token", tokenData);
                result.put(HttpStatus.MSG, "登录成功!");
                return;
            }
        }
        result.put(HttpStatus.MSG, "用户名或密码错误!");
    }

    @ApiService(value = "用户注册", serviceKey = "register", signType = 0, notes = "用户注册", params = {
            @ApiParam(value = "用户名", name = "userName", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "用户账号", name = "account", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "账号密码", name = "pwd", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "确认密码", name = "confirmPwd", clazz = Object.class, paramType = "body", required = true)
    })
    public void register(Map<String, Object> result, JSONObject object) {
        String userName = object.getString("userName");
        String account = object.getString("account");
        String projectId = StringUtil.isNotEmpty(object.getString("projectId")) ? object.getString("projectId") : "default";
        String pwd = object.getString("pwd");//注册密码
        String password = object.getString("confirmPwd");//确认密码
        if (account == null || account.trim().equals("") || userName == null || "".equals(userName.trim())) {
            result.put(HttpStatus.MSG, "用户信息不能为空!");
            return;
        } else if (password == null || password.trim().equals("")) {
            result.put(HttpStatus.MSG, "密码不能为空!");
            return;
        } else if (!pwd.trim().equals(password)) {
            result.put(HttpStatus.MSG, "两次密码不相等!");
            return;
        }
        String realAccount = AESUtil.decrypt(account);
        //判断用户账号是否已经注册
        Map<String, Object> userInfo = WcpDataBase.querySingleMap("wcp", "SELECT ACCOUNT,PWD FROM WCP_AUTH_USER WHERE ACCOUNT=? ", new Object[]{realAccount}, null);
        if (userInfo != null && userInfo.size() > 0) {
            result.put(HttpStatus.MSG, "该用户已存在!");
        } else {
            boolean regFlag = WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_USER(ACCOUNT,PWD,USERNAME,PROJECT) VALUES(?,?,?,?)", new Object[]{realAccount, AESUtil.encrypt(pwd), userName, projectId});
            result.put(HttpStatus.MSG, regFlag ? "注册成功" : "注册失败");
        }
    }

    /**
     * 修改密码
     *
     * @param result
     * @param object
     */
    @ApiService(value = "修改密码", serviceKey = "updatePassword", signType = 2, notes = "修改密码", params = {
            @ApiParam(value = "用户账号", name = "account", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "旧密码", name = "oldPwd", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "新密码", name = "newPwd", clazz = Object.class, paramType = "body", required = true)
    })
    public void updatePassword(Map<String, Object> result, JSONObject object) {
        String account = object.getString("account");
        String oldPwd = object.getString("oldPwd");
        String newPwd = object.getString("newPwd");
        String projectId = WcpThreadLocal.getProjectId();
        if (StringUtil.isNotEmpty(account) && StringUtil.isNotEmpty(oldPwd) && StringUtil.isNotEmpty(newPwd)) {
            String realAccount = AESUtil.decrypt(account);
            Map<String, Object> userInfo = WcpDataBase.querySingleMap("wcp", "SELECT ID,ACCOUNT,PWD FROM WCP_AUTH_USER WHERE ACCOUNT=?  AND PROJECT=?", new Object[]{realAccount, projectId}, null);
            if (userInfo != null) {
                if (!userInfo.get("PWD").equals(AESUtil.encrypt(oldPwd))) {
                    result.put(HttpStatus.MSG, "旧密码不正确!");
                    return;
                } else {
                    //修改密码
                    boolean regFlag = WcpDataBase.execute("wcp", "UPDATE WCP_AUTH_USER SET PWD=? WHERE ACCOUNT=? AND PROJECT=?", new Object[]{AESUtil.encrypt(newPwd), realAccount, projectId});
                    result.put(HttpStatus.MSG, regFlag ? "注册成功" : "注册失败");
                    return;
                }
            }
            result.put(HttpStatus.MSG, "用户不存在!");
        } else {
            result.put(HttpStatus.MSG, "参数不能为空!");
        }
    }

    /**
     * 获取验证码
     *
     * @param result
     * @param object
     */
    @ApiService(value = "验证码", serviceKey = "captchaImage", signType = 0, notes = "验证码")
    public void captchaImage(Map<String, Object> result, JSONObject object) {
        //获取base64验证码  内容是随机的
        ValidateCodeUtil.Validate randomCode = ValidateCodeUtil.getRandomCode();
        String uuid = StringUtil.generateUUID();
        //base64字符串
        String base64Str = randomCode.getBase64Str();
        //验证码内容
        String value = randomCode.getValue();
        //将验证码存入到缓存中
        redisCache.setCacheObject(uuid, value, 1, TimeUnit.MINUTES);
        result.put("baseImg", "data:image/jpeg;base64," + base64Str);
        result.put("uuid", uuid);
    }

    @ApiService(value = "查询当前登录用户", serviceKey = "queryMe", signType = 2)
    public void queryMe(Map<String, Object> result, JSONObject object) {
        String userId = WcpThreadLocal.getUserId();
        String userAccount = WcpThreadLocal.getUserAccount();
        String userName = WcpThreadLocal.getUserName();

        if (userId == null || userAccount == null || userName == null) {
            result.put(HttpStatus.MSG, "登录用户异常");
            return;
        }
        if (userId.isEmpty() || userAccount.isEmpty() || userName.isEmpty()) {
            result.put(HttpStatus.MSG, "未找到登录用户信息");
            return;
        }
        Map<String, String> entry = new HashMap<>();
        entry.put("userId", userId);
        entry.put("userAccount", userAccount);
        entry.put("userName", userName);
        result.put("user", entry);
    }



}
