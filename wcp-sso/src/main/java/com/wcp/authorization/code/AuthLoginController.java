package com.wcp.authorization.code;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.execption.ServiceException;
import com.wcp.redis.CacheMemory;
import com.wcp.redis.MemoryService;
import com.wcp.thread.WcpThreadLocal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

/**
 * 1. @Description TODO auth单点登录授权码模式
 * 2. <AUTHOR>
 * 3. @Date 2025/5/6 8:53
 */
@ApiGroup(value = "auth单点登录", serviceKey = "auth/v1", classify = "auth/v1", classifyName = "auth单点登录")

public class AuthLoginController {

    @Value("${spring.sso.oauth2.client-id:}")
    private String clientId;
    @Value("${spring.sso.oauth2.client-secret:}")
    private String clientSecret;
    @Value("${spring.sso.oauth2.authorization-grant-type:}")
    private String grantType;
    @Value("${spring.sso.oauth2.redirect-uri:}")
    private String redirectUri;
    @Value("${spring.sso.oauth2.token-uri:}")
    private String tokenUri;
    @Value("${spring.sso.oauth2.user-info-uri:}")
    private String userInfoUri;
    @ApiService(value = "获取token信息", serviceKey = "token", signType = 0, notes = "获取token信息")
    public void token(Map<String, Object> result, JSONObject object){
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", grantType);
        params.add("code", object.getString("code"));
        params.add("client_id", clientId);
        params.add("client_secret", clientSecret);
        params.add("redirect_uri", redirectUri);
        String projectId=object.getString("projectId");
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        String auth = Base64.getEncoder().encodeToString(( clientId+":"+clientSecret).getBytes(StandardCharsets.UTF_8));
        headers.set("Authorization", "Basic " + auth);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(tokenUri, request, Map.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            Map<String, Object> body = response.getBody();
            String accessToken = (String) body.get("access_token");
            String refresh_token = (String) body.get("refresh_token");
            result.put("token",accessToken);
            HttpHeaders userinfoHeaders = new HttpHeaders();
            userinfoHeaders.set("Authorization", "Bearer " + accessToken);
            HttpEntity<?> userinfoRequest = new HttpEntity<>(userinfoHeaders);
            ResponseEntity<Map> userinfoResponse = restTemplate.exchange(userInfoUri, HttpMethod.GET, userinfoRequest, Map.class);
            CacheMemory.setValue(projectId,"wcp",accessToken,userinfoResponse.getBody());
            System.out.println(userinfoResponse.getBody());
        }
        else {
            throw new ServiceException("获取token失败");
        }
    }
}
