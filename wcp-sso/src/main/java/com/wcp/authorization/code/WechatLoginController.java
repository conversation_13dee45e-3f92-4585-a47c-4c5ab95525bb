package com.wcp.authorization.code;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 微信登录
 *
 * <AUTHOR> & 2025/7/21 14:12
 */
@Slf4j
@ApiGroup(value = "微信登录", serviceKey = "wechat/auth/v1", classify = "wechat/auth/v1", classifyName = "微信扫码登录")
public class WechatLoginController {
    /**
     * 获取二维码
     *
     * @param result 返回结果
     * @param object 请求参数
     */
    @ApiService(value = "获取二维码", serviceKey = "qrcode", signType = 0, notes = "获取二维码")
    public void getQrCode(Map<String, Object> result, JSONObject object) {

        result.put("openid", object.getString("openid"));
    }

}
