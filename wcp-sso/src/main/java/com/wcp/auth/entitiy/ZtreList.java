package com.wcp.auth.entitiy;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 1. @Description 菜单树
 * 2. <AUTHOR>
 * 3. @Date 2024/8/14 11:07
 */

public class ZtreList {

    public static List<Map<String, Object>> buildMenuTree(List<Map<String, Object>> menuList) {
        Map<String, List<Map<String, Object>>> childrenMap = new HashMap<>();
        for (Map<String, Object> menu : menuList) {
            String parentId = (String) menu.get("parentId");

            if (!childrenMap.containsKey(parentId)) {
                childrenMap.put(parentId, new ArrayList<>());
            }
            childrenMap.get(parentId).add(menu);
        }

        return menuList.stream()
                .filter(menu -> "0".equals((String)menu.get("parentId")))
                .peek(menu -> menu.put("children", getChildren(menu, childrenMap)))
                .collect(Collectors.toList());
    }

    private static List<Map<String, Object>> getChildren(Map<String, Object> menu, Map<String, List<Map<String, Object>>> childrenMap) {
        String id = (String) menu.get("id");
        if (childrenMap.containsKey(id)) {
            return childrenMap.get(id).stream()
                    .peek(subMenu -> subMenu.put("children", getChildren(subMenu, childrenMap)))
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }
}
