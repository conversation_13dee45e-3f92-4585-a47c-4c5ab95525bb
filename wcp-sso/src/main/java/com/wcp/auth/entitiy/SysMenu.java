package com.wcp.auth.entitiy;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2024/8/19 11:58
 */
@Data
public class SysMenu {
    /** 菜单ID */
    private String menuId;

    /** 菜单名称 */
    private String menuName;

    /** 父菜单名称 */
    private String parentName;

    /** 父菜单ID */
    private String parentId;

    /** 显示顺序 */
    private Integer orderNum;

    /** 路由地址 */
    private String path;

    /** 组件路径 */
    private String component;

    /** 路由参数 */
    private String query;

    /** 是否为外链（0是 1否） */
    private String isFrame;
    /** 类型（M目录 C菜单 F按钮） */
    private String menuType;

    /** 显示状态（0显示 1隐藏） */
    private String visible="0";
    /** 权限字符串 */
    private String perms;

    /** 菜单图标 */
    private String icon;
    /** 是否缓存（0缓存 1不缓存） */
    private String isCache="1";
    /** 子菜单 */
    private List<SysMenu> children = new ArrayList<SysMenu>();
}
