package com.wcp.auth.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.annotation.ApiTransactional;
import com.wcp.auth.entitiy.ZtreList;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.DateUtil;
import com.wcp.utils.StringUtil;

import java.util.*;

/**
 * 1. @Description 角色模块
 * 2. <AUTHOR>
 * 3. @Date 2024/8/13 16:28
 */
@ApiGroup(value = "角色模块", serviceKey = "auth/role", classify = "auth/role", classifyName = "角色模块")
public class RoleService {

    /**
     * 添加角色
     *
     * @param result
     * @param object
     */
    @ApiService(value = "添加角色", serviceKey = "addRole", signType = 2)
    @ApiTransactional
    public void addRole(Map<String, Object> result, JSONObject object) {
        String roleKey = object.getString("roleKey"); //角色标识
        String roleId = StringUtil.generateUUID(); //角色ID
        String groupId= WcpThreadLocal.getProjectId();//object.getString("groupId");
        String roleName = object.getString("roleName"); //角色名称
        String remark = object.getString("remark");   //备注
        JSONArray menuIds = object.getJSONArray("menuIds");
        if (StringUtil.isNullOrEmpty(roleKey)) {
            throw new ServiceException("角色标识不能为空", HttpStatus.NO_CONTENT);
        }
        if (StringUtil.isNullOrEmpty(roleName)) {
            throw new ServiceException("角色名称不能为空", HttpStatus.NO_CONTENT);
        }
        //添加前先查询数据库是否有需要添加的角色
        String sql = "SELECT ROLE_KEY FROM WCP_AUTH_ROLE WHERE ROLE_KEY = ? AND GROUP_ID=?";
        Map<String, Object> map = WcpDataBase.querySingleMap("wcp", sql, new Object[]{roleKey,groupId}, null);
        if (map != null && map.size() > 0) {
            throw new ServiceException("角色标识不能重复添加", HttpStatus.NO_CONTENT);
        }
        //数据库操作
        WcpDataBase.execute("wcp", "INSERT INTO WCP_AUTH_ROLE (ROLE_ID,ROLE_KEY,ROLE_NAME,REMARK,GROUP_ID) VALUES (?,?,?,?,?)", new Object[]{roleId,roleKey, roleName, remark, groupId});
        //添加数据至角色菜单关联表
        if (menuIds != null && menuIds.size()>0) {
            List<Object> listMap = new ArrayList<>();
            for (int i = 0; i < menuIds.size(); i++) {
                Object[] objectMap = new Object[3];
                String menuId = (String) menuIds.get(i);
                objectMap[0] = roleId;
                objectMap[1] = menuId;
                objectMap[2] = groupId;
                listMap.add(objectMap);
            }
            WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_ROLE_MENU (ROLE_ID,MENU_ID,GROUP_ID) VALUES (?,?,?)", listMap);
        }
        result.put(HttpStatus.MSG, "添加成功");
    }


    /**
     * 删除角色
     *
     * @param result
     * @param object
     */
    @ApiService(value = "删除角色", serviceKey = "deleteRole", signType = 2)
    @ApiTransactional
    public void deleteRole(Map<String, Object> result, JSONObject object) {
        String roleId = object.getString("roleId"); //角色标识
        if (StringUtil.isNullOrEmpty(roleId)) {
            throw new ServiceException("请选择需要删除的角色", HttpStatus.NO_CONTENT);
        }
        String groupId= WcpThreadLocal.getProjectId();
        //删除角色后还需删除角色菜单关联表信息
        WcpDataBase.execute("wcp", "DELETE FROM WCP_AUTH_ROLE WHERE ROLE_ID = ? AND GROUP_ID=?", new Object[]{roleId,groupId});
        WcpDataBase.execute("wcp", "DELETE FROM WCP_AUTH_ROLE_MENU WHERE ROLE_ID = ? AND GROUP_ID=?", new Object[]{roleId,groupId});
        result.put(HttpStatus.MSG, "删除成功");
    }


    /**
     * 修改角色
     *
     * @param result
     * @param object
     */
    @ApiService(value = "修改角色", serviceKey = "updateRole", signType = 2)
    @ApiTransactional
    public void updateRole(Map<String, Object> result, JSONObject object) {
        String roleId = object.getString("roleId"); //角色标识
        String roleName = object.getString("roleName"); //角色名称
        String groupId =WcpThreadLocal.getProjectId(); //object.getString("groupId");   //权限组标识
        String remark = object.getString("remark");   //备注
        JSONArray menuIds = object.getJSONArray("menuIds");
        if (StringUtil.isNullOrEmpty(roleId)) {
            throw new ServiceException("角色标识不能为空", HttpStatus.NO_CONTENT);
        }
        if (StringUtil.isNullOrEmpty(roleName)) {
            throw new ServiceException("角色名称不能为空", HttpStatus.NO_CONTENT);
        }
        //修改角色表数据信息
        WcpDataBase.execute("wcp", "UPDATE WCP_AUTH_ROLE SET ROLE_NAME = ?, REMARK = ? WHERE ROLE_ID = ? AND GROUP_ID=?", new Object[]{roleName, remark, roleId, groupId});
        //先删除角色和菜单关联表信息
        WcpDataBase.execute("wcp", "DELETE FROM WCP_AUTH_ROLE_MENU WHERE ROLE_ID = ? AND  GROUP_ID=?", new Object[]{roleId,groupId});

        //然后重新添加关联关系到角色菜单表中
        if (menuIds != null) {
            List<Object> listMap = new ArrayList<>();
            for (int i = 0; i < menuIds.size(); i++) {
                Object[] objectMap = new Object[3];
                String menuId = (String) menuIds.get(i);
                objectMap[0] = roleId;
                objectMap[1] = menuId;
                objectMap[2] = groupId;
                listMap.add(objectMap);
            }
            WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_ROLE_MENU (ROLE_ID,MENU_ID,GROUP_ID) VALUES (?,?,?)", listMap);
        }
        result.put(HttpStatus.MSG, "修改成功");
    }


    /**
     * 查询修改弹窗角色信息
     *
     * @param result
     * @param object
     */
    @ApiService(value = "查询修改弹窗角色信息", serviceKey = "queryToUpdateRole", signType = 2)
    public void queryToUpdateRole(Map<String, Object> result, JSONObject object) {
        String roleKey = object.getString("roleKey"); //角色标识
        String groupId =WcpThreadLocal.getProjectId();
        if (StringUtil.isNullOrEmpty(roleKey)) {
            throw new ServiceException("角色标识不能为空", HttpStatus.NO_CONTENT);
        }

        String sql = "SELECT ROLE_ID,ROLE_NAME,REMARK,GROUP_ID FROM WCP_AUTH_ROLE WHERE ROLE_ID = ? AND GROUP_ID=?";
        //数据库操作
        List<Map<String, Object>> objectList = WcpDataBase.queryMap("wcp", sql, new Object[]{roleKey,groupId}, "roleKey-ROLE_ID;roleName-ROLE_NAME;remark-REMARK;groupKey-GROUP_ID");
        result.put(HttpStatus.DATA, objectList);
        result.put(HttpStatus.MSG, "查询成功");
    }


    /**
     * 查询修改弹窗菜单信息
     *
     * @param result
     * @param object
     */
    @ApiService(value = "查询修改弹窗菜单信息", serviceKey = "queryToUpdateMenu", signType = 2)
    public void queryToUpdateGroup(Map<String, Object> result, JSONObject object) {
        Map<String, Object> resultMap = new HashMap<>();
        String roleId = object.getString("roleId"); //角色标识
        if (StringUtil.isNullOrEmpty(roleId)) {
            throw new ServiceException("角色ID不能为空", HttpStatus.NO_CONTENT);
        }
        String groupId =WcpThreadLocal.getProjectId();
        //角色详细信息
        String roleSql="select ROLE_ID,ROLE_KEY,ROLE_NAME,REMARK,GROUP_ID from WCP_AUTH_ROLE WHERE ROLE_ID=?  AND GROUP_ID=?";
        Map<String, Object> role=WcpDataBase.querySingleMap("wcp",roleSql,new Object[]{roleId,groupId},"roleId-ROLE_ID;roleKey-ROLE_KEY;roleName-ROLE_NAME;remark-REMARK;groupId-GROUP_ID");
        resultMap.put("role",role);
        //结果返回
        result.put(HttpStatus.DATA, resultMap);
        result.put(HttpStatus.MSG, "查询成功");
    }


    /**
     * 查询角色
     *
     * @param result
     * @param object
     */
    @ApiService(value = "查询角色", serviceKey = "queryRole", signType = 2)
    public void queryRole(Map<String, Object> result, JSONObject object) {
        String roleKey = object.getString("roleKey"); //角色标识
        String roleName = object.getString("roleName"); //角色名称
        String groupId = WcpThreadLocal.getProjectId();   //权限组标识
      /*  String btime = object.getString("btime");   //创建时间 开始时间
        String etime = object.getString("etime");   //创建时间 结束时间*/
        Integer pageNum = object.getInteger("pageNum"); //页数
        Integer pageSize = object.getInteger("pageSize");   //每页条数

        if (pageNum == null || pageNum <= 0) pageNum = 1; //默认为1页
        if (pageSize == null || pageSize <= 0) pageSize = 10; // 默认每页10条

        List<Object> params = new ArrayList<>();
        params.add(groupId);
        String sqls="SELECT A.ROLE_NAME, A.ROLE_ID,A.ROLE_KEY, A.CREATE_TIME,A.GROUP_ID,B.GROUP_NAME FROM WCP_AUTH_ROLE A  " +
                "LEFT JOIN WCP_AUTH_GROUP B ON A.GROUP_ID=B.GROUP_ID  " +
                " WHERE A.GROUP_ID=?  ";
        StringBuilder sql = new StringBuilder(sqls);

        if (roleName != null && !roleName.isEmpty()) {
            sql.append(" AND A.ROLE_NAME LIKE ?");
            params.add("%" + roleName+ "%");
        }


        //数据库操作
        Map<String, Object> objectMap = WcpDataBase.queryMapByPaging("wcp", sql.toString(), params.toArray(), "roleId-ROLE_ID;roleName-ROLE_NAME;groupKey-GROUP_ID;createTime-CREATE_TIME;roleKey-ROLE_KEY;groupName-GROUP_NAME", pageNum, pageSize, null);
        if (objectMap.get("pageData") != null) {
            List list = (List) objectMap.get("pageData");
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map = (Map<String, Object>) list.get(i);
                    map.put("createTime", DateUtil.calendarToString((Calendar) map.get("createTime")));
                    String roleKeys = map.get("roleKey").toString();
                    //判断当前角色是否是管理员角色，如果是添加不可删除状态标识
                    if (roleKeys.equals("admin")) {
                        map.put("admin", true);
                    } else {
                        map.put("admin", false);
                    }
                }
            }
        }
        result.put(HttpStatus.DATA, objectMap);
        result.put(HttpStatus.MSG, "查询成功");
    }


    /**
     * 获取权限组下拉框信息
     *
     * @param result
     * @param object
     */
    @ApiService(value = "获取权限组下拉框信息", serviceKey = "queryRoleGroup", signType = 2)
    public void queryRoleGroup(Map<String, Object> result, JSONObject object) {
        String sql = "SELECT GROUP_ID,GROUP_NAME FROM WCP_AUTH_GROUP";
        //数据库操作
        List<Map<String, Object>> objectList = WcpDataBase.queryMap("wcp", sql, new Object[]{}, "groupId-GROUP_ID;groupName-GROUP_NAME");
        result.put(HttpStatus.DATA, objectList);
        result.put(HttpStatus.MSG, "查询成功");
    }
    @ApiService(value = "查询角色绑定的菜单", serviceKey = "roleMenuTreeselect", signType = 2)
    public void  queryRoleMenu(Map<String, Object> result, JSONObject object){
        String roleId=object.getString("roleId");
        String groupId = WcpThreadLocal.getProjectId();
        String menuSql="select MENU_ID,PARENT_ID,MENU_NAME from WCP_AUTH_MENU WHERE GROUP_ID=?";
        List<Map<String,Object>> list=WcpDataBase.queryMap("wcp",menuSql,new Object[]{groupId},"id-MENU_ID;parentId-PARENT_ID;label-MENU_NAME");
        List<Map<String, Object>> treeList=ZtreList.buildMenuTree(list);
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("menus", treeList);
        String roleMenuSql="SELECT MENU_ID   FROM WCP_AUTH_ROLE_MENU WHERE ROLE_ID=? AND  GROUP_ID=?";
        List<Map<String,Object>> roleMenus=WcpDataBase.queryMap("wcp",roleMenuSql,new Object[]{roleId,groupId},"menuId-MENU_ID");
        List<String> roleMenuIds=new ArrayList<>();
        if(roleMenus!=null && roleMenus.size()>0){
            roleMenus.forEach(x->{
                roleMenuIds.add((String)x.get("menuId"));
            });
        }
        jsonObject.put("checkedKeys", roleMenuIds);
        result.put(HttpStatus.DATA, jsonObject);
    }
}
