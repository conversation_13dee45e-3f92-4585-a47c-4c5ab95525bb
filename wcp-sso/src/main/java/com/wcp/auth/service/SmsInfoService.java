package com.wcp.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.exceptions.ClientException;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.sms.ShortMessageService;
import com.wcp.thread.WcpThreadLocal;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/7/17 16:48
 */
@ApiGroup(value = "业务系统短信服务", serviceKey = "sms", classify = "sms", classifyName = "业务系统短信服务")
public class SmsInfoService {
    @Resource
    private ShortMessageService shortMessageService;

    /**
     * 注册用户验证码
     * @param result
     * @param object
     */
    @ApiService(value = "注册用户验证码", serviceKey = "send-reg-code", signType = 0, notes = "注册用户验证码")
    public void senSmsByRegister(Map<String, Object> result, JSONObject object){
        try {
            String phone=object.getString("phone");
            String projectId= WcpThreadLocal.getProjectId();
            Boolean flag=shortMessageService.senSmsByRegister(phone,projectId);
            result.put("data",flag?"发送成功":"发送失败");
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 找回密码
     * @return
     */
    @ApiService(value = "用户找回密码", serviceKey = "send-pwd-code", signType = 0, notes = "用户找回密码")
    public void senSmsByRetrievePwd(Map<String, Object> result, JSONObject object){
        try {
            String phone=object.getString("phone");
            String projectId= WcpThreadLocal.getProjectId();
            Boolean flag=shortMessageService.senSmsByRetrievePwd(phone,projectId);
            result.put("data",flag?"发送成功":"发送失败");
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 用户登录发送验证码
     * @param result
     * @param object
     */
    @ApiService(value = "用户登录验证码", serviceKey = "send-login-code", signType = 0, notes = "用户登录验证码")
    public void senSmsByLogin(Map<String, Object> result, JSONObject object){
        try {
            String phone=object.getString("phone");
            String projectId= WcpThreadLocal.getProjectId();
            Boolean flag=shortMessageService.senSmsBylogin(phone,projectId);
            result.put("data",flag?"发送成功":"发送失败");
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }
}
