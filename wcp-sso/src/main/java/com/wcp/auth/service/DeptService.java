package com.wcp.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description 部门组织机构
 * 2. <AUTHOR>
 * 3. @Date 2024/8/14 9:04
 */
@ApiGroup(value = "部门模块", serviceKey = "auth/dept", classify = "auth/dept", classifyName = "角色模块")
public class DeptService {

    /**
     * dem列表
     * @param result
     * @param object
     */
    @ApiService(value = "部门列表",serviceKey = "list",signType = 2)
    public void list(Map<String, Object> result, JSONObject object) {
        String deptName=object.getString("deptName");
        String deptStatus=object.getString("deptStatus");
        String projectId=WcpThreadLocal.getProjectId();
        StringBuffer sql=new StringBuffer();
        sql.append("SELECT DEPT_ID,PARENT_ID,DEPT_NAME,ORDER_NUM,DEPT_STATUS,CREATE_TIME FROM WCP_AUTH_DEPT WHERE PROJECT_ID=?");
        List<Object> param=new ArrayList<>();
        param.add(projectId);
        if(StringUtil.isNotEmpty(deptName)){
            sql.append(" AND DEPT_NAME LIKE ?");
            param.add("%" + deptName + "%");
        }
        sql.append(" ORDER BY ORDER_NUM ASC");
        List<Map<String,Object>> list=WcpDataBase.queryMap("wcp",sql.toString(),param.toArray(),"deptId-DEPT_ID;parentId-PARENT_ID;deptName-DEPT_NAME;orderNum-ORDER_NUM;deptStatus-DEPT_STATUS;createTime-CREATE_TIME");
        result.put(HttpStatus.DATA, list);
        result.put(HttpStatus.MSG, "查询成功");
    }

    /**
     * 新增部门
     * @param result
     * @param object
     */
    @ApiService(value = "新增部门",serviceKey = "addDept",signType = 2)
    public void addDept(Map<String, Object> result, JSONObject object) {
        String sql="INSERT INTO WCP_AUTH_DEPT(DEPT_ID,PARENT_ID,DEPT_NAME,ORDER_NUM,DEPT_STATUS,CREATE_BY,UPDATE_BY,PROJECT_ID) VALUES(?,?,?,?,?,?,?,?)";
        String deptId=StringUtil.generateUUID();
        String parentId=object.getString("parentId");
        String deptName=object.getString("deptName");
        String orderNum=object.getString("orderNum");
        String account= WcpThreadLocal.getUserAccount();
        String projectId=WcpThreadLocal.getProjectId();
        boolean flag=WcpDataBase.execute("wcp",sql,new Object[]{deptId,parentId,deptName,orderNum,0,account,account,projectId});
        if(flag){
            result.put(HttpStatus.DATA, null);
            result.put(HttpStatus.MSG, "新增成功" );
        }else{
            throw new ServiceException("新增失败");
        }
    }

    /**
     * 查询详情
     * @param result
     * @param object
     */
    @ApiService(value = "查询部门",serviceKey = "queryDetail",signType = 2)
    public void queryDetail(Map<String, Object> result, JSONObject object) {
        String sql="SELECT DEPT_ID,PARENT_ID,DEPT_NAME,ORDER_NUM,DEPT_STATUS,CREATE_TIME FROM WCP_AUTH_DEPT WHERE DEPT_ID=? AND PROJECT_ID=?";
        String deptId=object.getString("deptId");
        String projectId=WcpThreadLocal.getProjectId();
        Map<String,Object> map=WcpDataBase.querySingleMap("wcp",sql,new Object[]{deptId,projectId},"deptId-DEPT_ID;parentId-PARENT_ID;deptName-DEPT_NAME;orderNum-ORDER_NUM;deptStatus-DEPT_STATUS;createTime-CREATE_TIME");
        result.put(HttpStatus.DATA, map);
        result.put(HttpStatus.MSG, "查询成功");
    }

    @ApiService(value = "修改部门",serviceKey = "updateDept",signType = 2)
    public void updateDept(Map<String, Object> result, JSONObject object) {
        String sql="UPDATE WCP_AUTH_DEPT SET PARENT_ID=?,DEPT_NAME=?,ORDER_NUM=? WHERE DEPT_ID=? AND PROJECT_ID=?";
        String deptId=object.getString("deptId");
        String parentId=object.getString("parentId");
        String deptName=object.getString("deptName");
        String orderNum=object.getString("orderNum");
        String projectId=WcpThreadLocal.getProjectId();
        boolean flag=WcpDataBase.execute("wcp",sql,new Object[]{parentId,deptName,orderNum,deptId,projectId});
        if(flag){
            result.put(HttpStatus.DATA, null);
            result.put(HttpStatus.MSG, "新增成功" );
        }else{
            throw new ServiceException("新增失败");
        }
    }

    /**
     * 删除部门
     * @param result
     * @param object
     */
    @ApiService(value = "删除部门",serviceKey = "delDept",signType = 2)
    public void delDept(Map<String, Object> result, JSONObject object) {
        String sql="DELETE FROM WCP_AUTH_DEPT  WHERE DEPT_ID=? AND PROJECT_ID=?";
        String deptId=object.getString("deptId");
        String projectId=WcpThreadLocal.getProjectId();
        boolean flag=WcpDataBase.execute("wcp",sql,new Object[]{deptId,projectId});
        if(flag){
            result.put(HttpStatus.DATA, null);
            result.put(HttpStatus.MSG, "删除成功" );
        }else{
            throw new ServiceException("删除失败");
        }
    }
}
