package com.wcp.auth.service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.annotation.ApiTransactional;
import com.wcp.auth.entitiy.RouterVo;
import com.wcp.auth.entitiy.SysMenu;
import com.wcp.auth.util.TreeNodeUtil;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.redis.CacheMemory;
import com.wcp.redis.RedisCache;
import com.wcp.auth.entitiy.ZtreList;
import com.wcp.auth.util.ValidateCodeUtil;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.AESUtil;
import com.wcp.utils.DateUtil;
import com.wcp.utils.StringUtil;
import com.wcp.utils.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description: 用户相关业务处理
 * Author: qianchao
 * Date: 2024/2/19 11:27
 */
@ApiGroup(value = "自定义权限模块", serviceKey = "auth/user", classify = "auth/user", classifyName = "自定义权限模块")
@Slf4j
public class AuthUserService {
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private TokenUtil tokenUtil;
    @Value("${spring.sso.captcha}")
    private boolean captcha;

    @ApiService(value = "打印当前服务器时间", serviceKey = "logTime", signType = 0, notes = "打印当前服务器时间")
    public void logTime(Map<String, Object> result, JSONObject object) {
        result.put("time",System.currentTimeMillis());
    }

    /**
     * 登录密码带有时间戳
     * @param result
     * @param object
     */
    @ApiService(value = "用户登录", serviceKey = "login", signType = 0, notes = "用户登录", params = {
            @ApiParam(value = "用户名", name = "username", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "用户密码", name = "password", clazz = Object.class, paramType = "body", required = true)})
    public void login(Map<String, Object> result, JSONObject object) {
        String account = object.getString("account");
        String password = object.getString("password");
        String projectId = WcpThreadLocal.getProjectId();
        String uuid = object.getString("uuid");
        String captchaCode = object.getString("captchaCode");
        if (projectId == null) projectId = "default";
        //启用验证码
        if (captcha) {
            //获取缓存中的验证码
            String cacheCaptchaCode = redisCache.getCacheObject(uuid)==null?"":redisCache.getCacheObject(uuid);
            if (!captchaCode.toUpperCase().equals(cacheCaptchaCode.toUpperCase())) {
                throw new ServiceException("验证码错误!");
            }
        }
        if (StringUtil.isNotEmpty(account) || StringUtil.isNotEmpty(password)) {
            String dePassword = AESUtil.decrypt(password);
            String timestampStr = dePassword.substring(dePassword.length() - 13, dePassword.length());
            String realPassword = dePassword.replace(timestampStr, "");
            password =AESUtil.encrypt(realPassword);
            // 验证时间戳相差是否在20秒内
            long timestamp = Long.parseLong(timestampStr);
            Instant date = Instant.ofEpochMilli(timestamp);
            // 获取当前时间
            Instant now = Instant.now();
            // 计算两个时间点之间的时间差（以秒为单位）
            long secondsDifference = Duration.between(date, now).get(ChronoUnit.SECONDS);
            // 检查时间差是否超过20秒
            if (Math.abs(secondsDifference) >= 20) {
                throw new ServiceException("验证密码超时!");
            }

            Map<String, Object> userInfo = WcpDataBase.querySingleMap("wcp", "SELECT ID,ACCOUNT,USERNAME FROM WCP_AUTH_USER WHERE ACCOUNT=? AND PWD=? AND PROJECT=?", new Object[]{AESUtil.decrypt(account), AESUtil.encrypt(password), projectId}, null);
            if (userInfo != null) {
                getRequestWhiteList(account,projectId);
                String tokenData = tokenUtil.createToken(userInfo);
                result.put("token", tokenData);
                result.put(HttpStatus.MSG, "登录成功!");
                return;
            }else{
                throw new ServiceException("用户名或密码错误!");
            }
        }else{
            throw new ServiceException("用户名或账号不能为空!");
        }
    }

    /**
     * 登录密码没有时间戳
     * @param result
     * @param object
     */
    @ApiService(value = "用户登录", serviceKey = "classic_login", signType = 0, notes = "用户登录", params = {
            @ApiParam(value = "登录类型(1:账号密码登录 2:手机短信登录)", name = "loginType", clazz = Integer.class, paramType = "body", required = true),
            @ApiParam(value = "用户名", name = "account", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "手机号", name = "phone", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "用户密码(密码登录时必填)", name = "password", clazz = Object.class, paramType = "body", required = false),
            @ApiParam(value = "短信验证码(短信登录时必填)", name = "smsCode", clazz = String.class, paramType = "body", required = false),
            @ApiParam(value = "图形验证码", name = "captchaCode", clazz = String.class, paramType = "body", required = false),
            @ApiParam(value = "图形验证码UUID", name = "uuid", clazz = String.class, paramType = "body", required = false)})
    public void classicLogin(Map<String, Object> result, JSONObject object) {
        Integer loginType = object.getInteger("loginType");
        String account = object.getString("account");
        String phone = object.getString("phone");
        String projectId = WcpThreadLocal.getProjectId();
        // 验证图形验证码
        if (captcha) {
            String uuid = object.getString("uuid");
            String captchaCode = object.getString("captchaCode");
            String cacheCaptchaCode = redisCache.getCacheObject(uuid)==null?"":redisCache.getCacheObject(uuid);
            if (!captchaCode.toUpperCase().equals(cacheCaptchaCode.toUpperCase())) {
                throw new ServiceException("验证码错误!");
            }
        }
        Map<String, Object> userInfo = null;
        if (loginType == 1) {
            // 账号密码登录
            String password = object.getString("password");
            if (StringUtil.isEmpty(account) || StringUtil.isEmpty(password)) {
                throw new ServiceException("用户名和密码不能为空!");
            }
            userInfo = WcpDataBase.querySingleMap("wcp",
                    "SELECT ID,ACCOUNT,USERNAME FROM WCP_AUTH_USER WHERE ACCOUNT=? AND PWD=? AND PROJECT=?",
                    new Object[]{account, AESUtil.encrypt(password), projectId}, null);

            if (userInfo == null) {
                throw new ServiceException("用户名或密码错误!");
            }
        } else if (loginType == 2) {
            // 手机短信登录
            String smsCode = object.getString("smsCode");
            if (StringUtil.isEmpty(phone) || StringUtil.isEmpty(smsCode)) {
                throw new ServiceException("手机号和验证码不能为空!");
            }
            // 2. 验证码校验
            String smsKey = CacheMemory.getNestKey("wcp", "aliyun", "sms-login", phone);
            String cacheCode = CacheMemory.getValue(projectId, "wcp", String.class, smsKey);
            if (!smsCode.equals(cacheCode)) {
                throw new ServiceException("短信验证码错误!");
            }
            userInfo = WcpDataBase.querySingleMap("wcp",
                    "SELECT ID,ACCOUNT,USERNAME FROM WCP_AUTH_USER WHERE TELE_PHONE=? AND PROJECT=?",
                    new Object[]{phone, projectId}, null);

            if (userInfo == null) {
                throw new ServiceException("手机号未注册!");
            }
        } else {
            throw new ServiceException("不支持的登录类型!");
        }
        // 登录成功处理
        getRequestWhiteList(account, projectId);
        String tokenData = tokenUtil.createToken(userInfo);
        result.put("token", tokenData);
        result.put(HttpStatus.MSG, "登录成功!");
    }

    /**
     * 获取用户请求的白名单
     * @param account
     * @return
     */
    public void getRequestWhiteList(String account,String projectId) {
            String sql="SELECT A.ACCOUNT,R.GROUP_ID,T.FACES_URL FROM WCP_AUTH_USER A  " +
                    " JOIN WCP_AUTH_USER_ROLE B ON A.ID=B.USER_ID " +
                    " JOIN WCP_AUTH_ROLE R on B.ROLE_ID=R.ROLE_ID  AND B.GROUP_ID=R.GROUP_ID  "+
                    " JOIN WCP_AUTH_ROLE_MENU C ON B.ROLE_ID=C.ROLE_ID  AND B.GROUP_ID=C.GROUP_ID  " +
                    " JOIN WCP_AUTH_MENU_FACES D ON C.MENU_ID=D.MENU_ID AND D.GROUP_ID=C.GROUP_ID  " +
                    " JOIN WCP_AUTH_INTERFACES T ON D.FACES_ID=T.FACES_ID  AND T.GROUP_ID=D.GROUP_ID  " +
                    " WHERE A.ACCOUNT=?   AND T.FACES_TYPE='C' GROUP BY A.ACCOUNT,R.GROUP_ID,T.FACES_URL  ";
        //String deaccount=AESUtil.decrypt(account);
        List<Map<String,Object>> list=WcpDataBase.queryMap("wcp",sql,new Object[]{account},"account-ACCOUNT;groupId-GROUP_ID;facesUrl-FACES_URL");
        if(list!=null && list.size()>0){
            Map<String, List<String>> result = list.stream()
                    .collect(Collectors.groupingBy(
                            map -> map.get("account") + "-" + map.get("groupId"), // 拼接的键
                            Collectors.mapping(
                                    map -> (String) map.get("facesUrl"), // 提取 facesUrl
                                    Collectors.toList() // 收集到列表中
                            )
                    ));
            // 打印结果
            result.forEach((key, value) -> {
                System.out.println(key+":"+value);
                CacheMemory.setValue(projectId,"wcp",key,value);
            });

        }


    }

    @ApiService(value = "用户注册", serviceKey = "register", signType = 0, notes = "用户注册", params = {
            @ApiParam(value = "用户名", name = "userName", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "用户账号", name = "account", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "账号密码", name = "pwd", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "确认密码", name = "confirmPwd", clazz = Object.class, paramType = "body", required = true)
    })
    public void register(Map<String, Object> result, JSONObject object) {
        String userName = object.getString("userName");
        String account = object.getString("account");
        String projectId = StringUtil.isNotEmpty(object.getString("projectId")) ? object.getString("projectId") : "default";
        String pwd = object.getString("pwd");//注册密码
        String password = object.getString("confirmPwd");//确认密码
        if (account == null || account.trim().equals("") || userName == null || "".equals(userName.trim())) {
            throw new ServiceException("用户信息不能为空!");
        } else if (password == null || password.trim().equals("")) {
            throw new ServiceException("密码不能为空!");
        } else if (!pwd.trim().equals(password)) {
            throw new ServiceException("两次密码不相等!");
        }
        String realAccount = AESUtil.decrypt(account);
        //判断用户账号是否已经注册
        Map<String, Object> userInfo = WcpDataBase.querySingleMap("wcp", "SELECT ACCOUNT,PWD FROM WCP_AUTH_USER WHERE ACCOUNT=?", new Object[]{realAccount}, null);
        if (userInfo != null && userInfo.size() > 0) {
            throw new ServiceException("该用户已存在!");
        } else {
            boolean regFlag = WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_USER(ACCOUNT,PWD,USERNAME,PROJECT) VALUES(?,?,?,?)", new Object[]{realAccount, AESUtil.encrypt(pwd), userName, projectId});
            if(regFlag){
                result.put(HttpStatus.MSG,"注册成功");
            }else{
                throw new ServiceException("注册失败!");
            }

        }
    }

    @ApiService(value = "用户注册", serviceKey = "register-with-phone", signType = 0, notes = "用户注册", params = {
            @ApiParam(value = "用户名", name = "userName", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "用户账号", name = "account", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "账号密码", name = "pwd", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "确认密码", name = "confirmPwd", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "手机号", name = "phone", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "验证码", name = "code", clazz = String.class, paramType = "body", required = true)
    })
    public void registerWithPhone(Map<String, Object> result, JSONObject object) {
        // 1. 参数获取与基本校验
        String userName = object.getString("userName");
        String account = object.getString("account");
        String projectId = WcpThreadLocal.getProjectId();
        String pwd = object.getString("pwd");
        String confirmPwd = object.getString("confirmPwd");
        String phone = object.getString("phone");
        String code = object.getString("code");

        // 2. 验证码校验
        String smsKey = CacheMemory.getNestKey("wcp", "aliyun", "sms-reg", phone);
        String cacheCode = CacheMemory.getValue(projectId, "wcp", String.class, smsKey);
        if (!code.equals(cacheCode)) {
            throw new ServiceException("验证码错误或已过期");
        }
        CacheMemory.deleteValue(projectId, "wcp", smsKey); // 验证后立即删除

        if (account == null || account.trim().equals("") || userName == null || "".equals(userName.trim())) {
            throw new ServiceException("用户信息不能为空!");
        } else if (pwd == null || pwd.trim().equals("")) {
            throw new ServiceException("密码不能为空!");
        } else if (!pwd.trim().equals(confirmPwd)) {
            throw new ServiceException("两次密码不相等!");
        }

        String encryptedPwd = AESUtil.encrypt(pwd);

        // 4. 检查手机号是否已存在
        Map<String, Object> existUser = WcpDataBase.querySingleMap("wcp",
                "SELECT ID, ACCOUNT, USERNAME, PWD, WECHAT_OPENID FROM WCP_AUTH_USER WHERE TELE_PHONE = ? AND PROJECT = ?",
                new Object[]{phone, projectId}, null);

        if (existUser != null) {
            // 5. 手机号已存在处理逻辑
            String existAccount = existUser.get("ACCOUNT").toString();
            String existUserId = existUser.get("ID").toString();

            // 5.1 检查是否是微信账号（有openid但无密码）
            boolean isWechatAccount = existUser.get("WECHAT_OPENID") != null
                    && (existUser.get("PWD") == null || "".equals(existUser.get("PWD").toString()));

            if (isWechatAccount) {
                // 微信账号合并：补充账号密码信息
                String updatedWechat="UPDATE WCP_AUTH_USER SET ACCOUNT = ?, PWD = ?, USERNAME = ? WHERE ID = ?";
                boolean updated=WcpDataBase.execute("wcp",updatedWechat,new Object[]{account, encryptedPwd, userName, existUserId});
                if (!updated) {
                    throw new ServiceException("微信账号合并失败");
                }
            } else {
                // 5.2 已经是完整账号
                throw new ServiceException("该手机号已被账号[" + existAccount + "]注册");
            }
        }
        // 6. 全新注册流程
        // 6.1 检查账号是否已存在
        Map<String, Object> accountCheck = WcpDataBase.querySingleMap("wcp",
                "SELECT ACCOUNT FROM WCP_AUTH_USER WHERE ACCOUNT = ?",
                new Object[]{account}, null);
        if (accountCheck != null) {
            throw new ServiceException("该用户账号已存在");
        }
        // 6.2 执行注册
        boolean regSuccess = WcpDataBase.insert("wcp",
                "INSERT INTO WCP_AUTH_USER(ID,ACCOUNT, PWD, USERNAME, TELE_PHONE, PROJECT) VALUES(?,?,?,?,?,?)",
                new Object[]{StringUtil.generateUUID(),account, encryptedPwd, userName, phone, projectId});

        if (regSuccess) {
            result.put(HttpStatus.MSG, "注册成功");
        } else {
            throw new ServiceException("注册失败，请稍后重试");
        }
    }

    /**
     * 修改密码
     *
     * @param result
     * @param object
     */
    @ApiService(value = "修改密码", serviceKey = "updatePassword", signType = 2, notes = "修改密码", params = {
            @ApiParam(value = "用户账号", name = "account", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "旧密码", name = "oldPwd", clazz = Object.class, paramType = "body", required = true),
            @ApiParam(value = "新密码", name = "newPwd", clazz = Object.class, paramType = "body", required = true)
    })
    public void updatePassword(Map<String, Object> result, JSONObject object) {
        String account = object.getString("account");
        String oldPwd = object.getString("oldPwd");
        String newPwd = object.getString("newPwd");
        String projectId = WcpThreadLocal.getProjectId();
        if (StringUtil.isNotEmpty(account) && StringUtil.isNotEmpty(oldPwd) && StringUtil.isNotEmpty(newPwd)) {
            String realAccount = AESUtil.decrypt(account);
            Map<String, Object> userInfo = WcpDataBase.querySingleMap("wcp", "SELECT ID,ACCOUNT,PWD FROM WCP_AUTH_USER WHERE ACCOUNT=?  AND PROJECT=?", new Object[]{realAccount, projectId}, null);
            if (userInfo != null) {
                if (!userInfo.get("PWD").equals(AESUtil.encrypt(oldPwd))) {
                    throw new ServiceException("旧密码不正确!");
                } else {
                    //修改密码
                    boolean regFlag = WcpDataBase.execute("wcp", "UPDATE WCP_AUTH_USER SET PWD=? WHERE ACCOUNT=? AND PROJECT=?", new Object[]{AESUtil.encrypt(newPwd), realAccount, projectId});
                    if(regFlag){
                        result.put(HttpStatus.MSG,"修改成功");
                    }else{
                        throw new ServiceException("修改失败!");
                    }
                }
            }
            throw new ServiceException("用户不存在!");
        } else {
            throw new ServiceException("参数不能为空!");
        }
    }

    /**
     * 获取验证码
     *
     * @param result
     * @param object
     */
    @ApiService(value = "验证码", serviceKey = "captchaImage", signType = 0, notes = "验证码")
    public void captchaImage(Map<String, Object> result, JSONObject object) {
        //获取base64验证码  内容是随机的
        ValidateCodeUtil.Validate randomCode = ValidateCodeUtil.getRandomCode();
        String uuid = StringUtil.generateUUID();
        //base64字符串
        String base64Str = randomCode.getBase64Str();
        //验证码内容
        String value = randomCode.getValue();
        //将验证码存入到缓存中
        redisCache.setCacheObject(uuid, value, 1, TimeUnit.MINUTES);
        result.put("baseImg", "data:image/jpeg;base64," + base64Str);
        result.put("uuid", uuid);
    }

    @ApiService(value = "查询当前登录用户", serviceKey = "queryMe", signType = 2)
    public void queryMe(Map<String, Object> result, JSONObject object) {
        String userId = WcpThreadLocal.getUserId();
        String userAccount = WcpThreadLocal.getUserAccount();
        String userName = WcpThreadLocal.getUserName();
        String projectId = WcpThreadLocal.getProjectId();
        if (userId == null || userAccount == null || userName == null) {
            throw new ServiceException("登录用户异常", HttpStatus.BAD_REQUEST);
        }
        if (userId.isEmpty() || userAccount.isEmpty() || userName.isEmpty()) {
            throw  new ServiceException("未找到登录用户信息", HttpStatus.BAD_REQUEST);
        }
        Map<String, Object> entry = new HashMap<>();
        entry.put("userId", userId);
        entry.put("userAccount", userAccount);
        entry.put("userName", userName);
        //admin是内置账号，不允许删除和修改
        if("admin".equals(userAccount)){
            List<String>roles=new ArrayList<>();
            roles.add("admin");
            result.put("roles",roles);
            List<String>permsList=new ArrayList<>();
            permsList.add("*:*:*");
            result.put("permissions",permsList);
            entry.put("admin",true);
        }else{
            String sql="SELECT D.MENU_NAME,D.PERMS,R.ROLE_KEY,A.INDEX_PATH FROM WCP_AUTH_USER A  " +
                    "LEFT JOIN WCP_AUTH_USER_ROLE B ON A.ID=B.USER_ID  " +
                    "LEFT JOIN WCP_AUTH_ROLE R ON B.ROLE_ID=R.ROLE_ID  AND B.GROUP_ID=R.GROUP_ID  "+
                    "LEFT JOIN WCP_AUTH_ROLE_MENU C ON B.ROLE_ID=C.ROLE_ID  AND C.GROUP_ID=B.GROUP_ID  " +
                    "LEFT JOIN WCP_AUTH_MENU D ON C.MENU_ID=D.MENU_ID  AND D.GROUP_ID=C.GROUP_ID  " +
                  //  "WHERE A.ID=? AND R.GROUP_ID=? AND D.MENU_TYPE='F' ";
                    "WHERE A.ID=?  AND D.MENU_TYPE='F' ";
            List<Map<String,Object>> mapList=WcpDataBase.queryMap("wcp", sql, new Object[]{userId}, "menuName-MENU_NAME;perms-PERMS;roleKey-ROLE_KEY;indexPath-INDEX_PATH");
            if(mapList!=null && mapList.size()>0){
                List<String> permsList = mapList.stream()
                        .map(map -> map.get("perms")) // 获取 perms 字段的值
                        .filter(value -> value != null) // 过滤掉 null 值
                        .map(Object::toString)
                        .distinct()// 转换为 String 类型（如果需要）
                        .collect(Collectors.toList()); // 收集到 List<String> 中
                List<String> roles = mapList.stream()
                        .map(map -> map.get("roleKey")) // 获取 roleKey 字段的值
                        .filter(value -> value != null) // 过滤掉 null 值
                        .map(Object::toString)
                        .distinct()// 转换为 String 类型（如果需要）
                        .collect(Collectors.toList()); // 收集到 List<String> 中
                result.put("roles",roles);
                result.put("permissions",permsList);
                result.put("indexPath",mapList.get(0).get("indexPath"));
            }
        }



        result.put(HttpStatus.DATA, entry);

    }

    //查询所有用户列表
    @ApiService(value = "用户管理-查询用户列表", serviceKey = "selectUserList", signType = 2)
    public void selectUserList(Map<String, Object> result, JSONObject object) {
        Map<String, Object> resultMap = new HashMap<>();
        String account = object.getString("account"); //用户名
        String userName = object.getString("userName"); //姓名
        String telePhone = object.getString("telePhone");   //电话
        String email = object.getString("email");   //邮箱
        String projectId = WcpThreadLocal.getProjectId();
        String deptId = object.getString("deptId");   //部门ID
        //页数
        int currentPage = object.getIntValue("pageNum");
        //条数
        int pageSize = object.getIntValue("pageSize");
        List<Object> params = new ArrayList<>();
        params.add("admin");
        params.add(projectId);
        StringBuffer sql =new StringBuffer();
        sql.append("SELECT A.ID,A.ACCOUNT,A.USERNAME,A.TELE_PHONE,A.CREATE_TIME,A.STATES,A.DEPT_ID,B.DEPT_NAME FROM  WCP_AUTH_USER A  JOIN WCP_AUTH_DEPT B ON A.DEPT_ID=B.DEPT_ID   WHERE STATES='1' AND ACCOUNT!=? AND PROJECT=?  ");
        if(StringUtil.isNotEmpty(account)){
            sql.append(" AND A.ACCOUNT like ?");
            params.add("%" + account + "%");
        }if(StringUtil.isNotEmpty(userName)){
            sql.append(" AND A.USERNAME like ?");
            params.add("%" + userName + "%");
        }if(StringUtil.isNotEmpty(telePhone)){
            sql.append(" AND A.TELE_PHONE like ?");
            params.add("%" + telePhone + "%");
        }
        if(deptId!=null){
            sql.append(" AND A.DEPT_ID =?");
            params.add(deptId);
        }
        Map<String, Object> objectMap = WcpDataBase.queryMapByPaging("wcp", sql.toString(), params.toArray(), "id-ID;account-ACCOUNT;userName-USERNAME;telePhone-TELE_PHONE;createTime-CREATE_TIME;states-STATES;deptId-DEPT_ID;deptName-DEPT_NAME", currentPage, pageSize, null);//获取用户总条数
        if (objectMap.get("pageData") != null) {
            List list = (List) objectMap.get("pageData");
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map = (Map<String, Object>) list.get(i);
                    map.put("createTime", DateUtil.calendarToString((Calendar) map.get("createTime")));
                }
            }
        }
        result.put(HttpStatus.DATA, objectMap);
        result.put(HttpStatus.MSG, "查询成功");
    }

    /**
     * 加载部门树
     * @param result
     * @param object
     */
    @ApiService(value = "加载部门树", serviceKey = "selectDeptTree", signType = 2)
    public static void selectDeptTree(Map<String, Object> result, JSONObject object) {
        String sql="SELECT DEPT_ID,PARENT_ID,DEPT_NAME FROM WCP_AUTH_DEPT WHERE DEPT_STATUS=0";
        List<Map<String,Object>> list=WcpDataBase.queryMap("wcp",sql,new Object[]{},"id-DEPT_ID;parentId-PARENT_ID;label-DEPT_NAME;");
        List<Map<String, Object>> treeList= ZtreList.buildMenuTree(list);
        result.put(HttpStatus.DATA, treeList);
        result.put(HttpStatus.MSG, "查询成功");
    }

    @ApiService(value = "查询用户详细信息", serviceKey = "queryUserDetail", signType = 2)
    public static void queryUserDetail(Map<String, Object> result, JSONObject object){
        String sql="SELECT ID,ACCOUNT,USERNAME,TELE_PHONE,CREATE_TIME,STATES,DEPT_ID FROM  WCP_AUTH_USER WHERE ID=?";
        String userId=object.getString("id");
        String projectId=WcpThreadLocal.getProjectId();
        Map<String,Object> objectMap=WcpDataBase.querySingleMap("wcp",sql,new Object[]{userId},"id-ID;account-ACCOUNT;userName-USERNAME;telePhone-TELE_PHONE;createTime-CREATE_TIME;states-STATES;deptId-DEPT_ID;");

        String roleSql="SELECT ROLE_ID,ROLE_NAME,ROLE_KEY,GROUP_ID FROM WCP_AUTH_ROLE WHERE GROUP_ID=?";
        List<Map<String, Object>> roleList=WcpDataBase.queryMap("wcp",roleSql,new Object[]{projectId},"roleId-ROLE_ID;roleName-ROLE_NAME;roleKey-ROLE_KEY;groupId-GROUP_ID;");

        String UserRoleSql="SELECT ROLE_ID FROM WCP_AUTH_USER_ROLE WHERE USER_ID=? AND GROUP_ID=?";
        List<Map<String, Object>> userRole=WcpDataBase.queryMap("wcp",UserRoleSql,new Object[]{userId,projectId},"roleId-ROLE_ID;");
        List<String> roleIds=new ArrayList<>();
        if(userRole!=null && userRole.size()>0){
            userRole.forEach(t->{
                roleIds.add((String)t.get("roleId"));
            });
        }
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("user",objectMap);
        jsonObject.put("roles",roleList);
        jsonObject.put("roleIds",roleIds);
        result.put(HttpStatus.DATA, jsonObject);
        result.put(HttpStatus.MSG, "查询成功");
    }
    @ApiService(value = "新增用户", serviceKey = "adduser", signType = 2)
    @ApiTransactional
    public void adduser(Map<String, Object> result, JSONObject object) {
        String userId = StringUtil.generateUUID();
        String userName = object.getString("userName");
        String account = object.getString("account");
        String telePhone = object.getString("telePhone");
        JSONArray roleIds = object.getJSONArray("roleIds");
        String deptId = object.getString("deptId");
        String groupId = WcpThreadLocal.getProjectId();
        String pwd = object.getString("password");//密码
        String projectId = WcpThreadLocal.getProjectId();
        //判断用户账号是否已经存在
        Map<String, Object> userInfo = WcpDataBase.querySingleMap("wcp", "SELECT ACCOUNT,PWD FROM WCP_AUTH_USER WHERE ACCOUNT=? AND PROJECT=?", new Object[]{account, projectId}, null);
        if (userInfo != null && userInfo.size() > 0) {
            throw new ServiceException("该用户已存在!");
        } else {
            boolean regFlag = WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_USER(ID,ACCOUNT,PWD,USERNAME,PROJECT,DEPT_ID,TELE_PHONE) VALUES(?,?,?,?,?,?,?)", new Object[]{userId, account, AESUtil.encrypt(pwd), userName, projectId, deptId, telePhone});
            if (regFlag) {
                if (roleIds != null) {
                    List<Object> listMap = new ArrayList<>();
                    for (int i = 0; i < roleIds.size(); i++) {
                        Object[] objectMap = new Object[3];
                        String roleId = (String) roleIds.get(i);
                        objectMap[0] = userId;
                        objectMap[1] = roleId;
                        objectMap[2] = groupId;
                        listMap.add(objectMap);
                    }
                    WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_USER_ROLE (USER_ID,ROLE_ID,GROUP_ID) VALUES (?,?,?)", listMap);
                }
                result.put(HttpStatus.MSG,"添加成功");
            }else{
                throw new ServiceException("添加失败");
            }
        }
    }

        @ApiService(value = "修改用户", serviceKey = "updateUser", signType = 2)
        @ApiTransactional
        public void updateUser (Map < String, Object > result, JSONObject object){
            String userId = object.getString("id");
            String userName = object.getString("userName");
            //String account = object.getString("account");
            String telePhone = object.getString("telePhone");
            JSONArray roleIds = object.getJSONArray("roleIds");
            String deptId = object.getString("deptId");
            String groupId = WcpThreadLocal.getProjectId();
            boolean upFlag=WcpDataBase.execute("wcp","UPDATE WCP_AUTH_USER SET USERNAME=?,TELE_PHONE=?,DEPT_ID=? WHERE ID=?",new Object[]{userName,telePhone,deptId,userId} );
            if (upFlag) {
                //删除原有的角色关联
                WcpDataBase.execute("wcp","DELETE FROM WCP_AUTH_USER_ROLE WHERE USER_ID=?",new Object[]{userId});
                if (roleIds != null) {
                    List<Object> listMap = new ArrayList<>();
                    for (int i = 0; i < roleIds.size(); i++) {
                        Object[] objectMap = new Object[3];
                        String roleId = (String) roleIds.get(i);
                        objectMap[0] = userId;
                        objectMap[1] = roleId;
                        objectMap[2] = groupId;
                        listMap.add(objectMap);
                    }
                    WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_USER_ROLE (USER_ID,ROLE_ID,GROUP_ID) VALUES (?,?,?)", listMap);
                }
                result.put(HttpStatus.MSG,"修改成功");
            }
           else{
                throw new ServiceException("修改失败!");
            }
        }
        @ApiService(value = "删除用户", serviceKey = "delUser", signType = 2)
        @ApiTransactional
        public void delUser(Map < String, Object > result, JSONObject object){
        String userId=object.getString("id");
        String groupId = WcpThreadLocal.getProjectId();
        //删除角色关联
         boolean res=WcpDataBase.execute("wcp","DELETE FROM WCP_AUTH_USER WHERE ID=?",new Object[]{userId});
        //删除用户
            if(res){
                boolean flag=WcpDataBase.execute("wcp","DELETE FROM WCP_AUTH_USER_ROLE WHERE USER_ID=? AND GROUP_ID=?",new Object[]{userId,groupId});
                result.put(HttpStatus.MSG,"删除成功");
            } else{
                throw new ServiceException("删除失败!");
            }
        }
        @ApiService(value = "获取菜单路由", serviceKey = "getRouters", signType = 2)
        public void getRouters(Map<String, Object> result, JSONObject object) {
            String userId = WcpThreadLocal.getUserId();
            String userAccount=WcpThreadLocal.getUserAccount();
            String projectId = WcpThreadLocal.getProjectId();
            if(StringUtil.isNotEmpty(userId)){
                StringBuffer sql=new StringBuffer();
                List<Object> params=new ArrayList<>();
                 sql.append("SELECT DISTINCT A.MENU_ID,A.MENU_NAME,A.PARENT_ID,A.ASSEMBLY_URL,A.ROUTE_URL,A.MENU_TYPE,A.PERMS,A.ICON,A.IS_FRAME,A.ORDER_NUM,A.IS_CACHE  FROM WCP_AUTH_MENU A  " +
                        "LEFT JOIN WCP_AUTH_ROLE_MENU B ON A.MENU_ID=B.MENU_ID  AND  A.GROUP_ID=B.GROUP_ID  " +
                        "LEFT JOIN WCP_AUTH_USER_ROLE C ON B.ROLE_ID=C.ROLE_ID  AND  B.GROUP_ID=C.GROUP_ID  " +
                         "LEFT JOIN WCP_AUTH_ROLE R ON C.ROLE_ID=R.ROLE_ID  AND  R.GROUP_ID=C.GROUP_ID  "+
                        "LEFT JOIN WCP_AUTH_USER D ON C.USER_ID=D.ID  " +
                        "WHERE  A.MENU_TYPE IN ('M','C')");
                 if(!"admin".equals(userAccount)){
                   //  sql.append("  AND D.ID=? AND R.GROUP_ID=?");
                     sql.append("  AND D.ID=?");
                     params.add(userId);
                     //params.add(projectId);
                 }
                 sql.append(" ORDER BY A.PARENT_ID, A.ORDER_NUM");
                List<SysMenu> mapList=WcpDataBase.query("wcp", sql.toString(), params.toArray(),
                        SysMenu.class,"menuId-MENU_ID;menuName-MENU_NAME;parentId-PARENT_ID;component-ASSEMBLY_URL;path-ROUTE_URL;menuType-MENU_TYPE;perms-PERMS;icon-ICON;isFrame-IS_FRAME;orderNum-ORDER_NUM;isCache-IS_CACHE");
                List<RouterVo> treeNode=null;
                if(mapList!=null && mapList.size()>0){
                    List<SysMenu> menus=TreeNodeUtil.getChildPerms(mapList,"0");
                    treeNode=TreeNodeUtil.buildMenus(menus);

               }else{
                    treeNode=new ArrayList<>();
                }
                result.put(HttpStatus.DATA,treeNode);
            }
    }

    @ApiService(value = "重置密码", serviceKey = "resetPwd", signType = 2)
    public void resetUserPwd(Map<String, Object> result, JSONObject object) {
        String userId = object.getString("userId");
        String password = object.getString("password");
        //修改密码
        boolean regFlag = WcpDataBase.execute("wcp", "UPDATE WCP_AUTH_USER SET PWD=? WHERE ID=? ", new Object[]{AESUtil.encrypt(password), userId});
        if(regFlag){
            result.put(HttpStatus.MSG, "修改成功" );
        }else{
            throw new ServiceException("修改失败");
        }
    }

    @ApiService(value = "获取用户", serviceKey = "getUserList", signType = 2)
    public void getUserList(Map<String, Object> result, JSONObject object) {
        //页数
        int currentPage = object.getIntValue("pageNum");
        //条数
        int pageSize = object.getIntValue("pageSize");
        Map<String, Object> objectMap = WcpDataBase.queryMapByPaging("wcp", "SELECT ACCOUNT,USERNAME FROM WCP_AUTH_USER WHERE STATES='1' ",null, "account-ACCOUNT;userName-USERNAME", currentPage, pageSize, null);//获取用户总条数
        result.put(HttpStatus.DATA, objectMap);
        result.put(HttpStatus.MSG, "查询成功");
    }
}
