package com.wcp.auth.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.auth.entitiy.ZtreList;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description 自定义权限菜单
 * 2. <AUTHOR>
 * 3. @Date 2024/8/14 15:36
 */
@ApiGroup(value = "自定义菜单", serviceKey = "auth/menu", classify = "auth/menu", classifyName = "自定义菜单")
public class MenuService {
    @ApiService(value = "新增菜单",serviceKey = "addMenu",signType = 2)
    public void addDept(Map<String, Object> result, JSONObject object) {
        String sql="INSERT INTO WCP_AUTH_MENU(MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,ASSEMBLY_URL,ROUTE_URL,MENU_TYPE,PERMS,ICON,CREATE_BY,UPDATE_BY,GROUP_ID,IS_CACHE,IS_FRAME) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,? )";
        String parentId=object.getString("parentId");
        String menuId=StringUtil.generateUUID();
        JSONArray facesIds = object.getJSONArray("facesIds");
        String orderNum=object.getString("orderNum");
        String icon=object.getString("icon");
        String menuName=object.getString("menuName");
        String menuType=object.getString("menuType");
        String routerUrl=object.getString("path");//路由地址
        String assemblyUrl=object.getString("component");//组件地址
        String perms=object.getString("perms");//接口地址
        int isCache=object.getInteger("isCache");//是否缓存
        int isFrame=object.getInteger("isFrame");//是否外链
        String account= WcpThreadLocal.getUserAccount();
        String groupId=WcpThreadLocal.getProjectId();
        //如果是按钮 就需要判断perms是否已经存在
        if (StringUtil.isNotEmpty(perms)) {
            List<Map<String, Object>> list = WcpDataBase.queryMap("wcp", "SELECT MENU_ID FROM WCP_AUTH_MENU WHERE PERMS=? AND GROUP_ID=?", new Object[]{perms,groupId}, "menuId-MENU_ID");
            if (list != null && list.size() > 0) {
               throw new ServiceException("权限标识已存在",HttpStatus.BAD_REQUEST);
            }
        }
        boolean flag= WcpDataBase.execute("wcp",sql,new Object[]{menuId,menuName,parentId,orderNum,assemblyUrl,routerUrl,menuType,perms,icon,account,account,groupId,isCache,isFrame});
        if(flag){
            if (facesIds != null) {
                List<Object> listMap = new ArrayList<>();
                for (int i = 0; i < facesIds.size(); i++) {
                    Object[] objectMap = new Object[2];
                    int facesId = (int) facesIds.get(i);
                    objectMap[0] = facesId;
                    objectMap[1] = menuId;
                    objectMap[2] = groupId;
                    listMap.add(objectMap);
                }
                WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_MENU_FACES (FACES_ID,MENU_ID,GROUP_ID) VALUES (?,?,?)", listMap);
            }
            result.put(HttpStatus.MSG,"新增成功");
        }else{
            throw new ServiceException("新增失败");
        }
    }
    @ApiService(value = "查询菜单",serviceKey = "list",signType = 2)
    public void list(Map<String, Object> result, JSONObject object) {
        String sql="SELECT MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,ASSEMBLY_URL,ROUTE_URL,MENU_TYPE,PERMS,ICON,CREATE_TIME,IS_CACHE,IS_FRAME FROM WCP_AUTH_MENU WHERE GROUP_ID = ?";
        String menuName=object.getString("menuName");
        String groupId=WcpThreadLocal.getProjectId();
        List<Object> params=new ArrayList<>();
        params.add(groupId);

        if(StringUtil.isNotEmpty(menuName)){
            sql+=" AND MENU_NAME  LIKE ?";
            params.add("%" + menuName+ "%");
        }
        sql+=" ORDER BY ORDER_NUM ASC";
        List<Map<String,Object>> mapList=WcpDataBase.queryMap("wcp",sql,params.toArray(),"menuId-MENU_ID;menuName-MENU_NAME;parentId-PARENT_ID;orderNum-ORDER_NUM;component-ASSEMBLY_URL;path-ROUTE_URL;menuType-MENU_TYPE;perms-PERMS;icon-ICON;createTime-CREATE_TIME;isCache-IS_CACHE;is_Frame-IS_FRAME");
       // List<Map<String, Object>> treeList= ZtreList.buildMenuTree(mapList);
        result.put(HttpStatus.DATA, mapList);
        result.put(HttpStatus.MSG, "查询成功");
    }


    @ApiService(value = "查询菜单详情",serviceKey = "queryMenuDetail",signType = 2)
    public void queryDetail(Map<String, Object> result, JSONObject object) {
        String sql="SELECT MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,ASSEMBLY_URL,ROUTE_URL,MENU_TYPE,PERMS,ICON,CREATE_TIME,IS_CACHE,IS_FRAME FROM WCP_AUTH_MENU WHERE MENU_ID=? AND GROUP_ID = ?";
        String menuId=object.getString("menuId");
        String groupId=WcpThreadLocal.getProjectId();
        Map<String,Object> map=WcpDataBase.querySingleMap("wcp",sql,new Object[]{menuId,groupId},"menuId-MENU_ID;menuName-MENU_NAME;parentId-PARENT_ID;orderNum-ORDER_NUM;component-ASSEMBLY_URL;path-ROUTE_URL;menuType-MENU_TYPE;perms-PERMS;icon-ICON;createTime-CREATE_TIME;isCache-IS_CACHE;isFrame-IS_FRAME");
        List<Map<String,Object>> list=WcpDataBase.queryMap("wcp","SELECT FACES_ID FROM WCP_AUTH_MENU_FACES WHERE MENU_ID=?  AND GROUP_ID = ?",new Object[]{menuId,groupId},"facesId-FACES_ID");
        List<String> keys=new ArrayList<>();
        if(list!=null && list.size()>0){
            list.forEach(x->{
                keys.add((String)x.get("facesId"));
            });
        }
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("menu",map);
        jsonObject.put("keys",keys);
        result.put(HttpStatus.DATA, jsonObject);
        result.put(HttpStatus.MSG, "查询成功");
    }

    @ApiService(value = "修改菜单",serviceKey = "updateMenu",signType = 2)
    public void updateMenu(Map<String, Object> result, JSONObject object) {
        String sql="UPDATE WCP_AUTH_MENU SET MENU_NAME=?,PARENT_ID=?,ORDER_NUM=?,ASSEMBLY_URL=?,ROUTE_URL=?,MENU_TYPE=?,PERMS=?,ICON=?,UPDATE_BY=?,IS_CACHE=?,IS_FRAME=?  WHERE MENU_ID=? AND GROUP_ID = ?";
        String groupId=WcpThreadLocal.getProjectId();
        String parentId=object.getString("parentId");
        String orderNum=object.getString("orderNum");
        JSONArray facesIds = object.getJSONArray("facesIds");
        String icon=object.getString("icon");
        String menuName=object.getString("menuName");
        String menuType=object.getString("menuType");
        String routerUrl=object.getString("path");//路由地址
        String assemblyUrl=object.getString("component");//组件地址
        String perms=object.getString("perms");//接口地址
        String menuId=object.getString("menuId");
        String tempCache=object.getString("isCache");
        int isCache=tempCache!=null?Integer.parseInt(tempCache):1;//是否缓存
        int isFrame=object.getInteger("isFrame");//是否外链
        String account= WcpThreadLocal.getUserAccount();
        //如果是按钮 就需要判断perms是否已经存在
        if (StringUtil.isNotEmpty(perms)) {
            List<Map<String, Object>> list = WcpDataBase.queryMap("wcp", "SELECT MENU_ID FROM WCP_AUTH_MENU WHERE PERMS=? AND MENU_ID!=?", new Object[]{perms,menuId}, "menuId-MENU_ID");
            if (list != null && list.size() > 0) {
                throw new ServiceException("权限标识已存在",HttpStatus.BAD_REQUEST);
            }
        }
        boolean flag= WcpDataBase.execute("wcp",sql,new Object[]{menuName,parentId,orderNum,assemblyUrl,routerUrl,menuType,perms,icon,account,isCache,isFrame,menuId,groupId});
        if(flag){
            WcpDataBase.execute("wcp","DELETE FROM WCP_AUTH_MENU_FACES WHERE MENU_ID=? AND GROUP_ID = ?",new Object[]{menuId,groupId});
            if (facesIds != null) {
                List<Object> listMap = new ArrayList<>();
                for (int i = 0; i < facesIds.size(); i++) {
                    Object[] objectMap = new Object[3];
                    String facesId = (String) facesIds.get(i);
                    objectMap[0] = facesId;
                    objectMap[1] = menuId;
                    objectMap[2] = groupId;
                    listMap.add(objectMap);
                }
                WcpDataBase.insert("wcp", "INSERT INTO WCP_AUTH_MENU_FACES (FACES_ID,MENU_ID,GROUP_ID) VALUES (?,?,?)", listMap);
            }
            result.put(HttpStatus.DATA, null);
            result.put(HttpStatus.MSG,"修改成功");
        }else{
            throw new ServiceException("修改失败");
        }

    }

    @ApiService(value = "删除菜单",serviceKey = "delMenu",signType = 2)
    public void delMenu(Map<String, Object> result, JSONObject object) {
        String menuId=object.getString("menuId");
        String groupId=WcpThreadLocal.getProjectId();
        //先判断当前菜单下面有没有子集，有子集不允许删除
        List<Map<String, Object>>listMaps=WcpDataBase.queryMap("wcp","SELECT MENU_ID FROM WCP_AUTH_MENU WHERE PARENT_ID=? AND GROUP_ID = ?",new Object[]{menuId,groupId},"menuId-MENU_ID;");
        if(listMaps!=null && listMaps.size()>0){
            throw new ServiceException("该菜单存在子集菜单，请先删除子菜单",HttpStatus.BAD_REQUEST);
        }else{
            String sql="DELETE FROM WCP_AUTH_MENU WHERE MENU_ID=? AND GROUP_ID = ?";
            boolean flag= WcpDataBase.execute("wcp",sql,new Object[]{menuId,groupId});
            boolean f2=WcpDataBase.execute("wcp", "DELETE FROM WCP_AUTH_ROLE_MENU WHERE MENU_ID = ?  AND GROUP_ID = ?", new Object[]{menuId,groupId});
            if(f2){
                result.put(HttpStatus.DATA, null);
                result.put(HttpStatus.MSG, "删除成功");
            }else{
                throw new ServiceException("删除失败");
            }

        }

    }

    @ApiService(value = "查询菜单树",serviceKey = "treeselect",signType = 2)
    public void facesTree(Map<String, Object> result, JSONObject object) {
        String sql="SELECT MENU_ID,MENU_NAME,PARENT_ID FROM WCP_AUTH_MENU WHERE GROUP_ID = ?";
        String groupId=WcpThreadLocal.getProjectId();
        List<Map<String,Object>> mapList=WcpDataBase.queryMap("wcp",sql,new Object[]{groupId},"id-MENU_ID;label-MENU_NAME;parentId-PARENT_ID;");
        List<Map<String, Object>> treeList= ZtreList.buildMenuTree(mapList);
        result.put(HttpStatus.DATA, treeList);
        result.put(HttpStatus.MSG, "查询成功");
    }

    public static void main(String[] args) {
        System.out.println(StringUtil.generateUUID());
    }
}
