package com.wcp.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.auth.entitiy.ZtreList;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description 接口管理
 * 2. <AUTHOR>
 * 3. @Date 2024/8/15 14:05
 */
@ApiGroup(value = "自定义接口管理", serviceKey = "auth/faces", classify = "auth/faces", classifyName = "自定义接口管理")

public class InterfaceService {
    @ApiService(value = "新增菜单",serviceKey = "addFaces",signType = 2)
    public void addDept(Map<String, Object> result, JSONObject object) {
        String sql="INSERT INTO WCP_AUTH_INTERFACES(FACES_ID,FACES_NAME,PARENT_ID,FACES_URL,FACES_TYPE,CREATE_BY,UPDATE_BY,GROUP_ID) VALUES(?,?,?,?,?,?,?,?)";
        String facesId=StringUtil.generateUUID();
        String parentId=object.getString("parentId");
        String facesName=object.getString("facesName");
        String facesType=object.getString("facesType");
        String facesUrl=object.getString("facesUrl");//路由地址
        String account= WcpThreadLocal.getUserAccount();
        String groupId=WcpThreadLocal.getProjectId();
        boolean flag= WcpDataBase.execute("wcp",sql,new Object[]{facesId,facesName,parentId,facesUrl,facesType,account,account,groupId});
        if(flag){
            result.put(HttpStatus.DATA, null);
            result.put(HttpStatus.MSG, "新增成功" );
        }else{
            throw new ServiceException("新增失败");
        }
    }

    @ApiService(value = "查询接口列表",serviceKey = "list",signType = 2)
    public void list(Map<String, Object> result, JSONObject object) {
        String sql="SELECT FACES_ID,FACES_NAME,PARENT_ID,FACES_URL,FACES_TYPE,CREATE_TIME FROM WCP_AUTH_INTERFACES WHERE GROUP_ID=?";
        String facesName=object.getString("facesName");
        String facesId=object.getString("facesId");
        List<Object> params=new ArrayList<>();
        String groupId=WcpThreadLocal.getProjectId();
        params.add(groupId);
        if(StringUtil.isNotEmpty(facesName)){
            sql+=" AND FACES_NAME like ?";
            params.add("%" + facesName + "%");
        }
        if(StringUtil.isNotEmpty(facesId)){
            sql+=" AND FACES_ID = ? OR PARENT_ID=?";
            params.add(facesId);
            params.add(facesId);
        }
        sql+=" ORDER BY CREATE_TIME DESC";
        List<Map<String,Object>> mapList=WcpDataBase.queryMap("wcp",sql,params.toArray(),"facesId-FACES_ID;facesName-FACES_NAME;parentId-PARENT_ID;facesUrl-FACES_URL;facesType-FACES_TYPE;createTime-CREATE_TIME");
        // List<Map<String, Object>> treeList= ZtreList.buildMenuTree(mapList);
        result.put(HttpStatus.DATA, mapList);
        result.put(HttpStatus.MSG, "查询成功");
    }

    @ApiService(value = "修改接口信息",serviceKey = "update",signType = 2)
    public void update(Map<String, Object> result, JSONObject object) {
        String sql="UPDATE WCP_AUTH_INTERFACES SET FACES_NAME=?,PARENT_ID=?,FACES_URL=?,FACES_TYPE=?,UPDATE_BY=?  WHERE FACES_ID=? AND GROUP_ID=?";
        String facesId=object.getString("facesId");
        String parentId=object.getString("parentId");
        String facesName=object.getString("facesName");
        String facesType=object.getString("facesType");
        String facesUrl=object.getString("facesUrl");//路由地址
        String account= WcpThreadLocal.getUserAccount();
        String groupId=WcpThreadLocal.getProjectId();
        boolean flag= WcpDataBase.execute("wcp",sql,new Object[]{facesName,parentId,facesUrl,facesType,account,facesId,groupId});
        if(flag){
            result.put(HttpStatus.DATA, null);
            result.put(HttpStatus.MSG, "新增成功" );
        }else{
            throw new ServiceException("新增失败");
        }
    }

    @ApiService(value = "查询接口详情",serviceKey = "queryDetail",signType = 2)
    public void queryDetail(Map<String, Object> result, JSONObject object) {
        String sql="SELECT FACES_ID,FACES_NAME,PARENT_ID,FACES_URL,FACES_TYPE,CREATE_TIME FROM WCP_AUTH_INTERFACES  WHERE FACES_ID=?  AND GROUP_ID=?";
        String facesId=object.getString("facesId");
        String groupId=WcpThreadLocal.getProjectId();
        Map<String,Object> map=WcpDataBase.querySingleMap("wcp",sql,new Object[]{facesId,groupId},"facesId-FACES_ID;facesName-FACES_NAME;parentId-PARENT_ID;facesUrl-FACES_URL;facesType-FACES_TYPE;createTime-CREATE_TIME");
        // List<Map<String, Object>> treeList= ZtreList.buildMenuTree(mapList);
        result.put(HttpStatus.DATA, map);
        result.put(HttpStatus.MSG, "查询成功");
    }

    @ApiService(value = "查询接口分组",serviceKey = "groupList",signType = 2)
    public void groupList(Map<String, Object> result, JSONObject object) {
        String sql="SELECT FACES_ID,FACES_NAME,PARENT_ID,FACES_URL,FACES_TYPE,CREATE_TIME FROM WCP_AUTH_INTERFACES WHERE FACES_TYPE='m'  AND GROUP_ID=?";
        String groupId=WcpThreadLocal.getProjectId();
        List<Map<String,Object>> mapList=WcpDataBase.queryMap("wcp",sql,new Object[]{groupId},"facesId-FACES_ID;facesName-FACES_NAME;parentId-PARENT_ID;facesUrl-FACES_URL;facesType-FACES_TYPE;createTime-CREATE_TIME");
        result.put(HttpStatus.DATA, mapList);
        result.put(HttpStatus.MSG, "查询成功");
    }

    @ApiService(value = "查询接口树",serviceKey = "facesTree",signType = 2)
    public void facesTree(Map<String, Object> result, JSONObject object) {
        String sql="SELECT FACES_ID,FACES_NAME,PARENT_ID,FACES_URL,FACES_TYPE,CREATE_TIME FROM WCP_AUTH_INTERFACES WHERE GROUP_ID=? ";
        String groupId=WcpThreadLocal.getProjectId();
        List<Object> params=new ArrayList<>();
        params.add(groupId);
        List<Map<String,Object>> mapList=WcpDataBase.queryMap("wcp",sql,params.toArray(),"id-FACES_ID;label-FACES_NAME;parentId-PARENT_ID;facesUrl-FACES_URL;facesType-FACES_TYPE;createTime-CREATE_TIME");
        List<Map<String, Object>> treeList= ZtreList.buildMenuTree(mapList);
        result.put(HttpStatus.DATA, treeList);
        result.put(HttpStatus.MSG, "查询成功");
    }

    @ApiService(value = "删除接口",serviceKey = "delFaces",signType = 2)
    public void delFaces(Map<String, Object> result, JSONObject object) {
        String facesId=object.getString("facesId");
        String querySql="SELECT MENU_ID FROM WCP_AUTH_MENU_FACES WHERE FACES_ID=? AND GROUP_ID=?";
        String groupId=WcpThreadLocal.getProjectId();
        List<Map<String,Object>> queryList=WcpDataBase.queryMap("wcp",querySql,new Object[]{facesId,groupId},null);
        if(queryList!=null && queryList.size()>0){
            throw new ServiceException("已有其他菜单引用该接口，请先删除",HttpStatus.BAD_REQUEST);
        }else{
            boolean flag= WcpDataBase.execute("wcp","DELETE FROM WCP_AUTH_INTERFACES WHERE GROUP_ID=? AND (FACES_ID=? OR PARENT_ID=?)",new Object[]{groupId,facesId,facesId});
            String sql="SELECT FACES_ID,FACES_NAME,PARENT_ID,FACES_URL,FACES_TYPE,CREATE_TIME FROM WCP_AUTH_INTERFACES  WHERE GROUP_ID=? ";
            List<Map<String,Object>> mapList=WcpDataBase.queryMap("wcp",sql,new Object[]{groupId},"id-FACES_ID;label-FACES_NAME;parentId-PARENT_ID;facesUrl-FACES_URL;facesType-FACES_TYPE;createTime-CREATE_TIME");
            List<Map<String, Object>> treeList= ZtreList.buildMenuTree(mapList);
            result.put(HttpStatus.DATA, treeList);
            result.put(HttpStatus.MSG, "查询成功");
        }


    }
}
