package com.wcp.auth.util;

import com.alibaba.druid.util.Base64;
import org.apache.commons.lang3.RandomUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.Random;


/**
 * <AUTHOR>
 * @ClassName: ValidateCodeUtil
 * @Description: 验证码生成工具类
 * @date 2017年11月14日 上午11:00:07
 */
public class ValidateCodeUtil {
    private static Validate validate = null;                  //验证码类，用于最后返回此对象，包含验证码图片base64和真值
    private static Random random = new Random();              //随机类，用于生成随机参数
    private static String randString = "0123456789abcdefghijkmnpqrtyABCDEFGHIJLMNQRTY";//随机生成字符串的取值范围  

    private static int width = 80;     //图片宽度  
    private static int height = 34;    //图片高度  
    private static int StringNum = 4;  //字符的数量  
    private static int lineSize = 40;  //干扰线数量  


    //将构造函数私有化 禁止new创建
    private ValidateCodeUtil() {
        super();
    }

    /**
     * 获取随机字符,并返回字符的String格式
     *
     * @param index (指定位置)
     * @return
     */
    private static String getRandomChar(int index) {
        //获取指定位置index的字符，并转换成字符串表示形式  
        return String.valueOf(randString.charAt(index));
    }

    /**
     * 获取随机指定区间的随机数
     *
     * @param min (指定最小数)
     * @param max (指定最大数)
     * @return
     */
    private static int getRandomNum(int min, int max) {
        return RandomUtils.nextInt(min, max);
    }

    /**
     * 获得字体
     *
     * @return
     */
    private static Font getFont() {
        return new Font("Fixedsys", Font.CENTER_BASELINE, 25);  //名称、样式、磅值  
    }

    /**
     * 获得颜色
     *
     * @return
     */
    private static Color getRandColor(int frontColor, int backColor) {
        if (frontColor > 255)
            frontColor = 255;
        if (backColor > 255)
            backColor = 255;

        int red = frontColor + random.nextInt(backColor - frontColor - 16);
        int green = frontColor + random.nextInt(backColor - frontColor - 14);
        int blue = frontColor + random.nextInt(backColor - frontColor - 18);
        return new Color(red, green, blue);
    }

    /**
     * 绘制字符串,返回绘制的字符串
     *
     * @param g
     * @param randomString
     * @param i
     * @return
     */
    private static String drawString(Graphics g, String randomString, int i) {
        Graphics2D g2d = (Graphics2D) g;
        g2d.setFont(getFont());   //设置字体
        g2d.setColor(new Color(random.nextFloat(), random.nextFloat(), random.nextFloat()));//设置颜色
        String randChar = String.valueOf(getRandomChar(random.nextInt(randString.length())));
        randomString += randChar;   //组装  
        int rot = getRandomNum(5, 10);
        g2d.translate(random.nextInt(3), random.nextInt(3));
        g2d.rotate(rot * Math.PI / 180);
        g2d.drawString(randChar, 13 * i, 20);
        g2d.rotate(-rot * Math.PI / 180);
        return randomString;
    }

    /**
     * 绘制干扰线
     *
     * @param g
     */
    private static void drawLine(Graphics g) {
        //起点(x,y)  偏移量x1、y1  
        int x = random.nextInt(width);
        int y = random.nextInt(height);
        int xl = random.nextInt(13);
        int yl = random.nextInt(15);
        g.setColor(new Color(random.nextFloat(), random.nextFloat(), random.nextFloat()));
        g.drawLine(x, y, x + xl, y + yl);
    }

    /**
     * @return String 返回base64
     * @MethodName: getRandomCode
     * @Description: 生成Base64图片验证码
     */
    public static Validate getRandomCode() {
        validate = validate == null ? new Validate() : validate;

        // BufferedImage类是具有缓冲区的Image类,Image类是用于描述图像信息的类  
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_BGR);
        Graphics g = image.getGraphics();// 获得BufferedImage对象的Graphics对象  
        g.fillRect(0, 0, width, height);//填充矩形  
        g.setFont(new Font("Times New Roman", Font.ROMAN_BASELINE, 18));//设置字体  
        g.setColor(getRandColor(110, 133));//设置颜色  
//        //绘制干扰线
//        for (int i = 0; i <= lineSize; i++) {
//            drawLine(g);
//        }
        //绘制字符  
        String randomString = "";
        for (int i = 1; i <= StringNum; i++) {
            randomString = drawString(g, randomString, i);
            validate.setValue(randomString);
        }

        g.dispose();//释放绘图资源  
        ByteArrayOutputStream bs = null;
        try {
            bs = new ByteArrayOutputStream();
            ImageIO.write(image, "png", bs);//将绘制得图片输出到流  
            String imgsrc = Base64.byteArrayToBase64(bs.toByteArray());
            validate.setBase64Str(imgsrc);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                bs.close();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                bs = null;
            }
        }
        return validate;
    }

    /**
     * <AUTHOR>
     * @ClassName: Validate
     * @Description: 验证码类
     * @date 2017年11月14日 上午11:35:34
     */
    public static class Validate implements Serializable {
        private static final long serialVersionUID = 1L;
        private String Base64Str;        //Base64 值
        private String value;            //验证码值

        public String getBase64Str() {
            return Base64Str;
        }

        public void setBase64Str(String base64Str) {
            Base64Str = base64Str;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public static void main(String[] args) {
        //获取base64验证码  内容是随机的
        Validate randomCode = ValidateCodeUtil.getRandomCode();

        //base64字符串
        String base64Str = randomCode.Base64Str;

        //验证码内容
        String value = validate.getValue();

        //https://www.toolscat.com/img/base64-img 这个网站在线解析 base64 图片

        //具体可以参考ShortMessageService 这个类中的 根据手机号发送短信验证码  虽然调用的是阿里云的短信发送服务,但是存储验证码的方式没什么区别

        //将验证码存到缓存中 以键值对的形式

        System.out.println(base64Str);//iVBORw0KGgoAAAANSUhEUgAAAFAAAAAiCAIAAABHmckwAAAC40lEQVR42tXYMUhWURQH8M+axCEpECQscg2bFEIDo69RMOjDCMHazIbaUpcUIoOWBsnVnAqpMURMChSDHMqhIUzEQcIhUnQU7MiB2+Hce8877757v/Ryhsf7Pt/j5/+8e+/7SgdVH49/Xq/avdqXbrAzpRS3+TWxckTAtrmU6DaC2QY/aHif2mzYpXS38ZmdCac2I/vm/ZNHBaw0n2qZKGjOAS6PfzVVxFwEjOYibBV46vclPFh/PcRKaR58VQeVOWnpGzuYnQNsa5V4k7Nh5zKfaOqHitXhMcG2uXW5npZhA7ihvI6lzFlgJwfTj3xmRmVFwULhpQa2r2FFYWsnLTDLrUvNshZLCaZalnaYPBrYmJ1t7Mwcz2cmLIwAdmSwk+p7qu1Pc2nD2KnAwqV833FqnS2d8Bl+trSDpVl4M+OlXxPipSerDbbnpIDo6Bjd+WGOh8+uQcnNfDzAf2p6hFSpGdnCo3tsEmZm58pkNzPNPC54davWDe7suxqrpalZAMszc8SE0exIGMyUPXqne3nufNgUDWaBLcxVMPbaX0RvaTB7W5qyNfEKi7CvvWUwmiOC772czX6Gz7zrArbwqsDO+CZqZ86ylsZbELx7+mEOsPI9Sd5XGjP9CKZrJbhgtghGc/YsrTdr3pPCwPg8VxXMBkxj5r8gb7kEcOa2mSYcyxwIRjPUTG+PZqcpL78aMJqD2RHAhm3Mjyr7zvo4Pc/+6ltzKzUrwXS5SgjOZXYOG4xmZLOdpmbSCjCHgL88/ffO+en5OJY5I5idYBZ13llaY+69fMs2O8B3O+Z8CVMzjtr5c0buMyO4uXHBZ2ZsuvwKy1KmORx8+EvikzeCGQ8WP5TlhH1mxtZoDy+7URHM3zsvMnNu8Oe3k1AAxgNaYIaCA6eZtrRgNh3OtBdGKlirbZu2OSHYHNsh06jtxmbPsGyWtv5tm8BWmhFMzRJ4bOM2lA/8v8yYsAmcgde2rmhC/gurCcAHCVcM1QAAAABJRU5ErkJggg==

        System.out.println(value);//DG7j
    }
}  
 