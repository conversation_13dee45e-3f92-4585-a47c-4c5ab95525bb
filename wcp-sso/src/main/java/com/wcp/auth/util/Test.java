package com.wcp.auth.util;

import com.wcp.auth.entitiy.MetaVo;
import com.wcp.auth.entitiy.RouterVo;
import com.wcp.auth.entitiy.TreeSelect;
import com.wcp.data.Constants;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2024/8/19 10:39
 */

public class Test {
    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVo> buildMenus(List<Map<String, Object>> menus)
    {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (Map<String, Object> menu : menus)
        {
            RouterVo router = new RouterVo();
            router.setHidden(true);
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setMeta(new MetaVo((String)menu.get("menuName"), (String)menu.get("icon"), true, (String)menu.get("routeUrl")));
            List<Map<String, Object>> cMenus = (List<Map<String, Object>>) menu.get("Children");
            if (cMenus!=null&& "M".equals(menu.get("menuType")))
            {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            }
            else if (isMenuFrame(menu))
            {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath((String) menu.get("routeUrl"));
                children.setComponent((String)menu.get("assemblyUrl"));
                children.setName(StringUtils.capitalize((String) menu.get("routeUrl")));
                children.setMeta(new MetaVo((String)menu.get("menuName"), (String)menu.get("icon"), true, (String)menu.get("routeUrl")));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            else if ("0".equals(menu.get("parentId")) && isInnerLink(menu))
            {
                router.setMeta(new MetaVo((String)menu.get("menuName"), (String)menu.get("icon")));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach((String) menu.get("routeUrl"));
                children.setPath(routerPath);
                children.setComponent("InnerLink");
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo((String)menu.get("menuName"), (String)menu.get("icon"), true, (String)menu.get("routeUrl")));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    public List<Map<String,Object>> buildMenuTree(List<Map<String,Object>> menus)
    {
        List<Map<String,Object>> returnList = new ArrayList<>();
        List<String> tempList = menus.stream()
                .map(menu -> (String) menu.get("menuId"))
                .collect(Collectors.toList());
        for (Iterator<Map<String,Object>> iterator = menus.iterator(); iterator.hasNext();)
        {
            Map<String,Object> menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.get("parentId")))
            {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = menus;
        }
        return returnList;
    }


    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(Map<String, Object> menu)
    {
        String routerName = StringUtils.capitalize((String)menu.get("routeUrl"));
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu))
        {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由名称，如没有配置路由名称则取路由地址
     *
     * @param name 路由名称
     * @param path 路由地址
     * @return 路由名称（驼峰格式）
     */
    public String getRouteName(String name, String path)
    {
        String routerName = StringUtils.isNotEmpty(name) ? name : path;
        return StringUtils.capitalize(routerName);
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(Map<String,Object> menu)
    {
        String routerPath = (String)menu.get("routeUrl");
        // 内链打开外网方式
        if (!"0".equals(menu.get("parentId")) && isInnerLink(menu))
        {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if ("0".equals(menu.get("parentId")) && "M".equals(menu.get("menuType"))  && "1".equals(menu.get("isFrame")))
        {
            routerPath = "/" + (String)menu.get("routeUrl");
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu))
        {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(Map<String,Object> menu)
    {
        String component = "Layout";
        if (StringUtils.isNotEmpty((String)menu.get("assemblyUrl")) && !isMenuFrame(menu))
        {
            component = (String)menu.get("assemblyUrl");
        }
        else if (StringUtils.isEmpty((String)menu.get("assemblyUrl")) && !"0".equals(menu.get("parentId")) && isInnerLink(menu))
        {
            component = "InnerLink";
        }
        else if (StringUtils.isEmpty((String)menu.get("assemblyUrl")) && isParentView(menu))
        {
            component = "ParentView";
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(Map<String, Object> menu)
    {
        return "0".equals(menu.get("parentId").toString()) && "C".equals(menu.get("menuType"))
                && menu.get("isFrame").equals("1");
    }

    /**
     * 是否为内链组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(Map<String,Object> menu)
    {
        return menu.get("isFrame").equals("1") && ishttp((String)menu.get("assemblyUrl"));
    }
    /**
     * 是否为http(s)://开头
     *
     * @param link 链接
     * @return 结果
     */
    public static boolean ishttp(String link)
    {
        return StringUtils.startsWithAny(link, Constants.HTTP, Constants.HTTPS);
    }
    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(Map<String,Object> menu)
    {
        return !"0".equals(menu.get("parentId").toString()) && "M".equals(menu.get("menuType"));
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list 分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public static List<Map<String, Object>> getChildPerms(List<Map<String, Object>> list, String parentId)
    {
        List<Map<String, Object>> returnList = new ArrayList<Map<String, Object>>();
        for (Iterator<Map<String, Object>> iterator = list.iterator(); iterator.hasNext();)
        {
            Map<String, Object> t = (Map<String, Object>) iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (((String)t.get("parentId")).equals(parentId))
            {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private static void recursionFn(List<Map<String, Object>> list, Map<String, Object> t)
    {
        // 得到子节点列表
        List<Map<String, Object>> childList = getChildList(list, t);
        t.put("Children",childList);
        for (Map<String, Object> tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private static List<Map<String, Object>> getChildList(List<Map<String, Object>> list, Map<String, Object> t)
    {
        List<Map<String, Object>> tlist = new ArrayList<Map<String, Object>>();
        Iterator<Map<String, Object>> it = list.iterator();
        while (it.hasNext())
        {
            Map<String, Object> n = (Map<String, Object>) it.next();
            if (n.get("parentId").toString().equals(t.get("parentId").toString()))
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private static boolean hasChild(List<Map<String, Object>> list, Map<String, Object> t)
    {
        return getChildList(list, t).size() > 0;
    }
    /**
     * 内链域名特殊字符替换
     *
     * @return 替换后的内链域名
     */
    public String innerLinkReplaceEach(String path)
    {
        return StringUtils.replaceEach(path, new String[] { Constants.HTTP, Constants.HTTPS, Constants.WWW, ".", ":" },
                new String[] { "", "", "", "/", "/" });
    }
}
