# WCP

## WCP 代码结构介绍
```
├── Wcp3.0   
│       └── wcp-common                          --项目管理模块
│              └── com.wcp.annotation           --应用模块的自定义注解
│              └── com.wcp.utils                --核心工具类
│       └── wcp-core                          --项目管理模块
│              └── com.wcp.platform           --平台发布页面调用平台相关固定接口
│              └── com.wcp.quartz             --定时器增删改Service
│       └── wcp-platform                          --平台模型 使用mybatisplus
│              └── com.wcp.controller           --平台controller
│              └── com.wcp.core                 --核心工具类
│              └── com.wcp.domain               --实体类
│              └── com.wcp.mapper               --dao
│              └── com.wcp.service              --业务类
│       └── wcp-database                        --应用使用的jdbc模块
│       └── wcp-service                         --应用模块 沿用之前的wcp2.0版本的jdbc 
│               └── com.wcp.service             --应用业务
│       └── wcp-sso                             --sso 模块
│       └── pom.xml 
 
```
#### JDK版本:jdk17
```
```

## 项目打包输出目录 
#### 这个打包主要是将配置文件和依赖分离，也可以选择将配置和代码打包在一起，几种方案都可兼容
```
cd wcp\wcp-platform\target\wcp
-- lib 项目依赖jar
--config 项目配置
--wcp-platform.jar 平台jar打包jar
```


## 代码规范
```
1.表名和字段都统一大写,字段禁止使用关键字
```
## wcp-platform
```
1.mybatis中只有模糊查询可以使用$,例如:like '%${userName}%' ,其他查询统一使用name=#{Name}
2.本地开发调试需要关闭权限过滤,将spring.security改成false
3.swagger接口文档地址:http://127.0.0.1:9090/wcp-platform/doc.html,platform模块接口和实体类需要使用swagger注解注明相关注释
```

## wcp-service
```
1.所有接口方法需要登录拦截的将signtype值写2
2.本地开发调试需要关闭权限过滤,将spring.sso.openPermissions值改为false

```

http://171.43.138.202:9099/tias/oauth/authorize?client_id=cidea5689aa5dfc41a4b55dd336caa555c6&response_type=code&redirect_uri=http://192.168.1.103:5174

http://127.0.0.1:8080/wcp-service/auth/v1/token?code=wHtU5R