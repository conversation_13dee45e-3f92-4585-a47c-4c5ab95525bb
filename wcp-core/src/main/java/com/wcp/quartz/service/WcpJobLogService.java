package com.wcp.quartz.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.data.WebServiceConst;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.quartz.domain.WcpJobLog;
import com.wcp.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description 调度任务日志
 * 2. <AUTHOR>
 * 3. @Date 2024/6/20 11:17
 */

@ApiGroup(value = "jobLog", serviceKey = "jobLog", classify = "jobLog", classifyName = "测试服务")
@Service
public class WcpJobLogService {

    @ApiService(value = "查询日志列表", serviceKey = "list", signType = 0, notes = "查询日志列表", params = {
            @ApiParam(value = "显示条数", name = "pageSize", clazz = Integer.class, paramType = "body", required = true),
            @ApiParam(value = "当前页数", name = "pageNum", clazz = Integer.class, paramType = "body", required = true),
            @ApiParam(value = "排序字段", name = "orderByColumn", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "排序方式", name = "isAsc", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "定时器名称", name = "jobName", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "定时器分组", name = "jobGroup", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "状态", name = "status", clazz = Integer.class, paramType = "body", required = true),
            @ApiParam(value = "任务ID", name = "jobId", clazz = Long.class, paramType = "body", required = true)
    })
    public void list(Map<String, Object> result, JSONObject object) throws Exception {
        Integer pageSize = object.getInteger("pageSize");//显示条数
        Integer pageNum = object.getInteger("pageNum");//当前页数
        String orderByColumn = object.getString("orderByColumn");//排序字段
        String isAsc = object.getString("isAsc");//排序方式
        String jobName = object.getString("jobName");//定时器名称
        String jobGroup = object.getString("jobGroup");//定时器分组
        Integer status = object.getInteger("status");//状态
        String beginTime = object.getString("beginTime");
        String endTime = object.getString("endTime");
        Long jobId=object.getLong("jobId");//jobId

        String sql = "SELECT JOB_LOG_ID,JOB_NAME,JOB_GROUP,INVOKE_TARGET,JOB_MESSAGE,STATUS,EXCEPTION_INFO,CREATE_TIME FROM WCP_JOB_LOG ";
        String alias = "jobLogId-JOB_LOG_ID;jobName-JOB_NAME;jobGroup-JOB_GROUP;invokeTarget-INVOKE_TARGET;jobMessage-JOB_MESSAGE;status-STATUS;exceptionInfo-EXCEPTION_INFO;createTime-CREATE_TIME";
        List<Object> objects = new ArrayList<>();
        List<String> sqls = new ArrayList<>();
        if(jobId!=null){
            sqls.add("JOB_ID= ?");
            objects.add(jobId);
        }
        if (!StringUtil.isEmptyAll(jobName)) {
            sqls.add("JOB_NAME LIKE ?");
            objects.add("%" + jobName + "%");
        }

        if (!StringUtil.isEmptyAll(jobGroup)) {
            sqls.add("job_group = ?");
            objects.add(jobGroup);
        }

        if (status != null ) {
            sqls.add("STATUS = ?");
            objects.add(status);
        }
        if (!StringUtil.isEmptyAll(beginTime)) {
            sqls.add("CREATE_TIME >= ?");
            objects.add(beginTime);
        }

        if (!StringUtil.isEmptyAll(endTime)) {
            sqls.add("CREATE_TIME >= ?");
            objects.add(endTime);
        }

        for (int i = 0; i < sqls.size(); i++) {
            if (i == 0) {
                sql += "WHERE " + sqls.get(i);
            } else {
                sql += "AND " + sqls.get(i);

            }
        }

       /* if (!StringUtil.isEmptyAll(orderByColumn)) {
            if (!StringUtil.isEmptyAll(isAsc)) {
                isAsc = "DESC";
            }
            sql += "ORDER BY " + orderByColumn + " " + isAsc;
        }*/
        sql += "ORDER BY CREATE_TIME DESC";
        if (pageSize == null) {
            pageSize = 20;
        }

        if (pageNum == null) {
            pageNum = 1;
        }

        Map<String, Object> map = WcpDataBase.queryMapByPaging("wcp", sql, objects.toArray(), alias, pageNum, pageSize, null);
        result.put(WebServiceConst.DATA, map);
        result.put(WebServiceConst.SUCCESS, true);
    }

    @ApiService(value = "查询定时器详情", serviceKey = "jobLogId", signType = 0, notes = "查询定时器详情", params = {
            @ApiParam(value = "定时器ID", name = "jobLogId", clazz = Integer.class, paramType = "body", required = true)
    })
    public void jobId(Map<String, Object> result, JSONObject object) throws Exception {
        Long jobLogId = object.getLong("jobLogId");
        if (jobLogId == null) {
            throw  new ServiceException("请选择日志!",HttpStatus.BAD_REQUEST);
        }
        WcpJobLog sysJobLog = selectJobById(jobLogId);
        result.put(WebServiceConst.DATA, sysJobLog);
    }

    @ApiService(value = "删除日志", serviceKey = "deleteJobLogByIds", signType = 0, notes = "删除日志", params = {
            @ApiParam(value = "定时器日志ID", name = "jobLogIds", clazz = JSONArray.class, paramType = "body", required = true)
    })
    public void deleteJobLogByIds(Map<String, Object> result, JSONObject object) throws Exception {
        JSONArray jobLogId = object.getJSONArray("jobLogIds");
        if (jobLogId == null) {
            throw  new ServiceException("请选择需要删除的日志!",HttpStatus.BAD_REQUEST);
        }
        List<Long> javaList = jobLogId.toJavaList(Long.class);
        if (javaList.isEmpty()) {
            throw new ServiceException("当前用户不存在,请输入正确的账号!", HttpStatus.NO_CONTENT);
        }
        String sql = "DELETE FROM WCP_JOB_LOG WHERE JOB_LOG_ID IN (";
        List<Object> objects = new ArrayList<>();
        for (Long l : javaList) {
            sql += "?,";
            objects.add(l);
        }
        sql += "?)";
        objects.add("");

        boolean wcp = WcpDataBase.execute("wcp", sql, objects.toArray());
        if (wcp) {
            result.put(HttpStatus.MSG, "删除定时器日志成功!");
        } else {
            throw new ServiceException("当前用户不存在,删除定时器日志失败!", HttpStatus.BAD_REQUEST);
        }
        int a=0;
        try{
           a++;
        }catch (Exception e){
            a--;
            e.printStackTrace();
        }

    }


    private static WcpJobLog selectJobById(Long jobLogId) {
        String sql = "SELECT JOB_LOG_ID,JOB_NAME,JOB_GROUP,INVOKE_TARGET,JOB_MESSAGE,STATUS,EXCEPTION_INFO,CREATE_TIME FROM WCP_JOB_LOG WHERE JOB_LOG_ID = ? ";
        List<WcpJobLog> wcp = WcpDataBase.query("wcp", sql, new Object[]{jobLogId}, WcpJobLog.class);
        WcpJobLog sysJobLog = new WcpJobLog();
        if (!wcp.isEmpty()) {
            sysJobLog = wcp.get(0);
        }
        return sysJobLog;
    }


    public boolean addJobLog(WcpJobLog sysJobLog) {
        String sql = "INSERT INTO WCP_JOB_LOG ( JOB_NAME,JOB_GROUP,INVOKE_TARGET,JOB_MESSAGE,STATUS,EXCEPTION_INFO,CREATE_TIME,JOB_ID) VALUES(?,?,?,?,?,?,?,?)";
        return WcpDataBase.execute("wcp", sql, new Object[]{
                sysJobLog.getJobName(),
                sysJobLog.getJobGroup(),
                sysJobLog.getInvokeTarget(),
                sysJobLog.getJobMessage(),
                sysJobLog.getStatus(),
                sysJobLog.getExceptionInfo(),
                sysJobLog.getStartTime(),
                sysJobLog.getJobId()
        });
    }
}
