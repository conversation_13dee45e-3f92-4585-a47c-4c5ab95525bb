package com.wcp.quartz.service;

import java.util.Date;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.data.Constants;
import com.wcp.data.ScheduleConstants;
import com.wcp.data.WebServiceConst;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.exception.job.TaskException;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.quartz.domain.WcpJob;
import com.wcp.quartz.core.CronUtils;
import com.wcp.quartz.core.ScheduleUtils;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 1. @Description wcp定时器jobService
 * 2. <AUTHOR>
 * 3. @Date 2024/6/20 10:54
 */

@Service
@ApiGroup(value = "平台定时器服务", serviceKey = "job", classify = "job", classifyName = "定时器服务")
public class WcpJobService {

    @Autowired
    private Scheduler scheduler;

    @ApiService(value = "查询定时器列表", serviceKey = "list", signType = 0, notes = "查询定时器列表", params = {
            @ApiParam(value = "显示条数", name = "pageSize", clazz = Integer.class, paramType = "body", required = true),
            @ApiParam(value = "当前页数", name = "pageNum", clazz = Integer.class, paramType = "body", required = true),
            @ApiParam(value = "排序字段", name = "orderByColumn", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "排序方式", name = "isAsc", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "定时器名称", name = "jobName", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "定时器分组", name = "jobGroup", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "状态", name = "status", clazz = Integer.class, paramType = "body", required = true)
    })
    public void list(Map<String, Object> result, JSONObject object) throws Exception {
        Integer pageSize = object.getInteger("pageSize");//显示条数
        Integer pageNum = object.getInteger("pageNum");//当前页数
        String orderByColumn = object.getString("orderByColumn");//排序字段
        String isAsc = object.getString("isAsc");//排序方式
        String jobName = object.getString("jobName");//定时器名称
        String jobGroup = object.getString("jobGroup");//定时器分组
        Integer status = object.getInteger("status");//状态
        String sql = "SELECT JOB_ID, JOB_NAME, JOB_GROUP, INVOKE_TARGET, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, STATUS, CREATE_BY, CREATE_TIME, REMARK,PROJECT_ID FROM WCP_JOB  ";
        String alias = "jobId-JOB_ID;jobName-JOB_NAME;jobGroup-JOB_GROUP;invokeTarget-INVOKE_TARGET;cronExpression-CRON_EXPRESSION;misfirePolicy-MISFIRE_POLICY;concurrent-CONCURRENT;status-STATUS;createBy-CREATE_BY;createTime-CREATE_TIME;remark-REMARK;projectId-PROJECT_ID";
        List<Object> objects = new ArrayList<>();
        List<String> sqls = new ArrayList<>();

        if (!StringUtil.isEmptyAll(jobName)) {
            sqls.add("JOB_NAME LIKE ?");
            objects.add("%" + jobName + "%");
        }

        if (!StringUtil.isEmptyAll(jobGroup)) {
            sqls.add("job_group = ?");
            objects.add(jobGroup);
        }

        if (status != null) {
            sqls.add("STATUS = ?");
            objects.add(status);
        }

        for (int i = 0; i < sqls.size(); i++) {
            if (i == 0) {
                sql += "WHERE " + sqls.get(i);
            } else {
                sql += "AND " + sqls.get(i) + " ";

            }
        }
        sql += "ORDER BY CREATE_TIME DESC";
        if (pageSize == null) {
            pageSize = 20;
        }

        if (pageNum == null) {
            pageNum = 1;
        }

        Map<String, Object> map = WcpDataBase.queryMapByPaging("wcp", sql, objects.toArray(), alias, pageNum, pageSize, "");
        result.put(WebServiceConst.DATA, map);
    }

    @ApiService(value = "查询定时器详情", serviceKey = "jobId", signType = 0, notes = "查询定时器详情", params = {
            @ApiParam(value = "定时器ID", name = "jobId", clazz = Integer.class, paramType = "body", required = true)
    })
    public void jobId(Map<String, Object> result, JSONObject object) throws Exception {
        Long jobId = object.getLong("jobId");
        if (jobId == null) {
            throw  new ServiceException("请选择定时器!",HttpStatus.BAD_REQUEST);

        }
        WcpJob sysJob = selectJobById(jobId);
        result.put(WebServiceConst.DATA, sysJob);
    }

    @ApiService(value = "查询定时器分组", serviceKey = "groupList", signType = 0, notes = "查询定时器分组")
    public void groupList(Map<String, Object> result, JSONObject object) throws Exception {
       String sql="SELECT JOB_KEY,JOB_GROUP FROM  WCP_JOB_GROUP";
        List<Map<String,Object>> data=WcpDataBase.queryMap("wcp",sql,new Object[]{},null);
        result.put(WebServiceConst.DATA, data);
    }


    @ApiService(value = "添加定时器", serviceKey = "add", signType = 0, notes = "添加定时器", params = {
            @ApiParam(value = "定时器名称", name = "jobName", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "定时器分组", name = "jobGroup", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "调用目标字符串", name = "invokeTarget", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "cron执行表达式", name = "cronExpression", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "cron计划策略", name = "misfirePolicy", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "是否并发执行（0允许 1禁止）", name = "concurrent", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "搜索值", name = "searchValue", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "备注", name = "remark", clazz = JSONObject.class, paramType = "body", required = true)
    })
    public void add(Map<String, Object> result, JSONObject object) throws Exception {
        WcpJob job = new WcpJob();
        job.setJobName(object.getString("jobName"));
        job.setJobGroup(object.getString("jobGroup"));
        job.setInvokeTarget(object.getString("invokeTarget"));
        job.setCronExpression(object.getString("cronExpression"));
        job.setMisfirePolicy(object.getString("misfirePolicy"));
        job.setConcurrent(object.getString("concurrent"));
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        job.setCreateBy(WcpThreadLocal.getUserId());
        job.setCreateTime(new Date());
        job.setUpdateBy(WcpThreadLocal.getUserId());
        job.setUpdateTime(new Date());
        job.setRemark(object.getString("remark"));
        job.setExtra(object.getString("extra"));
        String projectId=WcpThreadLocal.getProjectId();
        job.setProjectId(projectId);
        //判断定时任务是否已经存在
        String checkSql="select JOB_ID from WCP_JOB where JOB_NAME=? and JOB_GROUP=? and INVOKE_TARGET=?";
        List<Map<String,Object>> listMap=WcpDataBase.queryMap("wcp",checkSql,new Object[]{job.getJobName(),job.getJobGroup(),job.getInvokeTarget()},null);
        if(listMap!=null && listMap.size()>0){
            throw  new ServiceException("该定时任务已存在，请勿重复添加",HttpStatus.BAD_REQUEST);
        }
        else if (!CronUtils.isValid(job.getCronExpression())) {
            throw  new ServiceException("新增任务'" + job.getJobName() + "'失败，Cron表达式不正确",HttpStatus.BAD_REQUEST);
        } else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI)) {
            throw  new ServiceException("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用",HttpStatus.BAD_REQUEST);
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            throw  new ServiceException("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用",HttpStatus.BAD_REQUEST);
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{Constants.HTTP, Constants.HTTPS})) {
            throw  new ServiceException("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用",HttpStatus.BAD_REQUEST);

        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), Constants.JOB_ERROR_STR)) {
            throw  new ServiceException("新增任务'" + job.getJobName() + "'失败，目标字符串存在违规",HttpStatus.BAD_REQUEST);
        }

        String sql = "INSERT INTO WCP_JOB (JOB_NAME,JOB_GROUP,INVOKE_TARGET,CRON_EXPRESSION,MISFIRE_POLICY,CONCURRENT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,EXTRA,PROJECT_ID ) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        boolean wcp = WcpDataBase.execute("wcp", sql, new Object[]{
                job.getJobName(),
                job.getJobGroup(),
                job.getInvokeTarget(),
                job.getCronExpression(),
                job.getMisfirePolicy(),
                job.getConcurrent(),
                job.getStatus(),
                job.getCreateBy(),
                job.getCreateTime(),
                job.getUpdateBy(),
                job.getUpdateTime(),
                job.getRemark(),
                job.getExtra(),
                job.getProjectId()
        });

        if (wcp) {
            result.put(HttpStatus.MSG, "添加定时器成功!");
            ScheduleUtils.createScheduleJob(scheduler, job);
        } else {
            throw  new ServiceException("添加定时器失败!",HttpStatus.BAD_REQUEST);


        }
    }

    @ApiService(value = "修改定时器", serviceKey = "update", signType = 0, notes = "修改定时器", params = {
            @ApiParam(value = "定时器名称", name = "jobName", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "定时器分组", name = "jobGroup", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "调用目标字符串", name = "invokeTarget", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "cron执行表达式", name = "cronExpression", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "cron计划策略", name = "misfirePolicy", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "是否并发执行（0允许 1禁止）", name = "concurrent", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "搜索值", name = "searchValue", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "备注", name = "remark", clazz = JSONObject.class, paramType = "body", required = true)
    })
    public void update(Map<String, Object> result, JSONObject object) throws Exception {
        WcpJob job = new WcpJob();
        job.setJobId(object.getLong("jobId"));
        job.setJobName(object.getString("jobName"));
        job.setJobGroup(object.getString("jobGroup"));
        job.setInvokeTarget(object.getString("invokeTarget"));
        job.setCronExpression(object.getString("cronExpression"));
        job.setMisfirePolicy(object.getString("misfirePolicy"));
        job.setConcurrent(object.getString("concurrent"));
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        job.setCreateBy(WcpThreadLocal.getUserId());
        job.setCreateTime(new Date());
        job.setUpdateBy(WcpThreadLocal.getUserId());
        job.setUpdateTime(new Date());
        job.setExtra(object.getString("extra"));
        job.setRemark(object.getString("remark"));
        String projectId=WcpThreadLocal.getProjectId();
        job.setProjectId(projectId);

        if (!CronUtils.isValid(job.getCronExpression())) {
            throw  new ServiceException("修改任务'" + job.getJobName() + "'失败，Cron表达式不正确",HttpStatus.BAD_REQUEST);
        } else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI)) {
            throw  new ServiceException("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用",HttpStatus.BAD_REQUEST);
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            throw  new ServiceException("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用",HttpStatus.BAD_REQUEST);
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{Constants.HTTP, Constants.HTTPS})) {
            throw  new ServiceException("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用",HttpStatus.BAD_REQUEST);
        }
        boolean wcp = updateJob(job);
        updateSchedulerJob(job, job.getJobGroup());

        if (wcp) {
            result.put(HttpStatus.MSG, "修改定时器成功!");
            ScheduleUtils.createScheduleJob(scheduler, job);
        } else {
            throw  new ServiceException("修改定时器失败!",HttpStatus.BAD_REQUEST);
        }
    }


    @ApiService(value = "修改定时器状态", serviceKey = "changeStatus", signType = 0, notes = "修改定时器状态", params = {
            @ApiParam(value = "定时器ID", name = "jobId", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "定时器状态", name = "status", clazz = JSONObject.class, paramType = "body", required = true)
    })
    public void changeStatus(Map<String, Object> result, JSONObject object) throws Exception {
        Long jobId = object.getLong("jobId");
        if (jobId == null) {
            throw  new ServiceException("请选择定时器!",HttpStatus.BAD_REQUEST);
        }
        String status = object.getString("status");
        WcpJob job = selectJobById(jobId);
        job.setProjectId(WcpThreadLocal.getProjectId());
        boolean wcp = false;
        if (ScheduleConstants.Status.NORMAL.getValue().equals(status)) {
            wcp = resumeJob(job);
        } else if (ScheduleConstants.Status.PAUSE.getValue().equals(status)) {
            wcp = pauseJob(job);
        }
        if (wcp) {
            result.put(HttpStatus.MSG, "修改定时器状态成功!");
        } else {
            throw  new ServiceException("修改定时器状态失败!",HttpStatus.BAD_REQUEST);
        }

    }

    @ApiService(value = "立即执行定时器一次", serviceKey = "run", signType = 0, notes = "修改定时器", params = {
            @ApiParam(value = "定时器ID", name = "jobId", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "定时器分组", name = "jobGroup", clazz = JSONObject.class, paramType = "body", required = true)
    })
    public void run(Map<String, Object> result, JSONObject object) throws Exception {
        Long jobId = object.getLong("jobId");
        if (jobId == null) {
            throw  new ServiceException("请选择定时器!",HttpStatus.BAD_REQUEST);
        }
        String jobGroup = object.getString("jobGroup");
        if (StringUtil.isEmptyAll(jobGroup)) {
            throw  new ServiceException("请选择定时器分组!",HttpStatus.BAD_REQUEST);

        }
        WcpJob properties = selectJobById(jobId);
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(ScheduleConstants.TASK_PROPERTIES, properties);
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        boolean wcp = false;
        if (scheduler.checkExists(jobKey)) {
            wcp = true;
            scheduler.triggerJob(jobKey, dataMap);
        }
        if (wcp) {
            result.put(HttpStatus.MSG, "立即执行定时器一次成功!");
        } else {
            throw  new ServiceException("立即执行定时器一次失败!",HttpStatus.BAD_REQUEST);
        }
    }

    @ApiService(value = "删除定时器", serviceKey = "deleteJob", signType = 0, notes = "修改定时器", params = {
            @ApiParam(value = "定时器ID", name = "jobId", clazz = JSONObject.class, paramType = "body", required = true),
            @ApiParam(value = "定时器分组", name = "jobGroup", clazz = JSONObject.class, paramType = "body", required = true)
    })
    public void deleteJob(Map<String, Object> result, JSONObject object) throws Exception {
        Long jobId = object.getLong("jobId");
        if (jobId == null) {
            throw  new ServiceException("请选择定时器!",HttpStatus.BAD_REQUEST);
        }
        String jobGroup = object.getString("jobGroup");
        if (StringUtil.isEmptyAll(jobGroup)) {
            throw  new ServiceException("请选择定时器分组!",HttpStatus.BAD_REQUEST);

        }
        String sql = "DELETE FROM WCP_JOB WHERE JOB_ID = ?";
        boolean wcp = WcpDataBase.execute("wcp", sql, new Object[]{
                jobId
        });
        if (wcp) {
            scheduler.deleteJob(ScheduleUtils.getJobKey(jobId, jobGroup));
            result.put(HttpStatus.MSG, "删除定时器成功!");
        } else {
            throw  new ServiceException("删除定时器失败!",HttpStatus.BAD_REQUEST);
        }
    }
    private static boolean updateJob(WcpJob job) {
        String sql = "UPDATE WCP_JOB SET JOB_NAME = ?,JOB_GROUP = ?,INVOKE_TARGET = ?,CRON_EXPRESSION = ?,MISFIRE_POLICY = ?,CONCURRENT = ?,STATUS = ?,UPDATE_BY = ?,UPDATE_TIME = ?,EXTRA=? WHERE JOB_ID = ? AND PROJECT_ID=?";
        boolean wcp = WcpDataBase.execute("wcp", sql, new Object[]{
                job.getJobName(),
                job.getJobGroup(),
                job.getInvokeTarget(),
                job.getCronExpression(),
                job.getMisfirePolicy(),
                job.getConcurrent(),
                job.getStatus(),
                job.getUpdateBy(),
                job.getUpdateTime(),
                job.getExtra(),
                String.valueOf(job.getJobId()),
                job.getProjectId()

        });
        return wcp;
    }

    public boolean resumeJob(WcpJob job) throws SchedulerException {
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        job.setStatus(ScheduleConstants.Status.NORMAL.getValue());
        boolean wcp = updateJob(job);
        if (wcp) {
            scheduler.resumeJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
        return wcp;
    }

    public boolean pauseJob(WcpJob job) throws SchedulerException {
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        boolean wcp = updateJob(job);
        if (wcp) {
            scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
        return wcp;
    }

    private static WcpJob selectJobById(Long jobId) {
        String sql = "SELECT JOB_ID,JOB_NAME,JOB_GROUP,INVOKE_TARGET,CRON_EXPRESSION,MISFIRE_POLICY,CONCURRENT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,PROJECT_ID,EXTRA FROM WCP_JOB WHERE JOB_ID = ?";
        String alias = "jobId-JOB_ID;jobName-JOB_NAME;jobGroup-JOB_GROUP;invokeTarget-INVOKE_TARGET;cronExpression-CRON_EXPRESSION;misfirePolicy-MISFIRE_POLICY;concurrent-CONCURRENT;status-STATUS;createBy-CREATE_BY;createTime-CREATE_TIME;updateBy-UPDATE_BY;updateTime-UPDATE_TIME;remark-REMARK;projectId-PROJECT_ID;extra-EXTRA";
        List<Map<String, Object>> wcp = WcpDataBase.queryMap("wcp", sql, new Object[]{jobId}, alias);
        WcpJob sysJob = new WcpJob();
        if (!wcp.isEmpty()) {
            Map<String, Object> map = wcp.get(0);
            sysJob.setJobId((Long) map.get("jobId"));
            sysJob.setJobName(map.get("jobName")+"");
            sysJob.setJobGroup(map.get("jobGroup")+"");
            sysJob.setInvokeTarget(map.get("invokeTarget")+"");
            sysJob.setCronExpression(map.get("cronExpression")+"");
            sysJob.setMisfirePolicy(map.get("misfirePolicy")+"");
            sysJob.setConcurrent(map.get("concurrent")+"");
            sysJob.setStatus(map.get("status")+"");
            sysJob.setCreateBy(map.get("createBy")+"");
            sysJob.setCreateTime(((GregorianCalendar)map.get("createTime")).getTime());
            sysJob.setUpdateBy(map.get("updateBy")+"");
            sysJob.setUpdateTime(((GregorianCalendar) map.get("updateTime")).getTime());
            sysJob.setRemark(map.get("remark")+"");
            sysJob.setExtra(map.get("extra")+"");
            sysJob.setProjectId(map.get("projectId")+"");
        }
        return sysJob;
    }

    /**
     * 更新任务
     *
     * @param job      任务对象
     * @param jobGroup 任务组名
     */
    public void updateSchedulerJob(WcpJob job, String jobGroup) throws SchedulerException, TaskException {
        Long jobId = job.getJobId();
        // 判断是否存在
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        ScheduleUtils.createScheduleJob(scheduler, job);
    }


}
