package com.wcp.quartz.core;

import com.wcp.quartz.domain.WcpJob;
import org.quartz.JobExecutionContext;


/**
 * Description: 定时任务处理（允许并发执行）
 * Author: qianchao
 * Date: 2024/6/24 17:53
 */
public class QuartzJobExecution extends AbstractQuartzJob {
    @Override
    protected void doExecute(JobExecutionContext context, WcpJob sysJob) throws Exception
    {
        JobInvoke.invokeMethod(sysJob);
    }
}
