package com.wcp.quartz.core;
import com.wcp.annotation.ApiTimer;
import com.wcp.annotation.ApiTimerJob;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.db.jdbc.WcpJdbcService;
import com.wcp.exception.job.TaskException;
import com.wcp.minio.MinioService;
import com.wcp.quartz.domain.WcpJob;
import com.wcp.utils.AESUtil;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;


import java.lang.reflect.Method;
import java.util.*;

/**
 * 1. @Description TODO 定时器启动类
 * 2. <AUTHOR>
 * 3. @Date 2024/6/24 15:07
 */
@Component
public class HandleApiTimer implements ApplicationListener<ApplicationReadyEvent> {
    @Value("${wcp.quartz-auto-start}")
    public  Boolean quartzAutoStart;
    private static final Logger logger = LoggerFactory.getLogger(HandleApiTimer.class);

    /** 定时任务描述缓存 */
    public static List<WcpJob> TimerCacheList = new ArrayList<>();

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private Scheduler scheduler;

    /**
     * 从Spring bean中获取或有class,遍历class中所有的方法获取使用了@ApiTimer注解标记的方法
     * @param event
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event){
        Arrays.stream(applicationContext.getBeanDefinitionNames())
                .forEach(beanName -> {
                    Object bean = applicationContext.getBean(beanName);
                    Class<?> beanClass = AopProxyUtils.ultimateTargetClass(bean);
                    ReflectionUtils.doWithMethods(beanClass, method -> {
                        if (method.isAnnotationPresent(ApiTimer.class)) {
                            handleApiTimerAnnotatedParam(method);
                        }
                    }, ReflectionUtils.USER_DECLARED_METHODS);});
        try {
            init();
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        } catch (TaskException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理方法的ApiTimer注解参数
     */
    private void handleApiTimerAnnotatedParam(Method method)  {
        ApiTimer annotation = method.getAnnotation(ApiTimer.class);
        if (annotation != null) {
            String group = annotation.group();
            ApiTimerJob[] timerJobs = annotation.timerJob();
            if(timerJobs != null && timerJobs.length >0) {
                List<String> isExist = new ArrayList<>();
                for (ApiTimerJob timerJob : timerJobs) {
                    Long id = timerJob.id();
                    String name = timerJob.name();
                    String cron = timerJob.cron();
                    String notes = timerJob.notes();
                    String extra = timerJob.extra();
                    if (isExist.contains(name + group)) {
                        logger.error(group + "分组内的 " + name + " 已重复请重新修改！");
                        continue;
                    }
                    WcpJob jobBean = new WcpJob();
                    jobBean.setJobId(id);
                    jobBean.setJobName(name);
                    jobBean.setJobGroup(group);
                    jobBean.setCronExpression(cron);
                    jobBean.setInvokeTarget(method.getDeclaringClass().getName()+"."+method.getName());
                    jobBean.setStatus("0");
                    jobBean.setExtra(extra);
                    jobBean.setRemark(notes);
                    isExist.add(name+group);
                    TimerCacheList.add(jobBean);
                }
            }
        }
    }

    /**
     * 初始化所有的定时任务
     * @throws SchedulerException
     * @throws TaskException
     */
    public void init() throws SchedulerException, TaskException
    {

       /* List<Map<String,Object>> listMap= WcpDataBase.queryMap("wcp","SELECT PROJECT,SERVICE,DB_TYPE,DB_NAME,URL,USER_NAME,USER_PWD FROM WCP_SP_DB",new Object[]{},
                "project-PROJECT;service-SERVICE;dbType-DB_TYPE;dbName-DB_NAME;url-URL;userName-USER_NAME;userPwd-USER_PWD");
        if (listMap != null && listMap.size() > 0) {
            System.out.println("##################################WCP_SP_DB####################################");
            System.out.printf("%-40s %-20s %-20s %-20s %-80s %-20s %-20s%n",
                    "project", "service", "dbType", "dbName", "url", "userName", "userPwd");

            listMap.forEach(t -> {
                System.out.printf("%-40s %-20s %-20s %-20s %-80s %-20s %-20s%n",
                        t.get("project"),
                        t.get("service"),
                        t.get("dbType"),
                        t.get("dbName"),
                        t.get("url"),
                        t.get("userName"),
                        AESUtil.decrypt(t.get("userPwd").toString()));
            });
        }*/

        if(quartzAutoStart){
            scheduler.clear();
            String sql = "SELECT JOB_ID, JOB_NAME, JOB_GROUP, INVOKE_TARGET, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, STATUS, CREATE_BY, CREATE_TIME, REMARK,PROJECT_ID,EXTRA FROM WCP_JOB  ";
            String alias = "jobId-JOB_ID;jobName-JOB_NAME;jobGroup-JOB_GROUP;invokeTarget-INVOKE_TARGET;cronExpression-CRON_EXPRESSION;misfirePolicy-MISFIRE_POLICY;concurrent-CONCURRENT;status-STATUS;createBy-CREATE_BY;createTime-CREATE_TIME;remark-REMARK;projectId-PROJECT_ID;extra-EXTRA";
            List<WcpJob> jobList=WcpDataBase.query("wcp",sql,new Object[]{},WcpJob.class,alias);
            if(jobList==null ){
                jobList=new ArrayList<>();
            }
        //    jobList.addAll(TimerCacheList);
            for (WcpJob job : jobList)
            {
                ScheduleUtils.createScheduleJob(scheduler, job);
            }
        }

    }
}
