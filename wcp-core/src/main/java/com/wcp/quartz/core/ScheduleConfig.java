package com.wcp.quartz.core;

import com.alibaba.druid.pool.DruidDataSource;
import com.wcp.utils.ToolSpring;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import java.util.Properties;

/**
 * 1. @Description 定时任务配置（单机部署建议删除此类和qrtz数据库表，默认走内存会最高效）
 * 2. <AUTHOR>
 * 3. @Date 2024/6/20 10:39
 */
@Slf4j
@Configuration
public class ScheduleConfig {
    @Value("${wcp.quartz-auto-start}")
    public  Boolean quartzAutoStart;
    private final Environment environment;

    public ScheduleConfig(Environment environment) {
        this.environment = environment;
    }
    @Bean
    public SchedulerFactoryBean schedulerFactoryBean()
    {
        //Environment bean = ToolSpring.getBean(Environment.class);
        String url = environment.getProperty("spring.datasource.url");
        String driver = environment.getProperty("spring.datasource.driver-class-name");
        String username =environment.getProperty("spring.datasource.username");
        String password = environment.getProperty("spring.datasource.password");
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driver);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setDataSource(dataSource);

        // quartz参数
        Properties prop = new Properties();
        prop.put("org.quartz.scheduler.instanceName", "WcpScheduler");
        prop.put("org.quartz.scheduler.instanceId", "AUTO");
        // 线程池配置
        prop.put("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
        prop.put("org.quartz.threadPool.threadCount", "20");
        prop.put("org.quartz.threadPool.threadPriority", "5");
        // JobStore配置
        prop.put("org.quartz.jobStore.class", "org.springframework.scheduling.quartz.LocalDataSourceJobStore");
        // 集群配置
        prop.put("org.quartz.jobStore.isClustered", "true");
        prop.put("org.quartz.jobStore.clusterCheckinInterval", "15000");
        prop.put("org.quartz.jobStore.maxMisfiresToHandleAtATime", "10");
        prop.put("org.quartz.jobStore.txIsolationLevelSerializable", "true");

        // sqlserver 启用
        // prop.put("org.quartz.jobStore.selectWithLockSQL", "SELECT * FROM {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?");
        prop.put("org.quartz.jobStore.misfireThreshold", "12000");
        prop.put("org.quartz.jobStore.tablePrefix", "WCP_QRTZ_");
        factory.setQuartzProperties(prop);

        factory.setSchedulerName("WcpScheduler");
        // 延时启动
        factory.setStartupDelay(1);
        factory.setApplicationContextSchedulerContextKey("applicationContextKey");
        // 可选，QuartzScheduler
        // 启动时更新己存在的Job，这样就不用每次修改targetObject后删除qrtz_job_details表对应记录了
        factory.setOverwriteExistingJobs(true);
        // 设置自动启动，默认为true
        factory.setAutoStartup(quartzAutoStart);

        return factory;
    }
}
