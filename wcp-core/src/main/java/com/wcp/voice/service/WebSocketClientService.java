package com.wcp.voice.service;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;

import java.util.concurrent.CountDownLatch;

public class WebSocketClientService extends WebSocketClient {
    private String serverResponse;
    private CountDownLatch latch;

    public WebSocketClientService(URI serverUri) {
        super(serverUri);
        this.latch = new CountDownLatch(1); // 计数器，初始为1
    }

    @Override
    public void onOpen(ServerHandshake handshakeData) {
        System.out.println("WebSocket 连接打开.");
    }

    @Override
    public void onMessage(String message) {
        this.serverResponse = message;
        System.out.println("从服务器接收到的数据: " + message);
        latch.countDown();  // 接收到消息后，减少计数器
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("WebSocket 连接关闭: " + reason);
    }

    @Override
    public void onError(Exception ex) {
        ex.printStackTrace();
    }

    public String getServerResponse() {
        try {
            latch.await();  // 等待直到接收到消息
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return serverResponse;  // 返回收到的消息
    }
}

