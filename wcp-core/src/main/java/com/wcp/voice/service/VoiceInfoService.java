package com.wcp.voice.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.http.HttpStatus;
import com.wcp.voice.config.WebSocketConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Map;

import static com.wcp.thread.WcpThreadLocal.getRequest;
import static com.wcp.thread.WcpThreadLocal.getResponse;

/**
 * @Author: 彭潮
 * @CreateTime: 2024-12-06
 * @Description:
 */

@ApiGroup(value = "应用模块语音服务", serviceKey = "voice", classify = "voice", classifyName = "应用模块语音服务")
@Service
public class VoiceInfoService {

    @Autowired
    private WebSocketConfig webSocketConfig;

    @Value("${service.text-to-voice.url:http://localhost:8502/v1/audio/speech}")
    private String apiUrl;

    @ApiService(value = "语音转文字", serviceKey = "speechToText", signType = 0, notes = "语音转文字")
    public void speechToText(Map<String, Object> result, JSONObject object) throws IOException {
        // 获取请求域
        HttpServletRequest request = getRequest(object);
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        //获取文件
        MultipartFile audioData = multiRequest.getFile("audioData");
        //判断音频文件是否存在
        if (audioData == null || audioData.isEmpty()) {
            result.put(HttpStatus.CODE, HttpStatus.ERROR);
            result.put(HttpStatus.MSG, "未收到音频数据");
            return;
        }

        //将音频文件转为音频流
        byte[] audioBytes = audioData.getBytes();

        try {
            // 使用读取到的 websocketUrl 连接到服务端
            WebSocketClientService clientService = new WebSocketClientService(new URI(webSocketConfig.getUrl()));
            clientService.connectBlocking();  // 阻塞直到连接建立

            // 将音频数据发送到 WebSocket 服务端
            clientService.send(audioBytes);

            // 获取服务端返回的结果，等待消息处理完成
            String response = clientService.getServerResponse();

            if (response == null || response.isEmpty()) {
                result.put(HttpStatus.CODE, HttpStatus.ERROR);
                result.put(HttpStatus.MSG, "未接收到服务器返回的转写结果");
            } else {
                result.put(HttpStatus.CODE, HttpStatus.SUCCESS);
                result.put(HttpStatus.DATA, response);  // 返回转写的文本结果
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put(HttpStatus.CODE, HttpStatus.ERROR);
            result.put(HttpStatus.MSG, "请求发生错误: " + e.getMessage());
        }
    }


    @ApiService(value = "文字转语音", serviceKey = "textToSpeech", signType = 0, notes = "文字转语音")
    public void textToSpeech(Map<String, Object> result, JSONObject object) throws IOException {
        String input = object.getString("input");
        String voice = object.getString("voice");
        String prompt = object.getString("prompt");
        String language = object.getString("language");
        Integer speed = object.getInteger("speed");
        String response_format = object.getString("response_format");
        String model = object.getString("model");

        // 创建一个新的 JSONObject 作为请求体传递给服务端
        JSONObject requestBody = new JSONObject();
        requestBody.put("input", input);
        requestBody.put("voice", voice);
        requestBody.put("prompt", prompt);
        requestBody.put("language", language);
        requestBody.put("speed", speed);
        requestBody.put("response_format", response_format);
        requestBody.put("model", model);

        byte[] audioData = null;

        try {
            // 使用配置的 URL
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "audio/wav");
            connection.setDoOutput(true);

            // 将 JSON 请求体发送到服务端
            try (OutputStream os = connection.getOutputStream()) {
                byte[] inputData = requestBody.toString().getBytes("utf-8");
                os.write(inputData, 0, inputData.length);
            }

            //获取状态码
            int responseCode = connection.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream inputStream = connection.getInputStream()) {
                    // 将响应内容读取为字节数组
                    audioData = inputStream.readAllBytes();
                }
            }
            connection.disconnect();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 如果成功获取音频文件，将其作为 ByteArrayResource 返回
        if (audioData != null) {
            HttpServletResponse response = getResponse(object);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("out.wav", "UTF-8"));
            response.setHeader("fileName", URLEncoder.encode("out.wav", "UTF-8"));
            ServletOutputStream servletOutputStream = response.getOutputStream();
            servletOutputStream.write(audioData);
            servletOutputStream.flush();
            servletOutputStream.close();
        }
    }
}
