package com.wcp.voice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WebSocketConfig {
    @Value("${service.voice-to-text.url:ws://localhost:8002}")
    private String url;
    @Value("${service.voice-to-text.timeout:5000}")
    private int timeout;
    @Value("${service.voice-to-text.reconnect-interval:3000}")
    private int reconnectInterval;
    @Value("${service.voice-to-text.retries:3}")
    private int retries;

    // Getters and setters
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public int getReconnectInterval() {
        return reconnectInterval;
    }

    public void setReconnectInterval(int reconnectInterval) {
        this.reconnectInterval = reconnectInterval;
    }

    public int getRetries() {
        return retries;
    }

    public void setRetries(int retries) {
        this.retries = retries;
    }
}
