package com.wcp.platform;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.minio.MinioService;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/5/6 11:46
 */
@ApiGroup(value = "发布的资源文件", serviceKey = "resource", classify = "resource", classifyName = "发布的资源文件")

public class ResourceService {
    @ApiService(value = "生成签名", serviceKey = "generate-file-url", signType = 0, notes = "生成签名",scaner=false)
    public void generateFileUrl(Map<String, Object> result, JSONObject object) {
        try {
            String objectName=object.getString("objectName");
            String projectId=WcpThreadLocal.getProjectId();
            String file="ebi/project/"+projectId+"/"+objectName;
            String url= MinioService.getPresignedObjectUrl(file);
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("url",url);
            result.put("data",jsonObject);
        } catch (Exception e) {
            throw new ServiceException("生成签名链接失败");
        }
    }
}
