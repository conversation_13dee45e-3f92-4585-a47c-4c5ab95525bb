package com.wcp.platform;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.utils.GzipCompress;
import dm.jdbc.driver.DmdbBlob;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description: 应用模块查询项目图元
 * Author: qianchao
 * Date: 2024/3/15 10:48
 */
@ApiGroup(value = "图元服务", serviceKey = "pm-gcdata", classify = "pm-gcdata", classifyName = "图元服务")
public class GraphDataService {

    /**
     * 项目项目图元数据
     * @param result
     * @param object
     * @throws Exception
     */
    @ApiService(value = "获取项目图元数据", serviceKey = "query-graph", signType = 0, notes = "获取项目图元数据", params = {
            @ApiParam(value = "图元点号", name = "graphId", clazz = String.class, paramType = "url", required = true),
            @ApiParam(value = "数据类型 gzip/json", name = "encoding", clazz = String.class, paramType = "url", required = true) })
    public void getProjectGraph(Map<String, Object> result, JSONObject object) throws Exception {
        String rootId=object.getString("rootId");
        String encoding=object.getString("encoding");
        String sql="SELECT  GRAPH_ID ,GRAPH_CELL FROM WCP_SP_DATA WHERE ROOTID=?";
        Map<String,Object> data=WcpDataBase.querySingleMap("wcp",sql,new Object[]{rootId},null);
        if(data!=null){
            byte[] graphCell = null;
            Object blobObj = data.get("GRAPH_CELL");
            if (blobObj instanceof DmdbBlob) {
                DmdbBlob blob = (DmdbBlob) blobObj;
                try (InputStream is = blob.getBinaryStream();
                     ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = is.read(buffer)) != -1) {
                        baos.write(buffer, 0, len);
                    }
                    graphCell = baos.toByteArray();
                }
            }else{
                graphCell= (byte[]) data.get("GRAPH_CELL");
            }


            //图元存储是进行压缩后的二进制，需要转换
            String graphData = new String(graphCell, StandardCharsets.UTF_8);
            //如果是gzip就返回压缩后丶字符串，因为压缩后的字符串小，并且传输效率快。如果是json就返回明文
            result.put("data","gzip".equals(encoding)?graphData: GzipCompress.uncompress(graphData));
        }else{
            result.put("data",null);
        }
    }

    @ApiService(value = "获取项目路由数据", serviceKey = "get-route", signType = 0, notes = "获取项目路由数据")
    public void getRouter(Map<String, Object> result, JSONObject object){
        String router=object.getString("router");
        if (router == null || !router.startsWith("/")) {
            throw new ServiceException("非法路径");
        }
        String[] parts = router.substring(1).split("/", 2); // 去掉开头的斜杠，再分割成两段
        String prefix = parts[0];
        String subPath = "/" + parts[1]; // 保留后续路径前缀斜杠
        String sql="SELECT B.ID,B.PROJECT_ID,B.ROUTER,B.TITLE,B.ICON,B.CREATE_TIME,B.ACCOUNT,B.HPOS,B.VPOS,B.ADAPOS,B.ISVERIFY,B.REROUTER,B.ROOTID FROM WCP_SP_PROJECT  A    " +
                "  LEFT JOIN  WCP_SP_PAGE  B ON A.PROJECT_ID = B.PROJECT_ID " +
                "  WHERE B.ROUTER = ? AND A.PREFIX=? ";
        Map<String,Object> data=WcpDataBase.querySingleMap("wcp",sql,new Object[]{subPath,prefix},"id-ID;projectId-PROJECT_ID;router-ROUTER;title-TITLE;icon-ICON;" +
                "createTime-CREATE_TIME;account-ACCOUNT;hpos-HPOS;vpoS-VPOS;adapos-ADAPOS;isverify-ISVERIFY;rerouter-REROUTER;rootId-ROOTID");
        result.put("data",data);
    }
    @ApiService(value = "获取项目接口", serviceKey = "request/list", signType = 0, notes = "获取项目接口")
    public void getRequestList(Map<String, Object> result, JSONObject object){
        //String id=object.getString("id");
        String projectId=object.getString("projectId");
        String sql="SELECT ID,SETTING FROM WCP_SP_REQUEST WHERE  PROJECT_ID = ?";
        List<Map<String,Object>> data=WcpDataBase.queryMap("wcp",sql,new Object[]{projectId},"id-ID;setting-SETTING");
        List<JSONObject> list=new ArrayList<>();
        if(data!=null && data.size()>0){
            for(Map<String, Object> item : data) {
                if(item.get("setting")!=null){
                    JSONObject tmp=JSONObject.parseObject(item.get("setting").toString());
                    tmp.put("id",item.get("id"));
                    list.add(tmp);
                }

            }
        }
        JSONObject json=new JSONObject();
        json.put("rows",list);
        json.put("total",list.size());
        result.put("data",json);
    }

    @ApiService(value = "获取项目前缀", serviceKey = "prefix/list", signType = 0, notes = "获取项目前缀")
    public void getPrefixList(Map<String, Object> result, JSONObject object){
        String projectId=object.getString("projectId");
        String sql="SELECT ID,PREFIX_NAME,PREFIX,STATUS FROM WCP_SP_PROJECT_PREFIX WHERE  PROJECT_ID = ?";
        List<Map<String,Object>> data=WcpDataBase.queryMap("wcp",sql,new Object[]{projectId},"id-ID;prefixName-PREFIX_NAME;prefix-PREFIX;status-STATUS");
        List<JSONObject> list=new ArrayList<>();
        if(data!=null && data.size()>0){
            for(Map<String, Object> item : data) {
                JSONObject tmp = new JSONObject();
                tmp.put("prefix",item.get("prefix"));
                tmp.put("id",item.get("id"));
                tmp.put("name",item.get("prefixName"));
                tmp.put("status",item.get("status"));
                list.add(tmp);

            }
        }
        JSONObject json=new JSONObject();
        json.put("rows",list);
        json.put("total",list.size());
        result.put("data",json);
    }

    @ApiService(value = "获取拓扑图列表", serviceKey = "topology/query-idxs", signType = 0, notes = "获取拓扑图列表")
    public void getTopologyIdxs(Map<String, Object> result , JSONObject object){
        String projectId = object.getString("projectId");
        String sql = "SELECT PROJECT_ID,ID,NAME FROM WCP_GC_TOPOLOGY WHERE PROJECT_ID = ?";
        List<Map<String, Object>> data = WcpDataBase.queryMap("wcp", sql, new Object[]{projectId}, null);
        JSONObject json = new JSONObject();
        json.put("rows",data);
        json.put("total",data.size());
        result.put("data",json);

    }
}
