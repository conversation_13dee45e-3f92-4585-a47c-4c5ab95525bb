package com.wcp.platform;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.data.PackageUtil;
import com.wcp.service.AcommonService;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

/**
 * Description: 加载所有系统接口地址
 * Author: qianchao
 * Date: 2024/2/1 13:47
 */
@ApiGroup(value = "系统service", serviceKey = "system", classify = "system", classifyName = "系统service")
public class SystemService{

    @ApiService(value = "获取系统所有接口地址", serviceKey = "scanRequests", signType = 2, notes = "获取系统所有接口地址",scaner=false)
    public  void scanRequests(Map<String, Object> result, JSONObject object) {
        Map<String, JSONArray> stringJSONArrayMap= AcommonService.interfaceReuqestMap;
        //获取需要排除的模块
        Map<String,JSONObject> urlMap= AcommonService.urlMap;
        JSONArray resultArray=new JSONArray();
        for (Map.Entry<String, JSONArray> entry : stringJSONArrayMap.entrySet()) {
            String key = entry.getKey();
            JSONObject modelJson=urlMap.get(key);
            String desc=modelJson.getString("desc");
            JSONArray jsonArray = entry.getValue();
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("url",key);
            jsonObject.put("desc",desc);
            jsonObject.put("params",jsonArray);
            resultArray.add(jsonObject);
        }
        result.put("data",resultArray);
    }
}
