package com.wcp.platform;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统图标接口集合
 * <AUTHOR>
 * @date 2021.12.30
 */
@ApiGroup(value = "系统图标接口集合", serviceKey = "systemIcon", classify = "systemIcon", classifyName = "系统图标")

public class SystemIconService{
	@ApiService(value = "查询指定分组和名称的系统图标", serviceKey = "queryIconByGroupAndName", signType = 0, notes = "返回指定分组和名称的系统图标.", params = {
			@ApiParam(value = "分组标志", name = "group", clazz = String.class, paramType = "body", required = true),
			@ApiParam(value = "图标名称", name = "name", clazz = String.class, paramType = "body", required = true) })
	public void queryIconByGroupAndName(Map<String, Object> result, JSONObject object) {
		String viewGroup = object.getString("group");
		String viewName = object.getString("name");
		// 查询失败判断
		if (viewGroup == null || viewGroup.trim().equals("") || viewName == null || viewName.trim().equals("")) {
			throw  new ServiceException("查询异常");
		}
		String sql="SELECT A.GROUP_ID, A.ICON_NAME, A.ICON, A.ICON_TYPE FROM WCP_SP_ICON A LEFT JOIN WCP_SP_ICONGROUP B ON A.GROUP_ID=B.GROUP_ID WHERE A.GROUP_ID = ? AND A.ICON_NAME = ?";

		// 不用参数，查询所有的系统图标
		List<Map<String, Object>> tempIcons = WcpDataBase.queryMap("wcp",sql,new Object[] { viewGroup, viewName },null);;
		// 临时处理，记录分组是否已创建
		if (tempIcons.size() == 0) {
			throw  new ServiceException("未找到指定分组和名称的图标");
		}
		Map<String, Object> tempMap = tempIcons.get(0);
		String group = (String) tempMap.get("GROUP_ID");
		//String topic = (String) tempMap.get("TOPIC");
		String name = (String) tempMap.get("ICON_NAME");
		String icon = (String) tempMap.get("ICON");
		String type = (String) tempMap.get("ICON_TYPE");
		Map<String, Object> tempIcon = new HashMap<String, Object>();
		tempIcon.put("group", group);
		tempIcon.put("name", name);
		tempIcon.put("icon", icon);
		tempIcon.put("type", type);
		result.put("data", tempIcon);
		result.put("success", true);
	}
}
