package com.wcp.db.dbconfig;

public class DataBaseDao {
	/** 数据库名称 */
	public String name="";
	/** 数据库连接服务名称 */
	public String service="";
	/** 数据库别名 */
	public String alias="";
	/** 数据库驱动 */
	public String driver="";
	/** 数据库连接地址 */
	public String url="";
	/** 数据库连接用户名 */
	public String username="";
	/** 数据库连接密码 */
	public String password="";
	/** 数据库最大连接数 */
	public String maxclients="";
	/** 数据库版本号 */
	public String version="";
	/** 数据库字符集编码 */
	public String dbcharact="";
	/** 数据库使用字符集编码 */
	public String tocharact="";
	/** 数据库事务隔离级别 */
	public String translevel="";
	/** 数据库查询测试语句 */
	public String checksql="";
	/** 数据库描述 */
	public String desc="";
	/** 数据库图片地址 */
	public String icon="";
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getService() {
		return service;
	}
	public void setService(String service) {
		this.service = service;
	}
	public String getAlias() {
		return alias;
	}
	public void setAlias(String alias) {
		this.alias = alias;
	}
	public String getDriver() {
		return driver;
	}
	public void setDriver(String driver) {
		this.driver = driver;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getMaxclients() {
		return maxclients;
	}
	public void setMaxclients(String maxclients) {
		this.maxclients = maxclients;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getDbcharact() {
		return dbcharact;
	}
	public void setDbcharact(String dbcharact) {
		this.dbcharact = dbcharact;
	}
	public String getTocharact() {
		return tocharact;
	}
	public void setTocharact(String tocharact) {
		this.tocharact = tocharact;
	}
	public String getTranslevel() {
		return translevel;
	}
	public void setTranslevel(String translevel) {
		this.translevel = translevel;
	}
	public String getChecksql() {
		return checksql;
	}
	public void setChecksql(String checksql) {
		this.checksql = checksql;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
}
