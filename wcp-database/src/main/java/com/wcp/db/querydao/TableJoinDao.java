package com.wcp.db.querydao;

import java.util.List;

/**
 * 查询级联组织
 * <AUTHOR>
 * @date 2021.09.04
 */
public class TableJoinDao {
	/** JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL JOIN */
	private String joinType;
	/** 左边表格名称 */
	//private String leftTable;
	/** 右边表格名称 */
	private String rightTable;
	/** 左边和右边表格的字段,数组长度只能为2 */
	private List<String[]> joinFields;
	
	public TableJoinDao() {
		
	}
	
	/**
	 * 构建级联查询
	 * @param joinType      级联类型
	 * @param rightTable    右边表格名称
	 * @param joinFields    级联字段集合
	 */
	public TableJoinDao(String joinType,  String rightTable, List<String[]> joinFields) {
		this.joinType = joinType;
		this.rightTable = rightTable;
		this.joinFields = joinFields;
	}
	
	/*public TableJoinDao(String joinType, String leftTable, String rightTable, List<String[]> joinFields) {
		this.joinType = joinType;
		this.leftTable = leftTable;
		this.rightTable = rightTable;
		this.joinFields = joinFields;
	}*/
	
	public String getJoinType() {
		return joinType;
	}
	public void setJoinType(String joinType) {
		this.joinType = joinType;
	}
	/*public String getLeftTable() {
		return leftTable;
	}
	public void setLeftTable(String leftTable) {
		this.leftTable = leftTable;
	}*/
	public String getRightTable() {
		return rightTable;
	}
	public void setRightTable(String rightTable) {
		this.rightTable = rightTable;
	}
	public List<String[]> getJoinFields() {
		return joinFields;
	}
	public void setJoinFields(List<String[]> joinFields) {
		this.joinFields = joinFields;
	}

	/*@Override
	public String toString() {
		return "TableJoinDao [joinType=" + joinType  + ", rightTable=" + rightTable
				+ ", joinFields=" + joinFields + "]";
	}*/
	
}
