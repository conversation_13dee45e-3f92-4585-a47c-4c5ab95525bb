package com.wcp.db.querydao;

/**
 * 查询分页组织
 * <AUTHOR>
 * @date 2021.09.04
 */
public class LimitDao {
	/** 分页开始的索引 */
	private int beginIndex = 0;
	/** 分页查询的数目 */
	private int count = 1;
	
	/**
	 * 构造分页查询
	 */
	public LimitDao() {
		this.beginIndex = 0;
		this.count = 1;
	}
	
	/**
	 * 构造分页查询
	 * @param count        分页查询的数目
	 */
	public LimitDao(int count) {
		this.beginIndex = 0;
		this.count = count;
	}
	
	/**
	 * 构造分页查询
	 * @param beginIndex   分页开始的索引
	 * @param count        分页查询的数目
	 */
	public LimitDao(int beginIndex, int count) {
		this.beginIndex = beginIndex;
		this.count = count;
	}

	public int getBeginIndex() {
		return beginIndex;
	}

	public void setBeginIndex(int beginIndex) {
		this.beginIndex = beginIndex;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}
}
