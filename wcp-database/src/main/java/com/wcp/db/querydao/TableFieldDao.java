package com.wcp.db.querydao;

/**
 * 查询字段组织
 * <AUTHOR>
 * @date 2021.09.04
 */
public class TableFieldDao {
	/** 查询字段的表名 */
	private String tableName;
	/** 查询字段 */
	private String field;
	/** 查询字段别名 */
	private String alias;
	/** 聚合函数 */
	private String aggFunc;
	/** 数据库中字段类型 */
	private String fieldType;
	/** 查询后的字段类型 */
	private String fieldShowType;
	
	public TableFieldDao() {
		
	}
	
	/**
	 * 构建表的查询字段
	 * @param tableName      字段所在的表名
	 * @param field          查询字段
	 * @param alias		            查询字段别名
	 * @param aggFunc        聚合函数
	 * @param fieldType      数据库中字段类型
	 * @param fieldShowType  查询后的字段类型
	 */
	public TableFieldDao(String tableName, String field, String alias, String aggFunc, String fieldType, String fieldShowType) {
		this.tableName = tableName;
		this.field = field;
		this.alias = alias;
		this.aggFunc = aggFunc;
		this.fieldType = fieldType;
		this.fieldShowType = fieldShowType;
	}
	
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public String getField() {
		return field;
	}
	public void setField(String field) {
		this.field = field;
	}
	public String getAlias() {
		return alias;
	}
	public void setAlias(String alias) {
		this.alias = alias;
	}
	public String getAggFunc() {
		return aggFunc;
	}
	public void setAggFunc(String aggFunc) {
		this.aggFunc = aggFunc;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public String getFieldShowType() {
		return fieldShowType;
	}
	public void setFieldShowType(String fieldShowType) {
		this.fieldShowType = fieldShowType;
	}
	
	
}