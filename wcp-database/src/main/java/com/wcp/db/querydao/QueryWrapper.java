package com.wcp.db.querydao;

import com.baomidou.mybatisplus.core.enums.SqlLike;
import com.wcp.db.enums.SqlKeyword;
import com.wcp.db.jdbc.WcpDataBase;
import lombok.*;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class QueryWrapper {

    /**
     * 需要查询(显示)的字段
     */
    List<String> queryTableField;


    List<String> orand = new ArrayList<>();

    /**
     * 存储的条件
     */
    List<FieldParameterInfo> fieldParameterInfos ;
    /**
     * 存储like的条件
     */
    List<FieldParameterInfo> fieldParameterInfosToLike;
    /**
     * 表名
     */
    String tableName;
    /**
     * 排序
     */
    List<String> order;

    /**
     * 排序规则 正序或者倒叙
     */
    String orderType;

    /**
     * 分组
     */
    List<String> group;

    /**
     * 传入查询参数集合
     */
    List<Object> paramList;

    /**
     * SQL语句
     */
    StringBuilder sql;

    /**
     * 数据库类型名称
     */
    String dataBaseName;

    public QueryWrapper() {
        queryTableField = new ArrayList<>();
        fieldParameterInfos = new ArrayList<>();
        fieldParameterInfosToLike = new ArrayList<>();
        order = new ArrayList<>();
        group = new ArrayList<>();
        paramList = new ArrayList<>();
        sql = new StringBuilder();
    }
    /**
     *  规则往后延申  最后是建立一个枚举  来存储 比如说 =  != like这些
     *
     *  QueryWrapper 对象的构造器 空参 需要初始化
     *
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    @ToString
    public class FieldParameterInfo {
        /**
         * 字段
         */
        private String field;

        /**
         * 参数
         */
        private Object value;

        /**
         * 类型(AND,OR)
         */
        private SqlKeyword type;

        /**
         * 执行条件  如果为 true 就执行
         */
        private boolean condition;

        /**
         * 比较规则
         */
        private SqlKeyword rule;

        /**
         * like的模糊规则，左边或者右边或者中间
         */
        private SqlLike likeRule;
        /**
         * in和not in的集合
         */
        List<Object> inList;
    }


    /**
     * 将需要查询的字段添加至tableField集合中
     *
     * @param field 需要查询的字段
     * @return 当前对象
     */
    public QueryWrapper select(String... field) {
        this.queryTableField.addAll(Arrays.asList(field));
        return this;
    }


    public QueryWrapper setTableName(String tableName){
        this.tableName = tableName;
        return this;
    }

    /**
     * 将多个排序的字段进行添加
     *
     */
    public QueryWrapper setOrderBy(boolean isAsc,String... field){
        this.orderType = isAsc ? "ASC" : "DESC";
        this.order.addAll(Arrays.asList(field));
        return this;
    }

    /**
     * 将多个分组的字段进行添加
     *
     */
    public QueryWrapper setGroupBy(String... field){
        this.group.addAll(Arrays.asList(field));
        return this;
    }


    /**
     * 添加条件
     *
     * @param condition 是否执行
     * @param field     字段名称
     * @param parameter 参数
     * @param type      类型 1 AND 2 OR
     * @param rule      条件规则
     *                  1 =
     *                  2 !=
     * @return 当前对象
     */
    public QueryWrapper where(boolean condition, String field, Object parameter, SqlKeyword type, SqlKeyword rule) {
        FieldParameterInfo fieldParameterInfo = new FieldParameterInfo();
        fieldParameterInfo.setField(field);
        fieldParameterInfo.setValue(parameter);
        fieldParameterInfo.setCondition(condition);
        fieldParameterInfo.setType(type);
        fieldParameterInfo.setRule(rule);
        this.fieldParameterInfos.add(fieldParameterInfo);
        return this;
    }

    public QueryWrapper where(boolean condition, String field, SqlKeyword type, SqlKeyword rule) {
        FieldParameterInfo fieldParameterInfo = new FieldParameterInfo();
        fieldParameterInfo.setField(field);
        fieldParameterInfo.setCondition(condition);
        fieldParameterInfo.setType(type);
        fieldParameterInfo.setRule(rule);
        this.fieldParameterInfos.add(fieldParameterInfo);
        return this;
    }
    /**
     * 用于存储in相关的条件
     * @param condition
     * @param field
     * @param type
     * @param rule
     * @return
     */
    public QueryWrapper where(boolean condition, String field, SqlKeyword type, SqlKeyword rule,Object... parameters) {
        FieldParameterInfo fieldParameterInfo = new FieldParameterInfo();
        fieldParameterInfo.setField(field);
        fieldParameterInfo.setCondition(condition);
        fieldParameterInfo.setType(type);
        fieldParameterInfo.setRule(rule);
        fieldParameterInfo.setInList(Arrays.asList(parameters));
        this.fieldParameterInfos.add(fieldParameterInfo);
        return this;
    }

    /**
     * 用于存储like相关的条件
     * @param condition
     * @param field
     * @param parameter
     * @param type
     * @param rule
     * @param likeRule
     * @return
     */
    public QueryWrapper where(boolean condition, String field, Object parameter, SqlKeyword type, SqlKeyword rule,SqlLike likeRule) {
        FieldParameterInfo fieldParameterInfo = new FieldParameterInfo();
        fieldParameterInfo.setField(field);
        fieldParameterInfo.setValue(parameter);
        fieldParameterInfo.setCondition(condition);
        fieldParameterInfo.setType(type);
        fieldParameterInfo.setRule(rule);
        fieldParameterInfo.setLikeRule(likeRule);
        this.fieldParameterInfosToLike.add(fieldParameterInfo);
        return this;
    }


    /**
     * 添加等于条件 AND
     * 等于 in
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @return 当前对象
     */
    public QueryWrapper in(boolean condition, String field, Object... parameters) {
        return where(condition, field, SqlKeyword.AND, SqlKeyword.IN, parameters);
    }
    public QueryWrapper in(String field, Object... parameters) {
        return in(true, field, parameters);
    }

    /**
     * 添加等于条件 AND
     * 等于 NOT IN
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @return 当前对象
     */
    public QueryWrapper notIn(boolean condition, String field, Object... parameters) {
        return where(condition, field, SqlKeyword.AND, SqlKeyword.NOT_IN, parameters);
    }
    public QueryWrapper notIn(String field, Object... parameters) {
        return notIn(true, field, parameters);
    }


    /**
     * 添加等于条件 AND
     * 等于 =
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper eq(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.EQ);
    }
    public QueryWrapper eq(String field, Object parameter) {
        return eq(true, field, parameter);
    }

    /**
     * 添加等于条件 AND
     * 不等于 <>
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper ne(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.NE);
    }
    public QueryWrapper ne(String field, Object parameter) {
        return ne(true, field, parameter);
    }

    /**
     * 添加等于条件 AND
     * 不等于 >
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper gt(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.GT);
    }
    public QueryWrapper gt(String field, Object parameter) {
        return gt(true, field, parameter);
    }

    /**
     * 添加等于条件 AND
     * 不等于 >=
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper ge(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.GE);
    }
    public QueryWrapper ge(String field, Object parameter) {
        return ge(true, field, parameter);
    }

    /**
     * 添加等于条件 AND
     * 不等于 <
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper lt(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.LT);
    }
    public QueryWrapper lt(String field, Object parameter) {
        return lt(true, field, parameter);
    }

    /**
     * 添加等于条件 AND
     * 不等于 <=
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper le(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.LE);
    }
    public QueryWrapper le(String field, Object parameter) {
        return le(true, field, parameter);
    }

    /**
     * 默认like查询为左右全部模糊查询
     * @param condition
     * @param field
     * @param parameter
     * @return
     */
    public QueryWrapper like(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.LIKE, SqlLike.DEFAULT);
    }

    public QueryWrapper notLike(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.NOT_LIKE, SqlLike.DEFAULT);
    }

    public QueryWrapper likeLeft(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.LIKE, SqlLike.LEFT);
    }

    public QueryWrapper likeRight(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.LIKE, SqlLike.RIGHT);
    }

    public QueryWrapper notLikeLeft(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.NOT_LIKE, SqlLike.LEFT);
    }

    public QueryWrapper notLikeRight(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.AND, SqlKeyword.NOT_LIKE, SqlLike.RIGHT);
    }

    /**
     * 条件IS NULL
     * AND
     * @param condition
     * @param field
     * @return
     */
    public QueryWrapper isNull(boolean condition, String field) {
        return where(condition, field, SqlKeyword.AND, SqlKeyword.IS_NULL);
    }
    /**
     * 条件IS NOT NULL
     * AND
     * @param condition
     * @param field
     * @return
     */
    public QueryWrapper isNotNull(boolean condition, String field) {
        return where(condition, field, SqlKeyword.AND, SqlKeyword.IS_NOT_NULL);
    }

    /**
     * 添加等于条件 AND
     * 等于 in
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @return 当前对象
     */
    public QueryWrapper orIn(boolean condition, String field, Object... parameters) {
        return where(condition, field, SqlKeyword.OR, SqlKeyword.IN, parameters);
    }
    public QueryWrapper orIn(String field, Object... parameters) {
        return orIn(true, field, parameters);
    }

    /**
     * 添加等于条件 AND
     * 等于 NOT IN
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @return 当前对象
     */
    public QueryWrapper orNotIn(boolean condition, String field, Object... parameters) {
        return where(condition, field, SqlKeyword.OR, SqlKeyword.NOT_IN, parameters);
    }
    public QueryWrapper orNotIn(String field, Object... parameters) {
        return orNotIn(true, field, parameters);
    }

    /**
     * 添加等于条件 OR
     * 等于 =
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper orEq(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.EQ);
    }
    public QueryWrapper orEq(String field, Object parameter) {
        return orEq(true, field, parameter);
    }

    /**
     * 添加等于条件 OR
     * 不等于 <>
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper orNe(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.NE);
    }
    public QueryWrapper orNe(String field, Object parameter) {
        return orNe(true, field, parameter);
    }

    /**
     * 添加等于条件 OR
     * 不等于 >
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper orGt(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.GT);
    }
    public QueryWrapper orGt(String field, Object parameter) {
        return orGt(true, field, parameter);
    }

    /**
     * 添加等于条件 OR
     * 不等于 >=
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper orGe(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.GE);
    }
    public QueryWrapper orGe(String field, Object parameter) {
        return orGe(true, field, parameter);
    }

    /**
     * 添加等于条件 OR
     * 不等于 <
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper orLt(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.LT);
    }
    public QueryWrapper orLt(String field, Object parameter) {
        return orLt(true, field, parameter);
    }

    /**
     * 添加等于条件 AND
     * 不等于 <=
     *
     * @param condition 执行条件 如果为true就执行
     * @param field     字段名
     * @param parameter 参数
     * @return 当前对象
     */
    public QueryWrapper orLe(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.LE);
    }
    public QueryWrapper orLe(String field, Object parameter) {
        return orLe(true, field, parameter);
    }

    /**
     * 默认like查询为左右全部模糊查询，中间条件为OR
     * @param condition
     * @param field
     * @param parameter
     * @return
     */
    public QueryWrapper orLike(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.LIKE, SqlLike.DEFAULT);
    }

    public QueryWrapper orNotLike(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.NOT_LIKE, SqlLike.DEFAULT);
    }

    public QueryWrapper orLikeLeft(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.LIKE, SqlLike.LEFT);
    }

    public QueryWrapper orLikeRight(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.LIKE, SqlLike.RIGHT);
    }

    public QueryWrapper orNotLikeLeft(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.NOT_LIKE, SqlLike.LEFT);
    }

    public QueryWrapper orNotLikeRight(boolean condition, String field, Object parameter) {
        return where(condition, field, parameter, SqlKeyword.OR, SqlKeyword.NOT_LIKE, SqlLike.RIGHT);
    }

    /**
     * 条件IS NULL
     * OR
     * @param condition
     * @param field
     * @return
     */
    public QueryWrapper orIsNull(boolean condition, String field) {
        return where(condition, field, SqlKeyword.OR, SqlKeyword.IS_NULL);
    }
    /**
     * 条件IS NOT NULL
     * OR
     * @param condition
     * @param field
     * @return
     */
    public QueryWrapper orIsNotNull(boolean condition, String field) {
        return where(condition, field, SqlKeyword.OR, SqlKeyword.IS_NOT_NULL);
    }


    public String getSql(){
        // 清空 StringBuilder
        this.sql.setLength(0);
        this.sql.append("SELECT ");
        if (this.queryTableField != null&&this.queryTableField.size()!=0){
            this.queryTableField.stream().forEach(f -> {
                this.sql.append(formatString(f)+",");
            });
            this.sql = removeComma(this.sql);
        }else{
            this.sql.append("*");
        }

        this.sql.append(" FROM ").append(this.tableName).append(" WHERE 1=1 ");
        if (!this.fieldParameterInfosToLike.isEmpty()){//判断是否有like条件
            this.fieldParameterInfosToLike.stream().forEach(w -> {
                //添加like的where条件字段
                this.sql.append(w.getType()).append(" ").append(w.getField()).append(" LIKE ? ");
                //like条件的对应值集合
                if(w.getLikeRule().equals(SqlLike.DEFAULT)) {
                    this.paramList.add("'%"+w.getValue()+"%'");
                }else if(w.getLikeRule().equals(SqlLike.LEFT)){
                    this.paramList.add("'%"+w.getValue()+"'");
                }else if(w.getLikeRule().equals(SqlLike.RIGHT)){
                    this.paramList.add("'"+w.getValue()+"%'");
                }
            });
        }
        if(!this.fieldParameterInfos.isEmpty()){//判断普通wehre条件是否存在
            this.fieldParameterInfos.stream().forEach(p -> {
                if (p.getInList()!=null && p.getInList().size()!=0 ){//in的where条件
                    StringBuilder d = new StringBuilder();
                    for (Object obj:p.getInList()) {
                        this.paramList.add(obj);
                        d.append("?,");
                    }
                    d = removeComma(d);
                    this.sql.append(p.getType()).append(" ").append(p.getField()).append(" ").append(p.getRule().getSqlSegment()).append(" (").append(d.toString()).append(") ");

                }else if (p.getValue() == null){//如果不为空则为正常的where条件，为空则是is not null类型条件

                    //where条件字段拼接
                    this.sql.append(p.getType()).append(" ").append(p.getField()).append(" ").append(p.getRule().getSqlSegment());

                }else{//普通where条件拼接
                    //where条件值集合
                    this.paramList.add(p.getValue().toString());
                    //where条件字段拼接
                    this.sql.append(p.getType()).append(" ").append(p.getField()).append(" ").append(p.getRule().getSqlSegment()).append(" ? ");
                }
            });
        }
        if(!this.group.isEmpty()){//添加group by
            this.sql.append(" GROUP BY ");
            this.group.stream().forEach(g -> {
                this.sql.append(g).append(",");
            });
        }
        this.sql = removeComma(this.sql);

        if(!this.order.isEmpty()){//添加order by
            this.sql.append(" ORDER BY ");
            this.order.stream().forEach(o -> {
                this.sql.append(o).append(",");
            });
            this.sql = removeComma(this.sql);
            this.sql.append(" ");
            if (!this.orderType.isEmpty()){
                this.sql.append(this.orderType);
            }
        }

        return sql.toString();
    }

    //去除最后逗号
    public StringBuilder removeComma(StringBuilder sb){
        if (sb.length() > 0 && sb.charAt(sb.length() - 1) == ',') {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb;
    }

    public String formatString(String input) {
        String inputUpper = input.toUpperCase();
        // 匹配是否包含 " as "（注意空格）
        int position = inputUpper.indexOf(" AS ");

        // 如果字符串包含 " as "，则替换为空格前后加上反引号的形式
        if (position != -1) {
             //" as "之前的字符串
            String head = input.substring(0,position);
            //" as "之后的字符串
            String tail = input.substring(position + 4,input.length()).trim();
            switch (this.getDataBaseName()) {

                case "MySQL":  return head + " AS `" +tail + "`";

                case "Oracle": return head + " AS \"" +tail + "\"";

                case "SQL Server": return head + " AS [" +tail + "]";

                case "DM DBMS": return head + " AS \"" +tail + "\"";

                default: return head + " AS " + tail;
            }
        } else { // 如果字符串不包含 " as "，则在字符串前后加上反引号
            switch (this.getDataBaseName()) {

                case "MySQL":   return "`" + input + "`";

                case "Oracle":  return "\"" + input + "\"";

                case "SQL Server": return "[" + input + "]";

                case "DM DBMS": return "\"" + input + "\"";

                default: return  input ;
            }
        }
    }


    public static void main(String[] args) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.setDataBaseName("MySQL");
        List<String> objects = new ArrayList<>();
        queryWrapper.select("asaa As eee", "bbb AS cc", "qqq AS eee","vdfavas").setTableName("ceshi").eq("WCP", 123)
                .orIn("BBB","A","B","C","D")
                .like(true,"fff","v").isNotNull(true,"q")
                .setOrderBy(false,"AAA","BBB").setGroupBy("CCC");

        String tableField = queryWrapper.getSql();
        System.out.println(tableField);

        queryWrapper.paramList.stream().forEach(a ->{
            System.out.println(a);
        });

    }
}

