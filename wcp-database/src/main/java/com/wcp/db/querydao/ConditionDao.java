package com.wcp.db.querydao;

import java.util.List;

/**
 * 查询条件组织
 * <AUTHOR>
 * @date 2021.09.04
 */
public class ConditionDao {
	/** 连接类型(AND,OR) */
	private String linkType;
	/**表名*/
	private String tableName;
	/** 条件字段別名 */
	private String field;
	/** 判断条件(=,<>,>,<,>=,<=,BETWEEN,LIKE,IS NULL,IS NOT NULL,IN,!=) */
	private String judge;
	/** 查询值，如果judge是BETWEEN，则字是一个数组 */
	private Object value;
	/** 聚合函数(COUNT,AVG,MIN,MAX,SUM) */
	private String aggFunc;
	
	/** 附加值 */
	private Object value2;
	/** 括号中的子条件 */
	private List<ConditionDao> childrens;
	
	public ConditionDao() {
		
	}
	
	/**
	 * 构造条件
	 * @param linkType   连接类型(AND,OR)
	 * @param field      条件字段
	 * @param judge      判断条件(=,<>,>,<,>=,<=,BETWEEN,LIKE,IS NULL)
	 * @param value      查询值,如果judge是BETWEEN，则字是一个数组
	 */
	public ConditionDao(String linkType, String field, String judge, Object value) {
		this.linkType = linkType;
		this.field = field;
		this.judge = judge;
		this.value = value;
	}
	
	/**
	 * 构造条件
	 * @param linkType   连接类型(AND,OR)
	 * @param tableName   表名（多表时用，单表不用）
	 * @param field      条件字段
	 * @param judge      判断条件(=,<>,>,<,>=,<=,BETWEEN,LIKE,IS NULL)
	 * @param value      查询值,如果judge是BETWEEN，则字是一个数组
	 * @param aggFunc    聚合函数(COUNT,AVG,MIN,MAX,SUM)
	 */
	public ConditionDao(String linkType, String tableName,String field,  String judge, Object value, String aggFunc) {
		this.linkType = linkType;
		this.tableName = tableName;
		this.field = field;
		this.judge = judge;
		this.value = value;
		this.aggFunc = aggFunc;
	}
	
	/**
	 * 构造条件
	 * @param linkType   连接类型(AND,OR)
	 * @param field      条件字段
	 * @param judge      判断条件(=,<>,>,<,>=,<=,BETWEEN,LIKE,IS NULL)
	 * @param value      查询值,如果judge是BETWEEN，则字是一个数组
	 * @param aggFunc    聚合函数(COUNT,AVG,MIN,MAX,SUM)
	 * @param childrens  括号中的子条件
	 */
	public ConditionDao(String linkType, String field, String judge, Object value, String aggFunc,
			List<ConditionDao> childrens) {
		this.linkType = linkType;
		this.field = field;
		this.judge = judge;
		this.value = value;
		this.aggFunc = aggFunc;
		this.childrens = childrens;
	}

	public String getLinkType() {
		return linkType;
	}

	public void setLinkType(String linkType) {
		this.linkType = linkType;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getField() {
		return field;
	}

	public void setField(String field) {
		this.field = field;
	}

	public String getJudge() {
		return judge;
	}

	public void setJudge(String judge) {
		this.judge = judge;
	}

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public String getAggFunc() {
		return aggFunc;
	}

	public void setAggFunc(String aggFunc) {
		this.aggFunc = aggFunc;
	}

	public Object getValue2() {
		return value2;
	}

	public void setValue2(Object value2) {
		this.value2 = value2;
	}

	public List<ConditionDao> getChildrens() {
		return childrens;
	}

	public void setChildrens(List<ConditionDao> childrens) {
		this.childrens = childrens;
	}
}
