package com.wcp.db.service;

import com.wcp.db.data.SQLExcuteException;
import com.wcp.db.data.TableFieldProp;
import com.wcp.db.data.TableProp;
import com.wcp.db.querydao.*;

import java.rmi.Remote;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;

public interface PServiceRemote extends Remote
{
	/**
	 * 查询表字段属性
	 * @param projectId   项目点号
	 * @param service     服务连接标志
	 * @param types       表类型
	 * @return
	 */
	public List<TableProp> queryTable(String projectId, String service, String[] types) throws RemoteException;
	/**
	 * 查询表字段属性
	 * @param projectId   项目点号
	 * @param service     服务连接标志
	 * @param tableName   表名称
	 * @return
	 */
	public List<TableFieldProp> queryTableField(String projectId, String service, String tableName) throws RemoteException;
	public boolean existTable(String projectId, String service,	String tableNamePattern, String types[]) throws RemoteException;
	public boolean execute(String projectId, String service, String sql, Object[] param) throws RemoteException;
    public boolean executeDDL(String projectId, String service, String sql, Object[] param) throws RemoteException;
	public boolean execute(String projectId, String service, String sql, Object[][] param) throws RemoteException;
	public <T> boolean insert(String projectId, String service, String sql, List<T> param,
			String aliasStr) throws RemoteException;
	public <T> boolean insertOrUpdate(String projectId, String service, String sql, List<T> param,
			Object[] symbols, String aliasStr) throws RemoteException;
	public <T> boolean insertOrUpdate(String projectId, String service, String sql, List<T> param,
			String aliasStr) throws RemoteException;
	public <T> boolean insertMap(String projectId, String service, String sql,
			List<Map<String, Object>> param, String aliasStr) throws RemoteException;
	public <T> boolean insertListMap(String projectId, String service, String tableName,
			List<Map<String, Object>> param) throws RemoteException;
	public <T> List<T> query(String projectId, String service, String sql, Object[] param, 
			Object[] symbols, Class<T> clazz, String aliasStr) throws RemoteException;
	public List<Map<String, Object>> queryMap(String projectId, String service, String sql, 
			Object[] param, Object[] symbols, String aliasStr) throws RemoteException;
	Map<String, Object> queryMapByPaging(String projectId, String service, String sql,
									   Object[] param, Object[] symbols, String aliasStr) throws RemoteException;
	public List<Map<String, Object>> queryLocalDateMap(String projectId, String service, String sql,
											  Object[] param, Object[] symbols, String aliasStr) throws RemoteException;
	 public <T> List<Map<String, Object>> queryListMapBySql(String projectId, String service, String tableName,
				String tableField, String joinSql, String conditionSql, Object[] param) throws RemoteException;
	 public  <T> List<Map<String, Object>> queryDataListByRule(String projectId, String service, List<String> tableList,
				List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins, List<ConditionDao> conditions, List<GroupDao> groups,
				LimitDao limits, List<OrderDao> orders) throws SQLExcuteException; 
	 public  <T> boolean updateListsMap(String projectId, String service, String tableName,List<TableFieldDao> tableFields,
			 List<Map<String, Object>> param, List<ConditionDao> conditions) throws RemoteException;
	 public  <T> boolean insertListsMap(String projectId, String service, String tableName,List<TableFieldDao> tableFields,
			 List<Map<String, Object>> param) throws RemoteException;
	 public  <T> boolean deleteListsMap(String projectId, String service, String tableName,List<TableFieldDao> tableFields,
			 List<ConditionDao> conditions)  throws RemoteException;
	public int queryDataListByRuleCount(String projectId, String service, List<String> tableList,
			List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins, List<ConditionDao> conditions,
			List<GroupDao> groups, LimitDao limits, List<OrderDao> orders);


}
