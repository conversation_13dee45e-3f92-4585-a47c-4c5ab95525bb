package com.wcp.db.jdbc;
import com.wcp.utils.AESUtil;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * jdbc配置服务类
 * <AUTHOR>
 */
@Component
public class WcpJdbcService implements Cloneable
{

	private static Logger logger = Logger.getLogger(WcpJdbcService.class);
	private static ApplicationContext context;

	@Autowired
	public void setApplicationContext(ApplicationContext context) {
		WcpJdbcService.context = context;
	}
	private static Map<String, String> dbMap = new HashMap<String, String>();
	/**
	 * 返回JDBC配置服务，如果没有找到就往默认连接中找
	 * @param service     配置的服务标志
	 * @return
	 */
	public static WcpJdbcService getJdbcService(String projectId, String service) {
		JdbcConfig jdbcConfig = context.getBean(JdbcConfig.class);
			if("wcp".equals(service)) {
				return jdbcConfig.initJdbcService();
			}
			String v = dbMap.get(projectId + service);
			if(v==null){
				Map<String, Object> dataBaseMap=WcpDataBase.queryDataBaseFromDB(projectId,service,true);
				if(dataBaseMap == null && "wcp".equals(service)){
					dbMap.put(projectId + service, "1");
					return jdbcConfig.initJdbcService();
				} else if(dataBaseMap == null) {
					dbMap.put(projectId + service, "1");
				} else {
					WcpJdbcService temJdbcService = new WcpJdbcService();
					temJdbcService.setService(service);
					temJdbcService.setDUrl((String)dataBaseMap.get("url"));
					temJdbcService.setDUser((String)dataBaseMap.get("username"));
					String password=(String)dataBaseMap.get("password");
					temJdbcService.setDPsd(AESUtil.decrypt(password));
					temJdbcService.setDriver((String)dataBaseMap.get("driver"));
					if(dataBaseMap.get("maxclients") == null) {
						temJdbcService.setMaxCon("5");
					} else {
						temJdbcService.setMaxCon(dataBaseMap.get("maxclients") + "");
					}
					temJdbcService.setProject(projectId);
					return temJdbcService;
				}
			}else{
				if("wcp".equals(service)) {
					return jdbcConfig.initJdbcService();
				}
			}

        return null;
	}
	
	/**
	 * 根据数据库驱动字符串获取数据库类型
	 */
	public static String getDatabaseType(String projectId, String service) {
		String type = "";
		WcpJdbcService jdbcService = getJdbcService(projectId, service);
		String driverStr = jdbcService.getDriver();
		if (driverStr.indexOf("oracle") > 0) {
			type = "oracle";
		} else if (driverStr.indexOf("mysql") > 0) {
			type = "mysql";
		} else if (driverStr.indexOf("sqlserver") > 0) {
			type = "sqlserver";
		} else if (driverStr.indexOf("DmDriver") > 0) {
			type = "dm";
		} else {
			type = "oracle";
		}
		return type;
	}
	
	/**
	 * 根据数据库驱动字符串获取数据库名称
	 */
	public static String getDatabaseName(String projectId, String service) {
		String name = "";
		WcpJdbcService jdbcService = getJdbcService(projectId, service);
		String driverStr = jdbcService.getDriver();
		String urlStr = jdbcService.getDUrl();
		if (driverStr.indexOf("oracle") > 0) {
			int lastIndex = urlStr.lastIndexOf(":");
			name = urlStr.substring(lastIndex + 1);
		} else if (driverStr.indexOf("mysql") > 0) {
			int lastIndex = urlStr.lastIndexOf("/");
			int tempIndex = urlStr.indexOf("?");
			if (tempIndex == -1) {
				tempIndex = urlStr.length();
			}
			name = urlStr.substring(lastIndex + 1, tempIndex);
		} else if (driverStr.indexOf("sqlserver") > 0) {
			int lastIndex = urlStr.lastIndexOf("=");
			name = urlStr.substring(lastIndex + 1);
		} else if (driverStr.indexOf("DmDriver") > 0) {
			int lastIndex = urlStr.lastIndexOf("/");
			name = urlStr.substring(lastIndex + 1);
		} else {
			name = "wcp";
		}
		return name;
	}

	/* 连接服务标志 **/
	private String service;
	/* 数据连接URL **/
	private String durl;
	/* 数据连接用户名 **/
	private String duser;
	/* 数据连接密码 **/
	private String dpsd;
	/* 数据连接驱动器 **/
	private String driver;
	/* 连接池最大连接数 **/
	private String maxCon = "5";
	/* 连接池最小连接数 **/
	private String minCon = "2";
	/* 所属项目 **/
	private String project = "default";
	
	
	/**
	 * 返回连接服务标志 
	 * @return
	 */
	public String getService() {
		return service;
	}
	/**
	 * 设置连接服务标志 
	 * @param service
	 */
	public void setService(String service) {
		this.service = service;
	}
	/**
	 * 返回数据连接URL
	 * @return
	 */
	public String getDUrl() {
		return durl;
	}
	/**
	 * 设置数据连接URL
	 * @param durl
	 */
	public void setDUrl(String durl) {
		this.durl = durl;
	}
	/**
	 * 返回数据连接用户名
	 * @return
	 */
	public String getDUser() {
		return duser;
	}
	/**
	 * 设置数据连接用户名
	 * @param duser
	 */
	public void setDUser(String duser) {
		this.duser = duser;
	}
	/**
	 * 返回数据连接密码
	 * @return
	 */
	public String getDPsd() {
		return dpsd;
	}
	/**
	 * 设置数据连接密码
	 * @param dpsd
	 */
	public void setDPsd(String dpsd) {
		this.dpsd = dpsd;
	}
	/**
	 * 返回数据连接驱动器
	 * @return
	 */
	public String getDriver() {
		return driver;
	}
	/**
	 * 设置数据连接驱动器
	 * @param driver
	 */
	public void setDriver(String driver) {
		this.driver = driver;
	}
	/**
	 * 返回连接池最大连接数
	 * @return
	 */
	public String getMaxCon() {
		return maxCon;
	}
	/**
	 * 设置连接池最大连接数
	 * @param maxCon
	 */
	public void setMaxCon(String maxCon) {
		this.maxCon = maxCon;
	}
	/**
	 * 返回连接池最小连接数
	 * @return
	 */
	public String getMinCon() {
		return minCon;
	}
	/**
	 * 设置连接池最小连接数
	 * @param minCon
	 */
	public void setMinCon(String minCon) {
		this.minCon = minCon;
	}
	/**
	 * 返回所属项目
	 * @return
	 */
	public String getProject() {
		return project;
	}
	/**
	 * 设置所属项目
	 * @param project
	 */
	public void setProject(String project) {
		this.project = project;
	}
	
	public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

}
