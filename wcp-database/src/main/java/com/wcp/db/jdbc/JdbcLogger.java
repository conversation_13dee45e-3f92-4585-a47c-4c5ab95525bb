package com.wcp.db.jdbc;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wcp.db.util.PSqlUtils;
import com.wcp.utils.DateUtil;
import com.wcp.utils.ToolSpring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;


/**
 * Description: jdbc日志类
 * Author: qianchao
 * Date: 2024/2/27 15:02
 */

public class JdbcLogger {
    private static Logger logger= LoggerFactory.getLogger(JdbcLogger.class);
    private static boolean debugSQL;
    static {
        Environment bean = ToolSpring.getBean(Environment.class);
        debugSQL = Boolean.parseBoolean(bean.getProperty("wcp.debugSQL"));
    }
    public static PreparedStatement prepareStatement(Connection connection, String sql, List<?> parameterList) throws SQLException {
        // 创建 PreparedStatement 的代理对象
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        InvocationHandler handler = new InvocationHandler() {
            @Override
            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                if(debugSQL){
                    // 打印 SQL 语句
                    if (method.getName().equals("executeQuery") || method.getName().equals("executeUpdate") || method.getName().equals("executeBatch")) {
                        showFinalSql(sql,parameterList);
                        // 设置参数到 PreparedStatement
                       // setParameters(preparedStatement, parameterList);
                       // System.out.println(preparedStatement);

                    }
                }
                // 执行原始方法
                return method.invoke(preparedStatement, args);
            }
        };
        return (PreparedStatement) Proxy.newProxyInstance(
                JdbcLogger.class.getClassLoader(),
                new Class[]{PreparedStatement.class},
                handler
        );
    }
    /**
     * @param sql           原始SQL
     * @param parameterList 参数列表
     * @return              返回SQL
     * @description         jdbc打印执行SQL
     */
    public static String showFinalSql(String sql, List<?> parameterList) {

        //1 如果没有参数，说明是不是动态SQL语句
        int paramCount = 0;
        if (CollectionUtils.isNotEmpty(parameterList)) {
            paramCount = parameterList.size();
        }
        //2 如果有参数，则是动态SQL语句
        StringBuilder returnSql = new StringBuilder();
        // String[] subSql = sql.split("\\?");
        try{
            logger.info("Preparing:"+sql);
            logger.info("Parameters:");
            for (int i = 0; i < paramCount; i++) {
                Object item = parameterList.get(i);
                if(item instanceof String || item instanceof Integer || item instanceof Double){
                    logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>" , item));
                }
                if(item instanceof byte[]){
                    logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>" , item));
                }
                if(item instanceof Calendar){
                    logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>" , DateUtil.calendarToString((Calendar) item)));
                }
                if(item instanceof Date){
                    logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>" , DateUtil.dateToString((Date) item)));
                }
                if(item instanceof Object[]){
                    String arrayString = Arrays.stream((Object[]) item)
                            .map(obj -> Objects.toString(obj, "null"))
                            .collect(Collectors.joining(", "));

                    logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>" , arrayString));
                }
                if(item instanceof Map){
                    Map<String, Object> objMap = (Map<String, Object>) item;
                    String[] fieldNames = PSqlUtils.getInsertFieldNames(sql);
                    Object[] tempParams = new Object[fieldNames.length];
                    for (int j = 0; j < fieldNames.length; j++) {
                        String fieldName = fieldNames[j].trim();
                        tempParams[j] = objMap.get(fieldName);
                    }
                    String arrayString = Arrays.stream(tempParams)
                            .map(o -> o == null ? "null" : o.toString())
                            .collect(Collectors.joining(", "));

                    logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>" , arrayString));
                }
                if (item instanceof List<?>) {
                    for (int j = 0; j < ((ArrayList)item).size(); j++) {
                        if(((ArrayList<?>) item).get(j) instanceof String){
                            logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>"  ,((ArrayList<?>) item).get(j)));
                        } else if (((ArrayList<?>) item).get(j) instanceof Object[]) {
                            String arrayString = Arrays.stream((Object[]) ((ArrayList<?>) item).get(j))
                                    .map(o -> o == null ? "null" : o.toString())
                                    .collect(Collectors.joining(", "));
                            logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>"  ,arrayString));
                        } else if (((ArrayList<?>) item).get(j) instanceof Map) {
                            Map<?, ?> mapElem = (Map<?, ?>) ((ArrayList<?>) item).get(j);
                            String mapStr = mapElem.entrySet().stream()
                                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                                    .collect(Collectors.joining(", "));
                            logger.info(String.format("\t\t%s\t%s", "param"+(i+1)+"==>", mapStr));
                        } else {
                            logger.info(String.format("\t\t%s\t%s", "param"+(i+1)+"==>", ((ArrayList<?>) item).get(j)));
                        }
                    }
                }
                /*if(item instanceof ArrayList<?>){
                    for (int j = 0; j < ((ArrayList)item).size(); j++) {
                        if(((ArrayList<?>) item).get(j) instanceof String){
                            logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>"  ,((ArrayList<?>) item).get(j)));
                        }else{
                            Object[] obj= (Object[]) ((ArrayList<?>) item).get(j);
                            String arrayString = Arrays.stream(obj)
                                    .map(o -> o == null ? "null" : o.toString())
                                    .collect(Collectors.joining(", "));
                            logger.info(String.format("\t\t%s\t%s","param"+(i+1)+"==>"  ,arrayString));
                        }

                    }
                }*/

            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("SQL语句生成失败");
        }
        return returnSql.toString();
    }

    /**
     * 将参数设置到 PreparedStatement
     */
    private static void setParameters(PreparedStatement preparedStatement, List<?> parameterList) throws SQLException {
        if (parameterList != null && !parameterList.isEmpty()) {
            for (int i = 0; i < parameterList.size(); i++) {
                Object parameter = parameterList.get(i);
                if (parameter instanceof String) {
                    preparedStatement.setString(i + 1, (String) parameter);
                } else if (parameter instanceof Integer) {
                    preparedStatement.setInt(i + 1, (Integer) parameter);
                } else if (parameter instanceof Double) {
                    preparedStatement.setDouble(i + 1, (Double) parameter);
                } else if (parameter instanceof Date) {
                    preparedStatement.setDate(i + 1, new java.sql.Date(((Date) parameter).getTime()));
                } else if (parameter instanceof Calendar) {
                    preparedStatement.setDate(i + 1, new java.sql.Date(((Calendar) parameter).getTimeInMillis()));
                } else if (parameter instanceof Object[]) {
                    // 处理数组类型的参数
                    preparedStatement.setObject(i + 1, parameter);
                } else {
                    preparedStatement.setObject(i + 1, parameter);
                }
            }
        }
    }
}
