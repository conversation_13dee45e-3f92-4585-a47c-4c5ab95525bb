package com.wcp.db.jdbc;


import com.wcp.db.data.OperateTableData;
import com.wcp.db.data.SQLExcuteException;
import com.wcp.db.data.TableFieldProp;
import com.wcp.db.data.TableProp;
import com.wcp.db.querydao.*;
import com.wcp.data.DataTypeUtil;
import com.wcp.db.util.PPathUtil;
import com.wcp.db.util.PQueryUtil;
import com.wcp.db.util.PSqlUtils;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;
import org.apache.log4j.Logger;

import java.rmi.RemoteException;
import java.sql.*;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WcpDao {
	private  static Logger logger = Logger.getLogger(WcpDao.class);

	public   Map<Integer, String> getMapJdbcType() {
		HashMap<Integer, String> hashMap = new HashMap<>();
		hashMap.put(-7, "BIT");
		hashMap.put(-6, "TINYINT");
		hashMap.put(5, "SMALLINT");
		hashMap.put(4, "INTEGER");
		hashMap.put(-5, "BIGINT");
		hashMap.put(6, "FLOAT");
		hashMap.put(7, "REAL");
		hashMap.put(8, "DOUBLE");
		hashMap.put(2, "NUMERIC");
		hashMap.put(3, "DECIMAL");
		hashMap.put(1, "CHAR");
		hashMap.put(12, "VARCHAR");
		hashMap.put(-1, "LONGVARCHAR");
		hashMap.put(91, "DATE");
		hashMap.put(92, "TIME");
		hashMap.put(93, "TIMESTAMP");
		hashMap.put(-2, "BINARY");
		hashMap.put(-3, "VARBINARY");
		hashMap.put(-4, "LONGVARBINARY");
		hashMap.put(0, "NULL");
		hashMap.put(1111, "OTHER");
		hashMap.put(2005, "CLOB");
		return hashMap;
	}

	public   Map<String, String> getMapjavaType() {
		HashMap<String, String> hashMap = new HashMap<>();
		hashMap.put("CHAR", "String");
		hashMap.put("VARCHAR", "String");
		hashMap.put("LONGVARCHAR", "String");
		hashMap.put("NUMERIC", "NUMBER");
		hashMap.put("DECIMAL", "DECIMAL");
		hashMap.put("BIT", "Boolean");
		hashMap.put("TINYINT", "Integer");
		hashMap.put("SMALLINT", "Integer");
		hashMap.put("INTEGER", "Integer");
		hashMap.put("BIGINT", "Long");
		hashMap.put("REAL", "Float");
		hashMap.put("FLOAT", "Double");
		hashMap.put("DOUBLE", "Double");
		hashMap.put("BINARY", "byte[]");
		hashMap.put("VARBINARY", "byte[]");
		hashMap.put("LONGVARBINARY", "byte[]");
		hashMap.put("DATE", "String");
		hashMap.put("TIME", "String");
		hashMap.put("TIMESTAMP", "String");
		return hashMap;
	}

	// public   void main(String[] args) {
	// getMetaData("default", "wcp", "WCP_TB_DATAUNIT");
	// }

	public   void main(String[] args) {

	}

	/**
	 * 查询数据库类型
	 * @param service 数据库注册服务
	 * @return string
	 */
	public static String getTypeOfDB(String service){
		return WcpJdbcService.getDatabaseType(WcpThreadLocal.getProjectId(), service);
	}

	/**
	 * 查询指定表主键
	 * @param service
	 *            服务连接标志
	 * @param tableName
	 *            表名称
	 * @return
	 */
	public static List<String> getTablePrimaryKeys(String service, String tableName) {
		Connection conn = null;
		ResultSet resultSet = null;
		try {
			conn = WcpConnection.getTransactionConnect(WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			//DatabaseMetaData dbmd = conn.getMetaData();
			//ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			resultSet = conn.getMetaData().getPrimaryKeys(conn.getCatalog().toUpperCase(),
					conn.getMetaData().getUserName().toUpperCase(), tableName.toUpperCase());
			ArrayList<String> primaryKeyList = new ArrayList<>();
			while (resultSet.next()) {
				primaryKeyList.add(resultSet.getString("COLUMN_NAME"));
			}
			return primaryKeyList;
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if (resultSet != null) {
					resultSet.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (Exception e2) {
			}
		}
		return null;
	}

	/**
	 * 执行QueryWrapper查询操作
	 * @param service     数据源名称
	 * @return
	 */
	public static List<Map<String, Object>> queryMap(String service,QueryWrapper wrapper){
//		String url = "************************************************************************************************************************************************";
//		String username = "root";
//		String password = "Admin@123456";
//
//		try (Connection connection = DriverManager.getConnection(url, username, password)) {
//			DatabaseMetaData metaData = connection.getMetaData();
//			String databaseProductName = metaData.getDatabaseProductName();
//			System.out.println("Database Product Name: " + databaseProductName);
//		} catch (SQLException e) {
//			e.printStackTrace();
//		}
		Connection connection = WcpConnection.getConnection(WcpThreadLocal.getProjectId(), service);
		try {
			//获取源数据
			DatabaseMetaData metaData = connection.getMetaData();
			// 获取数据库产品名称
			String databaseProductName = metaData.getDatabaseProductName();
			// 获取数据库产品版本
			String databaseProductVersion = metaData.getDatabaseProductVersion();
			connection.close();
			wrapper.setDataBaseName(databaseProductName);
			String sql = wrapper.getSql();
			Object[] object = wrapper.getParamList().toArray();
			return queryMap(service, sql,object, null, null);
		}catch (SQLException e){
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 查询指定表字段属性
	 *
	 * @param service
	 *            服务连接标志
	 * @param tableName
	 *            表名称
	 * @return
	 */
	public static List<TableFieldProp> getTableNameFields(String service, String tableName) {
		Connection conn = null;
		ResultSet resultSet = null;
		try {
			conn = WcpConnection.getTransactionConnect(WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			String url = dbmd.getURL();
			String[] split = url.split("/");
			ResultSet rs=null;
			ResultSet keyRs=null;
			String database=null;
			if(split[0].toUpperCase().contains("JDBC:DM")){
				Pattern pattern = Pattern.compile("schema=(\\w+)");
				Matcher matcher = pattern.matcher(split[2]);
				if (matcher.find()) {
					database = matcher.group(1);
					rs= dbmd.getColumns(null, database, tableName, null);
					keyRs= dbmd.getPrimaryKeys(null, database, tableName);
				}

			}else{
				rs= dbmd.getColumns(conn.getCatalog(), null, tableName, null);
				keyRs= dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			}
			//ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> tableFieldDataMap = new HashMap<String, TableFieldProp>();
			List<TableFieldProp> tableFieldDataList = new ArrayList<TableFieldProp>();
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), tableName + "." + columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"), rs.getString("REMARKS"));
				tableFieldDataList.add(tableFieldDao);
				tableFieldDataMap.put(columnName, tableFieldDao);
			}
			//ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = tableFieldDataMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}
			return tableFieldDataList;
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if (resultSet != null) {
					resultSet.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (Exception e2) {
			}
		}
		return null;
	}

	/**
	 * 获取数据库表名和注释
	 * @param service
	 *            服务连接标志
	 * @return
	 */
	public static List<TableProp> queryTables(String service) {
		Connection conn = null;
		ResultSet resultSet = null;
		try {
			conn = WcpConnection.getTransactionConnect(WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();

			String url = dbmd.getURL();
			//String database = url.substring(31, 36);
			String[] split = url.split("/");
			String database=null;
			ResultSet rs=null;
			if(split[0].toUpperCase().contains("JDBC:DM")){
				Pattern pattern = Pattern.compile("schema=(\\w+)");
				Matcher matcher = pattern.matcher(split[2]);
				if (matcher.find()) {
					database = matcher.group(1);
					rs = dbmd.getTables(null, database, null, new String[] { "TABLE" });
				}

			}else{
				String[] split2 = split[3].split("\\?");
				database=split2[0];
				rs = dbmd.getTables(database, null, null, new String[] { "TABLE" });
			}
			// ResultSet rs = dbmd.getTables(conn.getCatalog(), null, null, types);
			// String database = url.substring(31, 36);
			// ResultSet rs = dbmd.getTables(database, null, null, new String[]{"TABLE"});
			// ResultSet rs = dbmd.getTables("wcpdb", null, null, new String[]{"TABLE"});
			List<TableProp> tableList = new ArrayList<TableProp>();
			while (rs.next()) {
				String tableCat = rs.getString("TABLE_CAT");
				String tableScheme = rs.getString("TABLE_SCHEM");
				String tableName = rs.getString("TABLE_NAME").toUpperCase();
				String tableType = rs.getString("TABLE_TYPE").toUpperCase();
				String tableDesc = rs.getString("REMARKS");
				TableProp tableFieldDao = new TableProp(tableCat, tableScheme, tableName, tableType, tableDesc);
				tableList.add(tableFieldDao);
			}
			return tableList;
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if (resultSet != null) {
					resultSet.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (Exception e2) {
			}
		}
		return null;
	}

	/**
	 * 查询指定表字段属性
	 *
	 * @param service
	 *            服务连接标志
	 * @param tableName
	 *            表名称
	 * @return
	 */
	public static List<TableFieldProp> getTableFields(String service, String tableName) {
		Connection conn = null;
		ResultSet resultSet = null;
		try {
			conn = WcpConnection.getTransactionConnect(WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			String url = dbmd.getURL();
			//String database = url.substring(31, 36);
			String[] split = url.split("/");
			String database=null;
			ResultSet rs=null;
			ResultSet keyRs=null;
			if(split[0].toUpperCase().contains("JDBC:DM")){
				Pattern pattern = Pattern.compile("schema=(\\w+)");
				Matcher matcher = pattern.matcher(split[2]);
				if (matcher.find()) {
					database = matcher.group(1);
					rs= dbmd.getColumns(null, database, tableName, null);
					keyRs= dbmd.getPrimaryKeys(null, database, tableName);
				}

			}else{
				rs= dbmd.getColumns(conn.getCatalog(), null, tableName, null);
				keyRs= dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			}
			//conn.getCatalog()

			Map<String, String> tableFieldDataMap = new HashMap<String, String>();
			List<TableFieldProp> tableFieldDataList = new ArrayList<TableFieldProp>();

			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				tableFieldDataMap.put(columnName, columnName);
			}
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"), rs.getString("REMARKS"));
				if (tableFieldDataMap.get(columnName) != null) {
					tableFieldDao.setPrimary(true);
				}
				tableFieldDataList.add(tableFieldDao);
			}

			return tableFieldDataList;
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if (resultSet != null) {
					resultSet.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (Exception e2) {
			}
		}
		return null;
	}

	/**
	 * 查询表字段属性
	 * @param service
	 *            服务连接标志
	 * @return
	 */
	public static List<TableProp> queryTable(String service, String[] types) {
		Connection conn = null;
		ResultSet resultSet = null;
		try {
			conn = WcpConnection.getTransactionConnect(WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			String url = dbmd.getURL();
			String[] split = url.split("/");
			ResultSet rs=null;
			ResultSet keyRs=null;
			String database=null;
			if(split[0].toUpperCase().contains("JDBC:DM")){
				Pattern pattern = Pattern.compile("schema=(\\w+)");
				Matcher matcher = pattern.matcher(split[2]);
				if (matcher.find()) {
					database = matcher.group(1);
					rs= dbmd.getColumns(null, database, null, null);
				}

			}else{
				rs= dbmd.getColumns(conn.getCatalog(), null, null, null);
			}
			//ResultSet rs = dbmd.getTables(conn.getCatalog(), null, null, types);
			List<TableProp> tableList = new ArrayList<TableProp>();
			while (rs.next()) {
				String tableCat = rs.getString("TABLE_CAT");
				String tableScheme = rs.getString("TABLE_SCHEM");
				String tableName = rs.getString("TABLE_NAME").toUpperCase();
				String tableType = rs.getString("TABLE_TYPE").toUpperCase();
				String tableDesc = rs.getString("REMARKS");
				TableProp tableFieldDao = new TableProp(tableCat, tableScheme, tableName, tableType, tableDesc);
				tableList.add(tableFieldDao);
			}
			return tableList;
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if (resultSet != null) {
					resultSet.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (Exception e2) {
			}
		}
		return null;
	}

	/**
	 * 查询表字段属性
	 * @param service
	 *            服务连接标志
	 * @param tableName
	 *            表名称
	 * @return
	 */
	public static List<TableFieldProp> queryTableField(String service, String tableName) {
		Connection conn = null;
		ResultSet resultSet = null;
		try {
			conn = WcpConnection.getTransactionConnect(WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			String url = dbmd.getURL();
			//String database = url.substring(31, 36);
			String[] split = url.split("/");
			String database=null;
			ResultSet rs=null;
			ResultSet keyRs=null;
			if(split[0].toUpperCase().contains("JDBC:DM")){
				Pattern pattern = Pattern.compile("schema=(\\w+)");
				Matcher matcher = pattern.matcher(split[2]);
				if (matcher.find()) {
					database = matcher.group(1);
					rs= dbmd.getColumns(null, database, tableName, null);
					keyRs= dbmd.getPrimaryKeys(null, database, tableName);
				}

			}else{
				rs= dbmd.getColumns(conn.getCatalog(), null, tableName, null);
				keyRs= dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			}
			//ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> tableFieldDataMap = new HashMap<String, TableFieldProp>();
			List<TableFieldProp> tableFieldDataList = new ArrayList<TableFieldProp>();
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName, rs.getInt("COLUMN_SIZE"),
						rs.getInt("DECIMAL_DIGITS"), rs.getInt("NULLABLE"), rs.getString("COLUMN_DEF"), rs.getString("REMARKS"));
				tableFieldDataList.add(tableFieldDao);
				tableFieldDataMap.put(columnName, tableFieldDao);
			}
			//ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = tableFieldDataMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}
			return tableFieldDataList;
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if (resultSet != null) {
					resultSet.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (Exception e2) {
			}
		}
		return null;
	}

	/**
	 * 查询是否存在某表
	 *
	 * @return
	 */
	public static  boolean existTable(String service, String tableNamePattern, String types[]) {
		boolean existTable = false;
		// 数据库名称
		String databaseName = WcpJdbcService.getDatabaseName( WcpThreadLocal.getProjectId(), service);
		Connection conn = null;
		ResultSet resultSet = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return false;
			DatabaseMetaData databaseMetaData = conn.getMetaData();
			String url = databaseMetaData.getURL();
			//String database = url.substring(31, 36);
			String[] split = url.split("/");
			String database=null;
			if(split[0].toUpperCase().contains("JDBC:DM")){
				Pattern pattern = Pattern.compile("schema=(\\w+)");
				Matcher matcher = pattern.matcher(split[2]);
				if (matcher.find()) {
					database = matcher.group(1);
					resultSet= databaseMetaData.getColumns(null, database, tableNamePattern, null);
				}

			}else{
				resultSet= databaseMetaData.getColumns(conn.getCatalog(), null, tableNamePattern, null);
			}
			//resultSet = databaseMetaData.getTables(databaseName, null, tableNamePattern, types);
			existTable = resultSet.next();
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
			existTable = false;
		} finally {
			try {
				if (resultSet != null) {
					resultSet.close();
				}
			} catch (Exception e2) {
				e2.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (Exception e2) {
				e2.printStackTrace();
			}
		}
		return existTable;
	}

	public static  boolean executeDDL(String service, String sql, Object[] param) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		sql = PSqlUtils.composeSql(sql, param);
		Connection conn = WcpConnection.getConnection( WcpThreadLocal.getProjectId(), service);
		if (conn == null)
			return false;
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return false;
		}
		PreparedStatement pst = null;
		boolean success = true;
		try {
			conn.setAutoCommit(false);
			pst = JdbcLogger.prepareStatement(conn, sql,Optional.ofNullable(param).map(Arrays::asList).orElseGet(() -> new ArrayList<>()));
//			pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			PSqlUtils.paramBinding(1, pst, param);
			success = pst.executeUpdate() > 0;
			conn.commit();
		} catch (SQLException e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "执行失败，请检查SQL语句或数据表!");
			logger.error(e.getMessage());
			success = false;
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				conn.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	// 99
	public static boolean execute(String service, String sql, Object[] param) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		sql = PSqlUtils.composeSql(sql, param);
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return false;
		}
		Connection conn = null;
		PreparedStatement pst = null;
		boolean success = true;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return false;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst = JdbcLogger.prepareStatement(conn, sql,Optional.ofNullable(param).map(Arrays::asList).orElseGet(() -> new ArrayList<>()));
			//pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			PSqlUtils.paramBinding(1, pst, param);
			success = pst.executeUpdate() > 0;
			if(!WcpConnection.isOpenTransaction()) {
				conn.commit();
			}
		} catch (SQLException e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "执行失败，请检查SQL语句或数据表!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
			success = false;
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public static   boolean execute(String service, String sql, Object[][] param) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		if (param != null && param.length > 0) {
			sql = PSqlUtils.composeSql(sql, param[0]);
		}
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		int[] backFalg = new int[param.length];
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return success;
		}
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst = JdbcLogger.prepareStatement(conn, sql,Optional.ofNullable(param).map(Arrays::asList).orElseGet(() -> new ArrayList<>()));
//			pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			int exeLastCount = 0;
			int exeCount = 0;
			for (int i = 0; i < param.length; i++) {
				try {
					PSqlUtils.paramBinding(1, pst, param[i]);
					pst.addBatch();
					exeCount++;
				} catch (SQLException e) {
					String tempStr = PPathUtil.convertValidLog(sql);
					logger.error("SQL:" + tempStr + "数据绑定失败，请检查SQL语句或参数!");
					logger.error(e.getMessage());
				}
				if (exeCount % 500 == 0 || i == (param.length - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						String tempStr = PPathUtil.convertValidLog(sql);
						logger.error("SQL:" + tempStr + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
		} catch (SQLException e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "创建预编译存储失败，请检查SQL语句或参数!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				pst.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public  static  <T> boolean insert(String service, String sql, List<T> param, String aliasStr) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		int[] backFalg = new int[param.size()];
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return success;
		}
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst= JdbcLogger.prepareStatement(conn,sql,Optional.ofNullable(param).map(Arrays::asList).orElseGet(() -> new ArrayList<>()));//jdbcLogger.prepareStatement(conn, sql);
			//pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			String[] fieldNames = PSqlUtils.getInsertFieldNames(sql);
			int exeLastCount = 0;
			int exeCount = 0;
			for (int i = 0; i < param.size(); i++) {
				try {
					PSqlUtils.paramBinding(1, pst, param.get(i), fieldNames, aliasStr, sql);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					String tempStr = PPathUtil.convertValidLog(sql);
					logger.error("SQL:" + tempStr + "数据绑定失败，请检查SQL语句或参数!");
					logger.error(e.getMessage());
					e.printStackTrace();
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length && (exeLastCount + j) < backFalg.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						String tempStr = PPathUtil.convertValidLog(sql);
						logger.error("SQL:" + tempStr + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
			success = backFalg.length > 0 && StringUtil.containsZero(backFalg);
		} catch (Exception e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "创建预编译存储失败，请检查SQL语句或参数!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public  static  <T> boolean insertOrUpdate(String service, String sql, List<T> param,
											   Object[] symbols, String aliasStr) {
		sql = PSqlUtils.consituteSymbols(sql, symbols);
		return insertOrUpdate( service, sql, param, aliasStr);
	}

	public  static  <T> boolean insertOrUpdate(String service, String sql, List<T> param,
											   String aliasStr) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		int[] backFalg = new int[param.size()];
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return false;
		}
		boolean success = false;
		Connection conn = null;
		PreparedStatement deletePst = null;
		PreparedStatement insertPst = null;
		try {
			// 开始事务
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			// 查询主健
			String tableName = PSqlUtils.getInsertTableName(sql);
			String[] tableFieldNames = PSqlUtils.getInsertTableFiledName(sql);
			DatabaseMetaData metaData = conn.getMetaData();
			int pointIdx = tableName.lastIndexOf(".");
			String tempTableName = tableName;
			if (pointIdx >= 0)
				tempTableName = tempTableName.substring(pointIdx + 1);
			ResultSet resultSet = metaData.getPrimaryKeys(conn.getCatalog(), null, tempTableName);
			// 主健
			String deleteSql = "DELETE FROM " + tableName + " WHERE ";
			List<String> primaryFieldNames = new ArrayList<>();
			while (resultSet.next()) {
				String columnName = resultSet.getString("COLUMN_NAME").toUpperCase();
				if (primaryFieldNames.size() == 0) {
					deleteSql += columnName + "= ?";
				} else {
					deleteSql += " AND " + columnName + "= ?";
				}
				primaryFieldNames.add(columnName);
			}
			// 删除操作
			int exeCount = 0;
			if (primaryFieldNames.size() > 0) {
				deletePst = JdbcLogger.prepareStatement(conn, deleteSql,new ArrayList<>());
//				deletePst = conn.prepareStatement(deleteSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
				for (int i = 0; i < param.size(); i++) {
					PSqlUtils.paramBinding(1, deletePst, param.get(i), tableFieldNames,
							primaryFieldNames.toArray(new String[0]), aliasStr);
					deletePst.addBatch();
					exeCount++;
					if (exeCount % 500 == 0 || i == (param.size() - 1)) {
						deletePst.executeBatch();
					}
				}
			}
			// 插入操作
			exeCount = 0;
			int exeLastCount = 0;
			insertPst = JdbcLogger.prepareStatement(conn, sql,new ArrayList<>());
//			insertPst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			for (int i = 0; i < param.size(); i++) {
				PSqlUtils.paramBinding(1, insertPst, param.get(i), tableFieldNames, aliasStr,sql);
				insertPst.addBatch();
				exeCount++;
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					int[] tempInt = insertPst.executeBatch();
					for (int j = exeLastCount; j < tempInt.length; j++) {
						backFalg[exeLastCount + j] = tempInt[j];
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
			if(!WcpConnection.isOpenTransaction()) {
				conn.commit();
			}
			success = backFalg.length > 0 && StringUtil.containsZero(backFalg);
		} catch (Exception e) {
			logger.error("SQL:" + sql + "插入执行失败,请检查SQL语句或数据.");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
			try {
				if(!WcpConnection.isOpenTransaction()) {
					conn.rollback();
				}
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (deletePst != null)
					deletePst.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (insertPst != null)
					insertPst.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public  static  <T> boolean insertMap(String service, String sql, List<Map<String, Object>> param,
										  String aliasStr) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		String[] fieldNames = PSqlUtils.getInsertFieldNames(sql);
		Map<String, String> aliasMap = null;
		if (aliasStr != null && !aliasStr.trim().equals("")) {
			aliasMap = PSqlUtils.analysisAliasField(aliasStr);
		}
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return false;
		}
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		int[] backFalg = new int[param.size()];
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst = JdbcLogger.prepareStatement(conn, sql,param);
//			pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			int exeLastCount = 0;
			int exeCount = 0;
			for (int i = 0; i < param.size(); i++) {
				try {
					Map<String, Object> objMap = param.get(i);
					Object[] tempParams = new Object[fieldNames.length];
					for (int j = 0; j < fieldNames.length; j++) {
						String fieldName = fieldNames[j].trim();
						if (aliasMap != null) {
							fieldName = aliasMap.get(fieldName);
						}
						tempParams[j] = objMap.get(fieldName);
					}
					PSqlUtils.paramBinding(1, pst, tempParams);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					String tempStr = PPathUtil.convertValidLog(sql);
					logger.error("SQL:" + tempStr + "数据绑定失败，请检查SQL语句或参数!");
					e.printStackTrace();
				}
				if (exeCount % 10000 == 0 || i == (param.size() - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + sql + "执行失败，请检查SQL语句或参数!");
						e.printStackTrace();
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
		} catch (Exception e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "创建预编译存储失败，请检查SQL语句或参数!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public  static  <T> boolean insertListMap(String service, String tableName,
											  List<Map<String, Object>> param) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> fieldMap = new HashMap<String, TableFieldProp>();
			List<String> tableFieldList = new ArrayList<>();
			String fieldNames = "";
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
				fieldMap.put(columnName, tableFieldDao);
				fieldNames += "".equals(fieldNames) ? columnName : ("," + columnName);
				tableFieldList.add(columnName);
			}
			ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = fieldMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}
			// 先删除
			OperateTableData deleteTableData = PSqlUtils.getJudgeSql(fieldMap, param);
			if (deleteTableData == null) {
				return success;
			}
			String deleteSql = "DELETE FROM " + tableName + " WHERE " + deleteTableData.getJudgeStr();
			pst = JdbcLogger.prepareStatement(conn, deleteSql,new ArrayList<>());
//			pst = conn.prepareStatement(deleteSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			int exeLastCount = 0;
			int exeCount = 0;
			Object[][] deleteParams = deleteTableData.getParams();
			for (int i = 0; i < deleteParams.length; i++) {
				try {
					PSqlUtils.paramBinding(1, pst, deleteParams[i]);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					logger.error("SQL:" + "数据绑定失败，请检查SQL语句或参数!");
					logger.error(e.getMessage());
					e.printStackTrace();
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						pst.executeBatch();
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + deleteSql + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
			pst.close();
			// 再插入
			int[] backFalg = new int[param.size()];
			String tempParamStr = PSqlUtils.getPlaceholder(fieldMap.size());
			String insertSql = "INSERT INTO " + tableName + "(" + fieldNames + ") VALUES (" + tempParamStr + ")";
			pst = JdbcLogger.prepareStatement(conn, insertSql,new ArrayList<>());
//			pst = conn.prepareStatement(insertSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			exeLastCount = 0;
			exeCount = 0;
			for (int i = 0; i < param.size(); i++) {
				try {
					Map<String, Object> objMap = param.get(i);
					Object[] tempParams = new Object[tableFieldList.size()];
					for (int j = 0; j < tempParams.length; j++) {
						TableFieldProp tableFieldDao = fieldMap.get(tableFieldList.get(j));
						tempParams[j] = PSqlUtils.transToTableData(objMap.get(tableFieldList.get(j)), tableFieldDao);
					}
					PSqlUtils.paramBinding(1, pst, tempParams);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					logger.error("SQL:" + "数据绑定失败，请检查SQL语句或参数!");
					if(WcpConnection.isOpenTransaction()) {
						throw new RuntimeException(e);
					}
					e.printStackTrace();
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + insertSql + "执行失败，请检查SQL语句或参数!");
						logger.error(e.getMessage());
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public static   <T> boolean deleteListMapMatchAll(String service, String tableName,
													  List<Map<String, Object>> param) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> fieldMap = new HashMap<String, TableFieldProp>();
			List<String> tableFieldList = new ArrayList<>();
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
				fieldMap.put(columnName, tableFieldDao);
				tableFieldList.add(columnName);
			}
			ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = fieldMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}

			// 删除
			int[] backFalg = new int[param.size()];
			OperateTableData deleteTableData = PSqlUtils.getDeleteSql(fieldMap, param);
			if (deleteTableData == null) {
				return success;
			}
			String deleteSql = "DELETE FROM " + tableName + " WHERE " + deleteTableData.getJudgeStr();
			pst = JdbcLogger.prepareStatement(conn,deleteSql,new ArrayList<>());
//			pst = conn.prepareStatement(deleteSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			int exeLastCount = 0;
			int exeCount = 0;
			Object[][] deleteParams = deleteTableData.getParams();
			for (int i = 0; i < deleteParams.length; i++) {
				try {
					PSqlUtils.paramBinding(1, pst, deleteParams[i]);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					logger.error("SQL:" + "数据绑定失败，请检查SQL语句或参数!");
					e.printStackTrace();
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + deleteSql + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public  static  <T> boolean deleteListMap(String service, String tableName,
											  List<Map<String, Object>> param) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> fieldMap = new HashMap<String, TableFieldProp>();
			List<String> tableFieldList = new ArrayList<>();
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
				fieldMap.put(columnName, tableFieldDao);
				tableFieldList.add(columnName);
			}
			ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = fieldMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}

			// 删除
			int[] backFalg = new int[param.size()];
			OperateTableData deleteTableData = PSqlUtils.getQueryConditionSqlOfTable(fieldMap, param);
			if (deleteTableData == null) {
				return success;
			}
			String deleteSql = "DELETE FROM " + tableName + " WHERE " + deleteTableData.getJudgeStr();
			pst = JdbcLogger.prepareStatement(conn,deleteSql,new ArrayList<>());
//			pst = conn.prepareStatement(deleteSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			int exeLastCount = 0;
			int exeCount = 0;
			Object[][] deleteParams = deleteTableData.getParams();
			for (int i = 0; i < deleteParams.length; i++) {
				try {
					PSqlUtils.paramBinding(1, pst, deleteParams[i]);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					logger.error("SQL:" + "数据绑定失败，请检查SQL语句或参数!");
					logger.error(e.getMessage());
					e.printStackTrace();
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + deleteSql + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public static   <T> boolean deleteListMapBySql(String service, String tableName,
												   String conditionSql, Object[] param) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return false;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> fieldMap = new HashMap<String, TableFieldProp>();
			List<String> tableFieldList = new ArrayList<>();
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
				fieldMap.put(columnName, tableFieldDao);
				tableFieldList.add(columnName);
			}
			ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = fieldMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}

			// 删除
			String deleteSql = "DELETE FROM " + tableName + " WHERE " + conditionSql;
			pst = JdbcLogger.prepareStatement(conn,deleteSql,new ArrayList<>());
//			pst = conn.prepareStatement(deleteSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, param);
			return pst.execute();
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return false;
	}

	public  static  <T> boolean updateListMap(String service, String tableName,
											  List<Map<String, Object>> param, List<Map<String, Object>> condition) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return success;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> fieldMap = new HashMap<String, TableFieldProp>();
			List<String> tableFieldList = new ArrayList<>();
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
				fieldMap.put(columnName, tableFieldDao);
				tableFieldList.add(columnName);
			}
			ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = fieldMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}

			// 删除
			int[] backFalg = new int[param.size()];
			OperateTableData updateTableData = PSqlUtils.getUpdateSql(fieldMap, param);
			if (updateTableData == null) {
				return success;
			}
			if (condition == null)
				condition = param;
			OperateTableData judgeTableData = PSqlUtils.getJudgeSql(fieldMap, condition);
			if (judgeTableData == null) {
				return success;
			}
			String updateSql = "UPDATE " + tableName + " SET " + updateTableData.getJudgeStr() + " WHERE "
					+ judgeTableData.getJudgeStr();
			pst = JdbcLogger.prepareStatement(conn,updateSql,new ArrayList<>());
//			pst = conn.prepareStatement(updateSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			int exeLastCount = 0;
			int exeCount = 0;
			Object[][] deleteParams = PSqlUtils.mergeParams(updateTableData.getParams(), judgeTableData.getParams());
			for (int i = 0; i < deleteParams.length; i++) {
				try {
					PSqlUtils.paramBinding(1, pst, deleteParams[i]);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					logger.error("SQL:" + "数据绑定失败，请检查SQL语句或参数!");
					logger.error(e.getMessage());
					e.printStackTrace();
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + updateSql + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	/**
	 * 返回匹配条件中的所有字段的结果
	 *
	 * @param projectId
	 * @param service
	 * @param tableList
	 *            数据库表名,支持多表和单表，单表：HOURDB A或HOURDB；多表：HOURDB A,IDELEMENT B,TABLE C
	 * @param tableFields
	 *            查询的字段，如果为空，则查询所有字段，A.SENID,A.TIME,B.DESC
	 * @param tableJoins
	 *            多表时级联类型，JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL
	 *            JOIN.分别为内联连，左连接，右连接，全连接.默认为内连接，单表为空
	 * @param conditions
	 *            查询条件，全部同时满足
	 * @param groups
	 *            排序，多表：A.SENID ASC,B.TIME DESC
	 * @return
	 */

	/**
	 *
	 * @param service
	 * @param tableList
	 * @param tableFields
	 *                多表时级联类型，JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL
	 * 	              JOIN.分别为内联连，左连接，右连接，全连接.默认为内连接，单表为空
	 * @param tableJoins
	 * @param conditions 查询条件，全部同时满足
	 * @param groups 排序，多表：A.SENID ASC,B.TIME DESC
	 * @param limit
	 * @param orders
	 * @param <T>
	 * @return
	 * @throws SQLExcuteException
	 */
	public  static  <T> List<Map<String, Object>> queryDataListByRule(String service,
																	  List<String> tableList, List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins,
																	  List<ConditionDao> conditions, List<GroupDao> groups, LimitDao limit, List<OrderDao> orders)
			throws SQLExcuteException {

		// 表名和别名的对应缓存
		Map<String, String> tableNameCache = new LinkedHashMap<String, String>();
		// 字段名和字段别名的对应缓存
		Map<String, String> tableFieldNameCache = new LinkedHashMap<String, String>();
		// 字段别名和查询出的字段名的对应缓存
		Map<String, String> tableFieldNameCache1 = new LinkedHashMap<String, String>();
		// 查询出的字段名和字段别名的对应缓存
		Map<String, String> tableFieldNameCache2 = new LinkedHashMap<String, String>();
		// 查询的字段名和数据类型的对应缓存
		Map<String, String> tableFieldNameCache3 = new LinkedHashMap<String, String>();
		char tempTableAlias = 'A';
		for (int i = 0; i < tableList.size(); i++) {
			tableNameCache.put(tableList.get(i).toUpperCase(), tempTableAlias++ + "");
		}
		int aggFuncCount = 1;
		// 组织查询字段
		String queryTableField = "";
		HashMap<Object, Object> aliasMap = new HashMap<>();
		if (tableFields != null && tableFields.size() > 0) { // 给出查询字段,则查询给定的字段
			for (int i = 0; i < tableFields.size(); i++) {
				TableFieldDao tableFieldDao = tableFields.get(i);
				String tableName = tableFieldDao.getTableName();
				// 如果未设置字段所在的表名，则认为是第一个表的字段
				if (tableName == null || tableName.trim().equals("")) {
					tableName = tableList.get(0).toUpperCase();
				}
				// 查找表的别名,如果没有别名,则往表格中添加一个别名
				String tableAlias = PQueryUtil.queryTableAlias(tableNameCache, tableName.toUpperCase());
				String tableField = tableFieldDao.getField().toUpperCase();
				String tableFieldAlias = tableFieldDao.getAlias();
				String aggFunc = tableFieldDao.getAggFunc();
				aliasMap.put(tableFieldAlias, tableField);
				// 如果没有设置别名,则默认为表的字段
				if (tableFieldAlias == null || tableFieldAlias.trim().equals("")) {
					tableFieldAlias = tableField;
					// if(aggFunc != null && aggFunc)
				}
				String queryFieldName = tableAlias + "_" + tableField;
				tableFieldNameCache.put(tableField, tableFieldAlias);
				tableFieldNameCache1.put(tableFieldAlias, queryFieldName);
				tableFieldNameCache2.put(queryFieldName, tableFieldAlias);
				tableFieldNameCache3.put(queryFieldName, tableFieldDao.getFieldShowType());
				if (i > 0)
					queryTableField += ",";
				queryTableField += tableAlias + "." + tableField + " " + "AS" + " " + queryFieldName;
			}
		}
		System.out.println(queryTableField);
		String tableJoinStr = "";
		if (tableJoins != null && tableJoins.size() > 1) {
			for (int i = 1; i < tableJoins.size(); i++) {
				TableJoinDao tableJoinDao = tableJoins.get(i);
				// String aliasLeftTableName = tableNameCache.get(tableJoinDao.getLeftTable());
				String aliasRightTableName = tableNameCache.get(tableJoinDao.getRightTable());
				tableJoinStr += " " + tableJoinDao.getJoinType() + " " + tableJoinDao.getRightTable() + " "
						+ aliasRightTableName;
				tableJoinStr += " ON (";
				for (int j = 0; j < tableJoinDao.getJoinFields().size(); j++) {
					String[] joinFields = tableJoinDao.getJoinFields().get(j);
					if (j > 0) {
						String[] split = joinFields[0].split("\\.");
						String[] split2 = joinFields[1].split("\\.");
						tableJoinStr += " AND ";
						tableJoinStr += tableNameCache.get(split[0]) + "." + split[1] + "="
								+ tableNameCache.get(split2[0]) + "." + split2[1];
						/*
						 * tableJoinStr += aliasLeftTableName + "." + joinFields[0] + "=" +
						 * aliasRightTableName + "." + joinFields[1];
						 */ } else {
						String[] split = joinFields[0].split("\\.");
						String[] split2 = joinFields[1].split("\\.");
						tableJoinStr += tableNameCache.get(split[0]) + "." + split[1] + "="
								+ tableNameCache.get(split2[0]) + "." + split2[1];
					}
				}
				tableJoinStr += ")";
			}
		}
		System.out.println(tableJoinStr);
		String orderStr = "";
		if (orders != null && orders.size() > 0) {
			orderStr = " ORDER BY";
			for (int i = 0; i < orders.size(); i++) {
				OrderDao orderDao = orders.get(i);
				String fields = orderDao.getField();
				String[] split = fields.split("\\.");
				String tablename = split[0];
				String fielld = split[1];
				//String aliasTableName = tableNameCache.get(orderDao.getTable());
				String aliasTableName = tableNameCache.get(tablename);
				// String aliasFieldName = tableFieldNameCache.get(orderDao.getField());
				if (i > 0)
					orderStr += ",";
				//orderStr += " " + aliasTableName + "." + orderDao.getField();
				orderStr += " " + aliasTableName + "." + aliasMap.get(fielld);
				orderStr += " " + orderDao.getOrder();
			}
		}
		System.out.println(orderStr);

		HashMap<String, String> fieldMap = new HashMap<>();
		if (tableFields.size() > 0) {
			for (TableFieldDao tableField : tableFields) {
				fieldMap.put(tableField.getAlias(), tableField.getField());
			}
		}
		ArrayList<Object> vList = new ArrayList<>();
		String sqls = "";
		if (conditions != null && conditions.size() > 0) {
			if (tableList.size() > 0) {
				for (int i = 0; i < conditions.size(); i++) {
					ConditionDao conditionDao = conditions.get(i);
					conditionDao.setTableName(tableNameCache.get(conditionDao.getTableName()));
				}
			}
			sqls = conditionsToString(sqls, fieldMap, conditions, vList);
		}

		// 分组
		String groupss = "";
		if (groups != null && groups.size() > 0) {
			for (int i = 0; i < groups.size(); i++) {
				GroupDao groupDao = groups.get(i);
				/*String fields = groupDao.getField();
				String[] split = fields.split("\\.");*/
				String tablename = groupDao.getTableName();
				String fielld = groupDao.getField();
				if (i == groups.size() - 1) {
					//groupss += tableNameCache.get(groupDao.getTableName()) + "." + groupDao.getField();
					groupss += tableNameCache.get(tablename) + "." + aliasMap.get(fielld);;
				} else {
					//groupss += tableNameCache.get(groupDao.getTableName()) + "." + groupDao.getField() + ",";
					groupss += tableNameCache.get(tablename) + "." + aliasMap.get(fielld) + ",";
				}
			}
		}
		/*
		 * if (groupss.trim().length() > 0) { groupss.substring(0,
		 * groupss.trim().length() - 1); }
		 */
		String limits = "";
		if (limit != null) {
			limits = "limit " + limit.getBeginIndex() + "," + limit.getCount();
		}
		// aliasToFiled(sqls,vList,tableFields,conditions);
		System.out.println("SELECT " + queryTableField + " FROM " + tableList.get(0) + " A" + tableJoinStr + orderStr);
		String sql = "SELECT " + queryTableField + " FROM " + tableList.get(0) + " A" + tableJoinStr;

		if (sqls != null && sqls.trim().length() > 0) {
			sql += " WHERE " + sqls;
		}
		if (groupss != null && groupss.trim().length() > 0) {
			sql += " GROUP BY " + groupss;
		}
		if (orderStr != null && orderStr.trim().length() > 0) {
			sql += " " + orderStr;
		}

		if (limits != null && limits.trim().length() > 0) {
			sql += " " + limits;
		}

		/*
		 * List<Map<String, Object>> queryMap = PDataBase.queryMap(service, sql, new
		 * Object[] {}, ""); System.out.println(queryMap.toString());
		 */

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		sql = PSqlUtils.consituteSymbols(sql, null);
		// 传入参数
		Object[] param = null;
		if (vList.size() > 0) {
			param = new Object[vList.size()];
			for (int i = 0; i < vList.size(); i++) {
				param[i] = vList.get(i);
			}
		}
		// Object[] param=new Object[] {};

		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return list;
		}
		sql = PSqlUtils.composeSql(sql, param);
		Connection conn = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return list;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst = JdbcLogger.prepareStatement(conn,sql,new ArrayList<>());
//			pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, param);
			rs = pst.executeQuery();

			ResultSetMetaData metaData = rs.getMetaData();
			int count = metaData.getColumnCount();
			String selectFields[] = new String[count];
			for (int i = 1; i <= count; i++) {
				selectFields[i - 1] = metaData.getColumnLabel(i);
			}
			list = PSqlUtils.convertResult(selectFields, rs, null);
		} catch (Exception e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "读取失败，请检查SQL语句或参数!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		List<Map<String, Object>> tempList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> tempMap = list.get(i);
			Map<String, Object> tempNewMap = new LinkedHashMap<String, Object>();
			Iterator<Entry<String, Object>> iterator = tempMap.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, Object> entry = iterator.next();
				String alias = tableFieldNameCache2.get(entry.getKey());
				Object tempObject = entry.getValue();
				String showType = tableFieldNameCache3.get(entry.getKey());
				if (showType != null) {
					tempObject = DataTypeUtil.transformDataBySimpleName(showType, tempObject);
				}
				tempNewMap.put(alias, tempObject);
			}
			tempList.add(tempNewMap);
		}
		return tempList;
	}

	/**
	 * 返回匹配条件中的所有字段的结果
	 *
	 * @param service
	 * @param tableName
	 *            数据库表名,支持多表和单表，单表：HOURDB A或HOURDB；多表：HOURDB A,IDELEMENT B,TABLE C
	 * @param tableField
	 *            查询的字段，如果为空，则查询所有字段，A.SENID,A.TIME,B.DESC
	 * @param joinSql
	 *            多表时级联类型，JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL
	 *            JOIN.分别为内联连，左连接，右连接，全连接.默认为内连接，单表为空
	 * @param condition
	 *            查询条件，全部同时满足
	 * @param orderField
	 *            排序，多表：A.SENID ASC,B.TIME DESC
	 * @return
	 */
	public static   <T> List<Map<String, Object>> queryListMapMatchAll(String service, String tableName,
																	   String tableField, String joinSql, Map<String, Object> condition, String orderField) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			// 查询不需要知道表的关键字
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			// 组织表,别名为Key,表名为Value
			Map<String, String> tableMap = new LinkedHashMap<String, String>();
			// 组织字段,第一级Key为表别名，第二级Key为字段名
			Map<String, Map<String, TableFieldProp>> fieldMap = new HashMap<String, Map<String, TableFieldProp>>();
			// 组织映射,第一级Key为表别名，第二级Key为前一表的字段，值为后一表的字段
			Map<String, Map<String, String>> fieldMappingMap = new HashMap<>();
			String[] queryTableArr = tableName.split(",");
			for (int i = 0; i < queryTableArr.length; i++) {
				String[] singleTableArr = queryTableArr[i].trim().split("[ ]+");
				if (singleTableArr.length > 1) {
					tableMap.put(singleTableArr[1].trim(), singleTableArr[0].trim());
				} else {
					tableMap.put(singleTableArr[0].trim(), singleTableArr[0].trim());
				}
				ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, singleTableArr[0].trim(), null);
				Map<String, TableFieldProp> singleFieldMap = new LinkedHashMap<String, TableFieldProp>();
				while (rs.next()) {
					String columnName = rs.getString("COLUMN_NAME").toUpperCase();
					TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
							rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
					singleFieldMap.put(columnName, tableFieldDao);
				}
				fieldMap.put(singleTableArr[0].trim(), singleFieldMap);
			}

			// 组织查询字段
			String queryTableField = tableField;
			// 如果未设置查询字段，则查询所有的表，默认以前面表的字段名称为主
			if (tableField == null || tableField.trim().equals("")) {
				queryTableField = "";
				Iterator<Entry<String, Map<String, TableFieldProp>>> iterator = fieldMap.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, Map<String, TableFieldProp>> entry = iterator.next();
					String tempTableName = entry.getKey();
					String tablePrefix = "";
					if (tableMap.size() > 1)
						tablePrefix = tempTableName + ".";
					Map<String, TableFieldProp> tempFieldMap = entry.getValue();
					Iterator<Entry<String, TableFieldProp>> iterator2 = tempFieldMap.entrySet().iterator();
					while (iterator2.hasNext()) {
						Entry<String, TableFieldProp> entry2 = iterator2.next();
						String fieldName = tablePrefix + entry2.getKey();
						queryTableField += queryTableField.equals("") ? fieldName : ("," + fieldName);
					}
				}
			}
			// 组织级连字段，连带表名
			String queryTableName = tableName;
			if (tableMap.size() > 1 && joinSql != null && !joinSql.equals("")) {
				queryTableName += " " + joinSql;
			}
			String querySql = "SELECT " + queryTableField + " FROM " + queryTableName;
			// 组织条件
			OperateTableData queryTableData = PSqlUtils.getQuerySqlOfMultiTable(fieldMap, condition);
			if (queryTableData != null) {
				querySql += " WHERE " + queryTableData.getJudgeStr();
			}
			if (orderField != null && !orderField.trim().equals("")) {
				querySql += " ORDER BY " + orderField;
			}
			//pst = conn.prepareStatement(querySql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst=JdbcLogger.prepareStatement(conn,querySql,new ArrayList<>());
			Object[] queryParamArr = null;
			if (queryTableData != null && queryTableData.getParams().length != 0) {
				queryParamArr = queryTableData.getParams()[0];
			}
			pst = JdbcLogger.prepareStatement(conn,querySql,new ArrayList<>());
//			pst = conn.prepareStatement(querySql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, queryParamArr);
			ResultSet rs = pst.executeQuery();

			ResultSetMetaData metaData = rs.getMetaData();
			int count = metaData.getColumnCount();
			String selectFields[] = new String[count];
			for (int i = 1; i <= count; i++) {
				selectFields[i - 1] = metaData.getColumnLabel(i);
			}
			return PSqlUtils.convertResult(selectFields, rs, null);
		} catch (Exception e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 返回匹配条件中的所有字段的结果
	 * @param service
	 * @param tableName
	 *            数据库表名,支持多表和单表，单表：HOURDB A或HOURDB；多表：HOURDB A,IDELEMENT B,TABLE C
	 * @param tableField
	 *            查询的字段，如果为空，则查询所有字段，A.SENID,A.TIME,B.DESC
	 * @param joinSql
	 *            多表时级联类型，JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL
	 *            JOIN.分别为内联连，左连接，右连接，全连接.默认为内连接，单表为空
	 * @param condition
	 *            查询条件，全部同时满足
	 * @param orderField
	 *            排序，多表：A.SENID ASC,B.TIME DESC
	 * @return
	 */
	public static   <T> List<Map<String, Object>> queryListMap(String service, String tableName,
															   String tableField, String joinSql, List<Map<String, Object>> condition, String orderField) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			// 查询不需要知道表的关键字
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			// 组织表,别名为Key,表名为Value
			Map<String, String> tableMap = new LinkedHashMap<String, String>();
			// 组织字段,第一级Key为表别名，第二级Key为字段名
			Map<String, Map<String, TableFieldProp>> fieldMap = new HashMap<String, Map<String, TableFieldProp>>();
			// 组织映射,第一级Key为表别名，第二级Key为前一表的字段，值为后一表的字段
			Map<String, Map<String, String>> fieldMappingMap = new HashMap<>();
			String[] queryTableArr = tableName.split(",");
			for (int i = 0; i < queryTableArr.length; i++) {
				String[] singleTableArr = queryTableArr[i].trim().split("[ ]+");
				if (singleTableArr.length > 1) {
					tableMap.put(singleTableArr[1].trim(), singleTableArr[0].trim());
				} else {
					tableMap.put(singleTableArr[0].trim(), singleTableArr[0].trim());
				}
				ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, singleTableArr[0].trim(), null);
				Map<String, TableFieldProp> singleFieldMap = new LinkedHashMap<String, TableFieldProp>();
				while (rs.next()) {
					String columnName = rs.getString("COLUMN_NAME").toUpperCase();
					TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
							rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
					singleFieldMap.put(columnName, tableFieldDao);
				}
				fieldMap.put(singleTableArr[0].trim(), singleFieldMap);
			}

			// 组织查询字段
			String queryTableField = tableField;
			// 如果未设置查询字段，则查询所有的表，默认以前面表的字段名称为主
			if (tableField == null || tableField.trim().equals("")) {
				queryTableField = "";
				Iterator<Entry<String, Map<String, TableFieldProp>>> iterator = fieldMap.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, Map<String, TableFieldProp>> entry = iterator.next();
					String tempTableName = entry.getKey();
					String tablePrefix = "";
					if (tableMap.size() > 1)
						tablePrefix = tempTableName + ".";
					Map<String, TableFieldProp> tempFieldMap = entry.getValue();
					Iterator<Entry<String, TableFieldProp>> iterator2 = tempFieldMap.entrySet().iterator();
					while (iterator2.hasNext()) {
						Entry<String, TableFieldProp> entry2 = iterator2.next();
						String fieldName = tablePrefix + entry2.getKey();
						queryTableField += queryTableField.equals("") ? fieldName : ("," + fieldName);
					}
				}
			}
			// 组织级连字段，连带表名
			String queryTableName = tableName;
			if (tableMap.size() > 1 && joinSql != null && !joinSql.equals("")) {
				queryTableName += " " + joinSql;
			}
			String querySql = "SELECT " + queryTableField + " FROM " + queryTableName;
			// 组织条件
			OperateTableData queryTableData = PSqlUtils.getQueryConditionSqlOfMultiTable(fieldMap, condition);
			if (queryTableData != null) {
				querySql += " WHERE " + queryTableData.getJudgeStr();
			}
			if (orderField != null && !orderField.trim().equals("")) {
				querySql += " ORDER BY " + orderField;
			}
			Object[] queryParamArr = null;
			if (queryTableData != null && queryTableData.getParams().length != 0) {
				queryParamArr = queryTableData.getParams()[0];
			}
			pst = JdbcLogger.prepareStatement(conn,querySql,new ArrayList<>());
//			pst = conn.prepareStatement(querySql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, queryParamArr);
			ResultSet rs = pst.executeQuery();

			ResultSetMetaData metaData = rs.getMetaData();
			int count = metaData.getColumnCount();
			String selectFields[] = new String[count];
			for (int i = 1; i <= count; i++) {
				selectFields[i - 1] = metaData.getColumnLabel(i);
			}
			return PSqlUtils.convertResult(selectFields, rs, null);
		} catch (Exception e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 返回匹配条件中的所有字段的结果
	 * @param service
	 * @param tableName
	 *            数据库表名,支持多表和单表，单表：HOURDB；多表：HOURDB,IDELEMENT,TABLE List<String>
	 * @param tableField
	 *            查询的字段，如果为空，则查询所有字段 List<> 单表:SENID AS senid,TIME AS time
	 *            多表:HOURDB.SENID AS senid,TABLE.TIME AS time,IDELEMENT.DESC AS
	 *            desc1
	 * @param joinSql
	 *            多表时级联类型，JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL
	 *            JOIN.分别为内联连，左连接，右连接，全连接.默认为内连接，单表为空
	 *            LEFT:TABLE1,TABLE2:TABLE1.SENID,TABLE2.SENID;TABLE1.TIME,TABLE2.TIME;RIGHT:TABLE1,TABLE3:TABLE1.SENID,TABLE3.SENID;TABLE1.TIME,TABLE3.TIME
	 * @param conditionSql
	 *            查询条件，全部同时满足
	 * @param param
	 *            排序 单表：SENID ASC,TIME DESC 多表：HOURDB.SENID ASC,TABLE.TIME DESC
	 * @return List<Map<String, Object>>
	 */
	public static   <T> List<Map<String, Object>> queryListMapBySql(String service, String tableName,
																	String tableField, String joinSql, String conditionSql, Object[] param) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			// 查询不需要知道表的关键字
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return null;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			// 组织表,别名为Key,表名为Value
			Map<String, String> tableMap = new LinkedHashMap<String, String>();
			// 组织字段,第一级Key为表别名，第二级Key为字段名
			Map<String, Map<String, TableFieldProp>> fieldMap = new HashMap<String, Map<String, TableFieldProp>>();
			// 组织映射,第一级Key为表别名，第二级Key为前一表的字段，值为后一表的字段
			Map<String, Map<String, String>> fieldMappingMap = new HashMap<>();
			String[] queryTableArr = tableName.split(",");
			for (int i = 0; i < queryTableArr.length; i++) {
				String[] singleTableArr = queryTableArr[i].trim().split("[ ]+");
				if (singleTableArr.length > 1) {
					tableMap.put(singleTableArr[1].trim(), singleTableArr[0].trim());
				} else {
					tableMap.put(singleTableArr[0].trim(), singleTableArr[0].trim());
				}
				ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, singleTableArr[0].trim(), null);
				Map<String, TableFieldProp> singleFieldMap = new LinkedHashMap<String, TableFieldProp>();
				while (rs.next()) {
					String columnName = rs.getString("COLUMN_NAME").toUpperCase();
					TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
							rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
					singleFieldMap.put(columnName, tableFieldDao);
				}
				fieldMap.put(singleTableArr[0].trim(), singleFieldMap);
			}

			// 组织查询字段
			String queryTableField = tableField;
			// 如果未设置查询字段，则查询所有的表，默认以前面表的字段名称为主
			if (tableField == null || tableField.trim().equals("")) {
				queryTableField = "";
				Iterator<Entry<String, Map<String, TableFieldProp>>> iterator = fieldMap.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, Map<String, TableFieldProp>> entry = iterator.next();
					String tempTableName = entry.getKey();
					String tablePrefix = "";
					if (tableMap.size() > 1)
						tablePrefix = tempTableName + ".";
					Map<String, TableFieldProp> tempFieldMap = entry.getValue();
					Iterator<Entry<String, TableFieldProp>> iterator2 = tempFieldMap.entrySet().iterator();
					while (iterator2.hasNext()) {
						Entry<String, TableFieldProp> entry2 = iterator2.next();
						String fieldName = tablePrefix + entry2.getKey();
						queryTableField += queryTableField.equals("") ? fieldName : ("," + fieldName);
					}
				}
			}
			// 组织级连字段，连带表名
			String queryTableName = tableName;
			if (tableMap.size() > 1 && joinSql != null && !joinSql.equals("")) {
				queryTableName += " " + joinSql;
			}
			String querySql = "SELECT " + queryTableField + " FROM " + queryTableName;
			if (conditionSql != null && !conditionSql.trim().equals("")) {
				querySql += " WHERE " + conditionSql;
			}
			pst = JdbcLogger.prepareStatement(conn,querySql,new ArrayList<>());
//			pst = conn.prepareStatement(querySql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, param);
			ResultSet rs = pst.executeQuery();

			ResultSetMetaData metaData = rs.getMetaData();
			int count = metaData.getColumnCount();
			String selectFields[] = new String[count];
			String aliasStr = "";
			if (tableField.toUpperCase().contains(" AS ")) {
				for (int i = 1; i <= count; i++) {
					selectFields[i - 1] = metaData.getColumnLabel(i);
					if (!aliasStr.equals(""))
						aliasStr += ";";
					aliasStr += selectFields[i - 1] + "-" + selectFields[i - 1];
				}
			} else {
				for (int i = 1; i <= count; i++) {
					selectFields[i - 1] = metaData.getColumnLabel(i);
				}
			}
			return PSqlUtils.convertResult(selectFields, rs, aliasStr);
		} catch (Exception e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	public  static  <T> List<T> query(String service, String sql, Object[] param, Object[] symbols,
									  Class<T> clazz, String aliasStr) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		List<T> list = new ArrayList<T>();
		sql = PSqlUtils.consituteSymbols(sql, symbols);
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return list;
		}
		sql = PSqlUtils.composeSql(sql, param);
		Connection conn = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return list;
			String tempSql=PSqlUtils.addQuotToAsName(sql,"\"","\"");
			pst = JdbcLogger.prepareStatement(conn,tempSql,Optional.ofNullable(param).map(Arrays::asList).orElseGet(() -> new ArrayList<>()));
//			pst = conn.prepareStatement(tempSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, param);
			rs = pst.executeQuery();
			aliasStr = PSqlUtils.judgeAlias(sql, aliasStr, rs);
			list = PSqlUtils.convertResult(clazz, rs, aliasStr);
		} catch (Exception e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "读取失败，请检查SQL语句或参数!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return list;
	}

	public  static List<Map<String, Object>> queryMap(String service, String sql, Object[] param, Object[] symbols, String aliasStr) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Connection conn = null;
		// String[] selectFields = PSqlUtils.getSelectFieldNames(sql);
		sql = PSqlUtils.consituteSymbols(sql, symbols);
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return list;
		}
		sql = PSqlUtils.composeSql(sql, param);
		PreparedStatement pst = null;
		ResultSet rs = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return list;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			String tempSql=PSqlUtils.addQuotToAsName(sql,"\"","\"");
			pst =JdbcLogger.prepareStatement(conn,tempSql,Optional.ofNullable(param).map(Arrays::asList).orElseGet(() -> new ArrayList<>()));
//			pst = conn.prepareStatement(tempSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, param);
			rs = pst.executeQuery();

			ResultSetMetaData metaData = rs.getMetaData();
			int count = metaData.getColumnCount();
			String selectFields[] = new String[count];
			for (int i = 1; i <= count; i++) {
				selectFields[i - 1] = metaData.getColumnLabel(i);
			}
			aliasStr = PSqlUtils.judgeAlias(sql, aliasStr, rs);
			list = PSqlUtils.convertResult(selectFields, rs, aliasStr);
		} catch (Exception e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "读取失败，请检查SQL语句或参数!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return list;
	}

	public static   Map<String, Object> queryMapByPaging(
			String service, String sql, Object[] param, Object[] symbols, String aliasStr,
			Integer currentPage,Integer pageSize, String rowNum) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		//1. 查询分页后总条数
		String queryTotalSql = StringUtil.replaceBetweenSelectAndFrom(sql.toUpperCase(), " COUNT(0) AS TOTAL ");
		List<Map<String, Object>> pageTotal = queryMap(service, queryTotalSql, param, symbols, null);
		if (pageTotal == null || pageTotal.size() == 0) return null;
		Object tempValue = pageTotal.get(0).get("TOTAL");
		Long total = null;
		if (tempValue instanceof Integer){
			total = ((Integer) tempValue).longValue();
		}else {
			total = (Long) tempValue;
		}

		//2.根据不同数据库类型，处理分页
		String pageSql = getPageSQL( service, sql, currentPage, pageSize, rowNum);
		List<Map<String, Object>> pageData = queryMap(service, pageSql, param, symbols, aliasStr);

		//3. 返回结果
		Map<String,Object> result = new HashMap<>();
		//3.1 当前页数
		result.put("currentPage",currentPage);
		//3.2 当前页数有多少条
		result.put("pageSize",pageSize);
		//3.3 总页数
		result.put("totalPage",(int) Math.ceil((double) total / pageSize));
		result.put("totalCount",total);
		//3.4 当前分页数据
		result.put("pageData",pageData);
		return result;
	}

	/**
	 * 根据不同类型数据库计算分页
	 * @param service 数据库服务
	 * @param sql 初始化sql
	 * @param currentPage 当前页数
	 * @param pageSize 当前页数条数
	 * @param orderBy 分仅支持oracle和sqlserver分页使用。排序字段
	 * @return sql
	 */
	public static String getPageSQL(String service, String sql, Integer currentPage, Integer pageSize, String orderBy) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		String pageSql = sql.toUpperCase();
		String databaseType = WcpJdbcService.getDatabaseType(WcpThreadLocal.getProjectId(), service);
		int offset = (currentPage - 1) * pageSize;
		if (databaseType != null){
			if (databaseType.contains("mysql") || databaseType.contains("dm")){
				pageSql = pageSql.toUpperCase() + " LIMIT " + pageSize + " OFFSET " + offset;
			}
			if (databaseType.contains("oracle")){
				int startRow = (currentPage - 1) * pageSize + 1;
				int endRow = currentPage * pageSize;
				pageSql =  "SELECT * FROM (" +
						"   SELECT T.*, ROWNUM AS RN FROM (" +
						"       " + pageSql +
						"   ) T" +
						") WHERE RN BETWEEN " + startRow +" AND " + endRow;
			}
			if (databaseType.contains("sqlserver")){
				int startRow = (currentPage - 1) * pageSize + 1;
				int endRow = currentPage * pageSize;
				String tempSql = StringUtil.addBetweenSelectAndFrom(pageSql, ", ROW_NUMBER() OVER(ORDER BY " + orderBy + ") AS RowNum ");
				pageSql = "WITH PAGING AS (" + tempSql +  ") SELECT * FROM PAGING WHERE ROWNUM BETWEEN " + startRow + " AND " + endRow;
			}
		}
		return pageSql;
	}

	/**
	 * 基于存储过程实例化CallableStatement对象(入参一定按照顺序写)
	 *
	 * @return
	 */
	public  static void executeStatement(String service, String sql, Object[] param) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		// sql = "{call LFF_TEST_PACKAGE.LFF_TEST_PROCEDURE(?,?,?,?)}";
		// 请求的存储过程(包名.存储过程名称，四个?表示参数)
		System.out.println("执行存储过程" + service + ":" + sql);
		Connection conn = null;
		CallableStatement statement = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
				System.out.println("检查到SQL注入" + service + ":" + sql);
				return;
			}
			statement = conn.prepareCall("{call " + sql + "}");
			PSqlUtils.paramBinding(1, statement, param);// 传入的参数
			boolean execute = statement.execute();
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if(statement!=null){
					statement.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
	}

	public  static List<Map<String, Object>> getStatement(String service, String sql, Object[] param,
														  String aliasStr) {
		sql = PSqlUtils.convertTableNamesToLowerCase(sql);
		// sql = "{call LFF_TEST_PACKAGE.LFF_TEST_PROCEDURE(?,?,?,?)}";
		// 请求的存储过程(包名.存储过程名称，四个?表示参数)
		System.out.println("执行存储过程" + service + ":" + sql);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Connection conn = null;
		CallableStatement statement = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return list;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			statement = conn.prepareCall("{call " + sql + "}");
			if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
				System.out.println("检查到SQL注入" + service + ":" + sql);
				return list;
			}
			PSqlUtils.paramBinding(1, statement, param);// 传入的参数
			System.out.println(statement.execute());
			ResultSetMetaData rsmd = statement.getMetaData();// rs为查询结果集
			ResultSet rs = statement.getResultSet();
			int count = rsmd.getColumnCount();
			String selectFields[] = new String[count];
			for (int i = 1; i <= count; i++) {
				selectFields[i - 1] = rsmd.getColumnLabel(i);
			}
			aliasStr = PSqlUtils.judgeAlias(sql, aliasStr, rs);
			list = PSqlUtils.convertResult(selectFields, rs, aliasStr);
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if(statement!=null){
					statement.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return list;
	}


	public  static <T> boolean updateListsMap(String service, String tableName,
											  List<TableFieldDao> tableFields, List<Map<String, Object>> param, List<ConditionDao> conditions)
			throws RemoteException {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		ArrayList<Object> vList = new ArrayList<>();
		String updateSql = "UPDATE " + tableName + " SET ";
		String sqls = "";
		if (param.size() > 0) {
			Map<String, Object> map = param.get(0);
			Iterator<Entry<String, Object>> iterator = map.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, Object> entry = iterator.next();
				System.out.println("key= " + entry.getKey() + " and value= " + entry.getValue());
				vList.add(entry.getValue());
				updateSql += entry.getKey() + "= ?,";
			}
			updateSql = updateSql.substring(0, updateSql.length() - 1);
		}
		sqls = aliasToFiled(sqls, vList, tableFields, conditions);
		updateSql = updateSql + " WHERE " + sqls;
		System.out.println(updateSql);
		Object[] params = null;
		if (vList.size() > 0) {
			params = new Object[vList.size()];
			for (int i = 0; i < vList.size(); i++) {
				params[i] = vList.get(i);
			}
		}
		updateSql = PSqlUtils.composeSql(updateSql, params);
		Connection conn = null;
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + updateSql);
			return false;
		}
		PreparedStatement pst = null;
		boolean success = false;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return false;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst = JdbcLogger.prepareStatement(conn,updateSql,new ArrayList<>());
//			pst = conn.prepareStatement(updateSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			PSqlUtils.paramBinding(1, pst, params);
			if (pst.executeUpdate() > 0) {
				success = true;
			}
			if(!WcpConnection.isOpenTransaction()) {
				conn.commit();
			}
		} catch (SQLException e) {
			String tempStr = PPathUtil.convertValidLog(updateSql);
			logger.error("SQL:" + tempStr + "执行失败，请检查SQL语句或数据表!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}

		return success;
	}

	public  static <T> boolean insertListsMap(String service, String tableName,
											  List<TableFieldDao> tableFields, List<Map<String, Object>> param) {
		boolean success = false;
		Connection conn = null;
		PreparedStatement pst = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return false;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			DatabaseMetaData dbmd = conn.getMetaData();
			ResultSet rs = dbmd.getColumns(conn.getCatalog(), null, tableName, null);
			Map<String, TableFieldProp> fieldMap = new HashMap<String, TableFieldProp>();
			List<String> tableFieldList = new ArrayList<>();
			String fieldNames = "";
			while (rs.next()) {
				String columnName = rs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = new TableFieldProp(rs.getInt("DATA_TYPE"), columnName,
						rs.getInt("COLUMN_SIZE"), rs.getInt("DECIMAL_DIGITS"));
				fieldMap.put(columnName, tableFieldDao);
				fieldNames += "".equals(fieldNames) ? columnName : ("," + columnName);
				tableFieldList.add(columnName);
			}
			ResultSet keyRs = dbmd.getPrimaryKeys(conn.getCatalog(), null, tableName);
			while (keyRs.next()) {
				String columnName = keyRs.getString("COLUMN_NAME").toUpperCase();
				TableFieldProp tableFieldDao = fieldMap.get(columnName);
				if (tableFieldDao != null) {
					tableFieldDao.setPrimary(true);
					tableFieldDao.setPrimarySeq(keyRs.getInt("KEY_SEQ"));
				}
			}
			// 先删除
			OperateTableData deleteTableData = PSqlUtils.getJudgeSql(fieldMap, param);
			if (deleteTableData == null) {
				return false;
			}
			String deleteSql = "DELETE FROM " + tableName + " WHERE " + deleteTableData.getJudgeStr();
			pst = JdbcLogger.prepareStatement(conn,deleteSql,new ArrayList<>());
//			pst = conn.prepareStatement(deleteSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			int exeLastCount = 0;
			int exeCount = 0;
			Object[][] deleteParams = deleteTableData.getParams();
			for (int i = 0; i < deleteParams.length; i++) {
				try {
					PSqlUtils.paramBinding(1, pst, deleteParams[i]);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					logger.error("SQL:" + "数据绑定失败，请检查SQL语句或参数!");
					logger.error(e.getMessage());
					e.printStackTrace();
					return success;
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						pst.executeBatch();
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + deleteSql + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
			pst.close();
			// 再插入
			int[] backFalg = new int[param.size()];
			String tempParamStr = PSqlUtils.getPlaceholder(fieldMap.size());
			String insertSql = "INSERT INTO " + tableName + "(" + fieldNames + ") VALUES (" + tempParamStr + ")";
			pst = JdbcLogger.prepareStatement(conn,insertSql,new ArrayList<>());
//			pst = conn.prepareStatement(insertSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			exeLastCount = 0;
			exeCount = 0;
			System.out.println("执行SQL语句" + service + ":" + insertSql);
			for (int i = 0; i < param.size(); i++) {
				try {
					Map<String, Object> objMap = param.get(i);
					Object[] tempParams = new Object[tableFieldList.size()];
					for (int j = 0; j < tempParams.length; j++) {
						TableFieldProp tableFieldDao = fieldMap.get(tableFieldList.get(j));
						tempParams[j] = PSqlUtils.transToTableData(objMap.get(tableFieldList.get(j)), tableFieldDao);
					}
					PSqlUtils.paramBinding(1, pst, tempParams);
					pst.addBatch();
					exeCount++;
				} catch (Exception e) {
					logger.error("SQL:" + "数据绑定失败，请检查SQL语句或参数!");
					logger.error(e.getMessage());
					e.printStackTrace();
				}
				if (exeCount % 500 == 0 || i == (param.size() - 1)) {
					try {
						int[] tempInt = pst.executeBatch();
						for (int j = exeLastCount; j < tempInt.length; j++) {
							backFalg[exeLastCount + j] = tempInt[j];
						}
						if(!WcpConnection.isOpenTransaction()) {
							conn.commit();
						}
						success=true;
					} catch (Exception e) {
						logger.error("SQL:" + insertSql + "执行失败，请检查SQL语句或参数!");
						if(WcpConnection.isOpenTransaction()) {
							throw new RuntimeException(e);
						}
						e.printStackTrace();
					}
					exeCount = 0;
					exeLastCount = i;
				}
			}
			success = backFalg.length > 0 && StringUtil.containsZero(backFalg);
		} catch (SQLException e) {
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	public  static  <T> boolean deleteListsMap(String service, String tableName,
											   List<TableFieldDao> tableFields, List<ConditionDao> conditions) {
		// System.out.println("执行SQL语句" + service + ":" + sql);
		String sqls = "";
		ArrayList<Object> vList = new ArrayList<>();
		String aliasToFiled = aliasToFiled(sqls, vList, tableFields, conditions);
		String sql = "DELETE FROM " + tableName + " WHERE " + aliasToFiled;
		Object[] param = null;
		if (vList.size() > 0) {
			param = new Object[vList.size()];
			for (int i = 0; i < vList.size(); i++) {
				param[i] = vList.get(i);
			}
		}
		sql = PSqlUtils.composeSql(sql, param);

		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return false;
		}
		Connection conn = null;
		PreparedStatement pst = null;
		boolean success = false;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return false;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst = JdbcLogger.prepareStatement(conn,sql,new ArrayList<>());
//			pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			PSqlUtils.paramBinding(1, pst, param);
			if (pst.executeUpdate() > 0) {
				success = true;
			}
			if(!WcpConnection.isOpenTransaction()) {
				conn.commit();
			}
		} catch (SQLException e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "执行失败，请检查SQL语句或数据表!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return success;
	}

	private  static String aliasToFiled(String sql, List<Object> vList, List<TableFieldDao> tableFields,
										List<ConditionDao> conditions) {
		HashMap<String, String> fieldMap = new HashMap<>();
		if (tableFields.size() > 0) {
			for (TableFieldDao tableField : tableFields) {
				if (tableField.getAlias() == null || tableField.getAlias().trim().length() == 0) {
					fieldMap.put(tableField.getField(), tableField.getField());
				} else {
					fieldMap.put(tableField.getAlias(), tableField.getField());
				}
			}
		}
		String conditionsToString = conditionsToString(sql, fieldMap, conditions, vList);
		return conditionsToString;
	}

	private  static void conditionsToConditions(HashMap<String, String> fieldMap,
												List<ConditionDao> conditionss) {
		if(conditionss!=null&&conditionss.size()>0) {
			for(int k=0;k<conditionss.size();k++) {
				ConditionDao conditionDao = conditionss.get(k);
				String field = conditionDao.getField();
				String[] split = field.split("\\.");
				//conditionDao.setTableName(split[0]);
				conditionDao.setField(split[1]);
				List<ConditionDao> childrens = conditionDao.getChildrens();
				if(childrens!=null&&childrens.size()>0) {
					conditionsToConditions(fieldMap,childrens);
				}
			}
		}

	}

	private  static String conditionsToString(String sql, HashMap<String, String> fieldMap,
											  List<ConditionDao> conditionss, List<Object> vList) {
		ArrayList<ConditionDao> conditions = new ArrayList<>();
		if(conditionss!=null&&conditionss.size()>0) {
			for(int k=0;k<conditionss.size();k++) {
				ConditionDao conditionDao = conditionss.get(k);
				String field = conditionDao.getField();
				String[] split = field.split("\\.");
				//conditionDao.setTableName(split[0]);
				conditionDao.setField(split[1]);
				List<ConditionDao> childrens = conditionDao.getChildrens();
				if(childrens!=null&&childrens.size()>0) {
					conditionsToConditions(fieldMap,childrens);
				}
				conditions.add(conditionDao);
			}
		}
		for (int i = 0; i < conditions.size(); i++) {
			ConditionDao conditionDao = conditions.get(i);
			if (conditionDao.getTableName() != null && conditionDao.getTableName().trim().length() != 0) {
				if (i == 0) {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sql += " " + conditionDao.getTableName() + "." + fieldMap.get(conditionDao.getField())
							+ judgeToValue;
				} else {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sql += " " + conditionDao.getLinkType() + " " + conditionDao.getTableName() + "."
							+ fieldMap.get(conditionDao.getField()) + judgeToValue;
				}
			} else {
				if (i == 0) {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sql += " " + fieldMap.get(conditionDao.getField()) + judgeToValue;
				} else {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sql += " " + conditionDao.getLinkType() + " " + fieldMap.get(conditionDao.getField())
							+ judgeToValue;
				}
			}

			// vList.add(conditionDao.getValue());
			if (conditionDao.getChildrens() != null && conditionDao.getChildrens().size() > 0) {
				String sqlChildren = "";
				conditionsChildrensToString(sqlChildren, fieldMap, conditionDao.getChildrens(), vList);
				if (sqlChildren.trim().length() > 0) {
					sql = sql + "and (" + sqlChildren + ")";
				}
			}
		}
		/*
		 * for (ConditionDao conditionDao : conditions) { sql+=
		 * conditionDao.getLinkType()+" "+fieldMap.get(conditionDao.getField())+
		 * conditionDao.getJudge()+"? "; vList.add(conditionDao.getValue());
		 * if(conditionDao.getChildrens().size()>0) { String sqlChildren="";
		 * conditionsChildrensToString(sqlChildren,fieldMap,conditionDao.getChildrens(),
		 * vList); if(sqlChildren.trim().length()>0) { sql=sql+"("+sqlChildren+")"; } }
		 * }
		 *
		 */
		return sql;
	}

	private static  String judgeToValue(String judge, List<Object> vList, Object object) {
		if (judge.equals("LIKE")) {
			vList.add("%" + object + "%");
			return " LIKE ? ";
		} else if (judge.equals("BETWEEN")) {
			Object[] v = (Object[]) object;
			for (int i = 0; i < v.length; i++) {
				vList.add(v[i]);
			}
			return " BETWEEN  ? and ?";
		} else if (judge.equals("IN")) {
			vList.add(object);
			return " IN ( ? )";
		} else if (judge.equals("IS NULL")) {
			return " IS NULL";
		} else if (judge.equals("IS NOT NULL")) {
			return " IS NOT NULL";
		} else {
			vList.add(object);
			return " " + judge + " ?";
		}
	}

	private  static void conditionsChildrensToString(String sqlChildren, HashMap<String, String> fieldMap,
													 List<ConditionDao> conditions, List<Object> vList) {
		sqlChildren += "(";
		for (int i = 0; i < conditions.size(); i++) {
			ConditionDao conditionDao = conditions.get(i);
			if (conditionDao.getTableName() != null && conditionDao.getTableName().trim().length() != 0) {
				if (i == 0) {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sqlChildren += " " + conditionDao.getTableName() + "." + fieldMap.get(conditionDao.getField())
							+ judgeToValue;
				} else {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sqlChildren += conditionDao.getLinkType() + " " + conditionDao.getTableName() + "."
							+ fieldMap.get(conditionDao.getField()) + judgeToValue;
				}
			} else {
				if (i == 0) {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sqlChildren += " " + conditionDao.getTableName() + "." + fieldMap.get(conditionDao.getField())
							+ judgeToValue;
				} else {
					String judgeToValue = judgeToValue(conditionDao.getJudge(), vList, conditionDao.getValue());
					sqlChildren += conditionDao.getLinkType() + " " + fieldMap.get(conditionDao.getField())
							+ judgeToValue;
				}
			}

			// vList.add(conditionDao.getValue());
			if (conditionDao.getChildrens().size() > 0) {
				conditionsChildrensToString(sqlChildren, fieldMap, conditionDao.getChildrens(), vList);
			}
		}
		sqlChildren += ")";
		/*
		 * for (ConditionDao conditionDao : conditions) { sqlChildren+=
		 * conditionDao.getLinkType()+" "+fieldMap.get(conditionDao.getField())+
		 * conditionDao.getJudge()+"? "; vList.add(conditionDao.getValue());
		 * if(conditionDao.getChildrens().size()>0) {
		 * conditionsChildrensToString(sqlChildren,fieldMap,conditionDao.getChildrens(),
		 * vList); } }
		 */

	}

	public static  int queryDataListByRuleCount(String service, List<String> tableList,
												List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins, List<ConditionDao> conditions,
												List<GroupDao> groups, LimitDao limits, List<OrderDao> orders) {
		// 表名和别名的对应缓存
		Map<String, String> tableNameCache = new LinkedHashMap<String, String>();
		// 字段名和字段别名的对应缓存
		Map<String, String> tableFieldNameCache = new LinkedHashMap<String, String>();
		// 字段别名和查询出的字段名的对应缓存
		Map<String, String> tableFieldNameCache1 = new LinkedHashMap<String, String>();
		// 查询出的字段名和字段别名的对应缓存
		Map<String, String> tableFieldNameCache2 = new LinkedHashMap<String, String>();
		// 查询的字段名和数据类型的对应缓存
		Map<String, String> tableFieldNameCache3 = new LinkedHashMap<String, String>();
		char tempTableAlias = 'A';
		for (int i = 0; i < tableList.size(); i++) {
			tableNameCache.put(tableList.get(i).toUpperCase(), tempTableAlias++ + "");
		}
		int aggFuncCount = 1;
		// 组织查询字段
		String queryTableField = "COUNT(*)";
		HashMap<Object, Object> aliasMap = new HashMap<>();
		if (tableFields != null && tableFields.size() > 0) { // 给出查询字段,则查询给定的字段
			for (int i = 0; i < tableFields.size(); i++) {
				TableFieldDao tableFieldDao = tableFields.get(i);
				String tableName = tableFieldDao.getTableName();
				// 如果未设置字段所在的表名，则认为是第一个表的字段
				if (tableName == null || tableName.trim().equals("")) {
					tableName = tableList.get(0).toUpperCase();
				}
				// 查找表的别名,如果没有别名,则往表格中添加一个别名
				String tableAlias = PQueryUtil.queryTableAlias(tableNameCache, tableName.toUpperCase());
				String tableField = tableFieldDao.getField().toUpperCase();
				String tableFieldAlias = tableFieldDao.getAlias();
				String aggFunc = tableFieldDao.getAggFunc();
				aliasMap.put(tableFieldAlias, tableField);
				// 如果没有设置别名,则默认为表的字段
				if (tableFieldAlias == null || tableFieldAlias.trim().equals("")) {
					tableFieldAlias = tableField;
					// if(aggFunc != null && aggFunc)
				}
				String queryFieldName = tableAlias + "_" + tableField;
				tableFieldNameCache.put(tableField, tableFieldAlias);
				tableFieldNameCache1.put(tableFieldAlias, queryFieldName);
				tableFieldNameCache2.put(queryFieldName, tableFieldAlias);
				tableFieldNameCache3.put(queryFieldName, tableFieldDao.getFieldShowType());

			}
		}
		String tableJoinStr = "";
		if (tableJoins != null && tableJoins.size() > 1) {
			for (int i = 1; i < tableJoins.size(); i++) {
				TableJoinDao tableJoinDao = tableJoins.get(i);
				String aliasRightTableName = tableNameCache.get(tableJoinDao.getRightTable());
				tableJoinStr += " " + tableJoinDao.getJoinType() + " " + tableJoinDao.getRightTable() + " "
						+ aliasRightTableName;
				tableJoinStr += " ON (";
				for (int j = 0; j < tableJoinDao.getJoinFields().size(); j++) {
					String[] joinFields = tableJoinDao.getJoinFields().get(j);
					if (j > 0) {
						String[] split = joinFields[0].split("\\.");
						String[] split2 = joinFields[1].split("\\.");
						tableJoinStr += " AND ";
						tableJoinStr += tableNameCache.get(split[0]) + "." + split[1] + "="
								+ tableNameCache.get(split2[0]) + "." + split2[1];
					} else {
						String[] split = joinFields[0].split("\\.");
						String[] split2 = joinFields[1].split("\\.");
						tableJoinStr += tableNameCache.get(split[0]) + "." + split[1] + "="
								+ tableNameCache.get(split2[0]) + "." + split2[1];
					}
				}
				tableJoinStr += ")";
			}
		}
		System.out.println(tableJoinStr);
		String orderStr = "";
		if (orders != null && orders.size() > 0) {
			orderStr = " ORDER BY";
			for (int i = 0; i < orders.size(); i++) {
				OrderDao orderDao = orders.get(i);
				String fields = orderDao.getField();
				String[] split = fields.split("\\.");
				String tablename = split[0];
				String fielld = split[1];
				String aliasTableName = tableNameCache.get(tablename);
				if (i > 0)
					orderStr += ",";
				orderStr += " " + aliasTableName + "." + aliasMap.get(fielld);
				orderStr += " " + orderDao.getOrder();
			}
		}
		System.out.println(orderStr);

		HashMap<String, String> fieldMap = new HashMap<>();
		if (tableFields.size() > 0) {
			for (TableFieldDao tableField : tableFields) {
				fieldMap.put(tableField.getField(), tableField.getField());
			}
		}
		ArrayList<Object> vList = new ArrayList<>();
		String sqls = "";
		if (conditions != null && conditions.size() > 0) {
			if (tableList.size() > 0) {
				for (int i = 0; i < conditions.size(); i++) {
					ConditionDao conditionDao = conditions.get(i);
					conditionDao.setTableName(tableNameCache.get(conditionDao.getTableName()));
				}
			}
			sqls = conditionsToString(sqls, fieldMap, conditions, vList);
		}

		// 分组
		String groupss = "";
		if (groups != null && groups.size() > 0) {
			for (int i = 0; i < groups.size(); i++) {
				GroupDao groupDao = groups.get(i);
				String tablename = groupDao.getTableName();
				String fielld = groupDao.getField();
				if (i == groups.size() - 1) {
					groupss += tableNameCache.get(tablename) + "." + aliasMap.get(fielld);;
				} else {
					groupss += tableNameCache.get(tablename) + "." + aliasMap.get(fielld) + ",";
				}
			}
		}


		System.out.println("SELECT " + queryTableField + " FROM " + tableList.get(0) + " A" + tableJoinStr + orderStr);
		String sql = "SELECT " + queryTableField + " FROM " + tableList.get(0) + " A" + tableJoinStr;

		if (sqls != null && sqls.trim().length() > 0) {
			sql += " WHERE " + sqls;
		}
		if (groupss != null && groupss.trim().length() > 0) {
			sql += " GROUP BY " + groupss;
		}
		if (orderStr != null && orderStr.trim().length() > 0) {
			sql += " " + orderStr;
		}
		Object[] param = null;
		if (vList.size() > 0) {
			param = new Object[vList.size()];
			for (int i = 0; i < vList.size(); i++) {
				param[i] = vList.get(i);
			}
		}
		List<Integer> query = query( service, sql, param, null, Integer.class, null);
		if(query!=null) {
			Integer count = query.get(0);
			return count;
		}
		return 0;
	}

	public  static List<Map<String, Object>> queryLocalDateMap(String service, String sql, Object[] param, Object[] symbols, String aliasStr) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Connection conn = null;
		// String[] selectFields = PSqlUtils.getSelectFieldNames(sql);
		sql = PSqlUtils.consituteSymbols(sql, symbols);
		if (PSqlUtils.sqlParamCheck(param, new HashSet<Object>())) {
			System.out.println("检查到SQL注入" + service + ":" + sql);
			return list;
		}
		sql = PSqlUtils.composeSql(sql, param);
		PreparedStatement pst = null;
		ResultSet rs = null;
		try {
			conn = WcpConnection.getTransactionConnect( WcpThreadLocal.getProjectId(), service);
			if(conn == null) return list;
			if(!WcpConnection.isOpenTransaction()) {
				conn.setAutoCommit(false);
			}
			pst = JdbcLogger.prepareStatement(conn,sql,Optional.ofNullable(param).map(Arrays::asList).orElseGet(() -> new ArrayList<>()));
//			pst = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
			pst.setFetchSize(30);
			PSqlUtils.paramBinding(1, pst, param);
			rs = pst.executeQuery();

			ResultSetMetaData metaData = rs.getMetaData();
			int count = metaData.getColumnCount();
			String selectFields[] = new String[count];
			for (int i = 1; i <= count; i++) {
				selectFields[i - 1] = metaData.getColumnLabel(i);
			}
			aliasStr = PSqlUtils.judgeAlias(sql, aliasStr, rs);
			list = PSqlUtils.convertLocalDateResult(selectFields, rs, aliasStr);
		} catch (Exception e) {
			String tempStr = PPathUtil.convertValidLog(sql);
			logger.error("SQL:" + tempStr + "读取失败，请检查SQL语句或参数!");
			logger.error(e.getMessage());
			if(WcpConnection.isOpenTransaction()) {
				throw new RuntimeException(e);
			} else {
				e.printStackTrace();
			}
		} finally {
			try {
				if(pst!=null){
					pst.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			try {
				if (conn != null && !WcpConnection.isOpenTransaction()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return list;
	}
}
