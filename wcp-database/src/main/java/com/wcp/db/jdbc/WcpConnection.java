package com.wcp.db.jdbc;

import com.alibaba.druid.pool.DruidDataSource;
import io.minio.messages.JsonOutputSerialization;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

/**
 * 数据加接池管理器
 * <AUTHOR>
 */
@Component
public class WcpConnection {
	private static Logger logger = Logger.getLogger(WcpConnection.class);
	// 所有的项目下的数据连接资源
	private static volatile Map<String, Map<String, DruidDataSource>> connectPools;
	// 存储是否共享的平台的数据连接资源
	private static volatile Map<String, Set<String>> sharePolls;
	// 事务启动记录器  {openTransaction: true : false}
	private static InheritableThreadLocal<Map<String, String>> transactionThreadLocal;
	// 连接池缓存类
	private static InheritableThreadLocal<Map<String, java.sql.Connection>> connectionThreadLocal;
	static {
		connectPools = new HashMap<String, Map<String, DruidDataSource>>();
		sharePolls = new HashMap<String, Set<String>>();
		transactionThreadLocal = new InheritableThreadLocal<Map<String, String>>();
		connectionThreadLocal = new InheritableThreadLocal<Map<String, Connection>>();
	}

	/**
	 * 开启事务
	 */
	public static void openTransaction() {
		Map<String, String> tempMap = transactionThreadLocal.get();
		if(tempMap == null) {
			tempMap = new HashMap<String, String>();
			transactionThreadLocal.set(tempMap);
		}
		tempMap.put("openTransaction", "true");
	}

	/**
	 * 关闭事务
	 */
	public static void closeTransaction() {
		Map<String, String> tempMap = transactionThreadLocal.get();
		if(tempMap == null) return;
		tempMap.remove("openTransaction");
		closeTransactionConnect();
	}

	/**
	 * 是否已开启事务
	 * @return
	 */
	public static boolean isOpenTransaction() {
		Map<String, String> tempMap = transactionThreadLocal.get();
		if(tempMap == null) return false;
		if("true".equals(tempMap.get("openTransaction"))) {
			return true;
		}
		return false;
	}

	/**
	 * 事务提交
	 * @throws SQLException
	 */
	public static void transactionCommit() throws SQLException {
		if(!isOpenTransaction()) return;
		Map<String, Connection> connectionMap = connectionThreadLocal.get();
		if(connectionMap == null) return;
		for (Connection connection : connectionMap.values()) {
			connection.commit();
		}
	}

	/**
	 * 事务提交
	 * @throws SQLException
	 */
	public static void transactionRollback() {
		if(!isOpenTransaction()) return;
		Map<String, Connection> connectionMap = connectionThreadLocal.get();
		if(connectionMap == null) return;
		for (Connection connection : connectionMap.values()) {
			try {
				connection.rollback();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
	}

	public static Connection getTransactionConnect(String projectId, String service) throws SQLException {
		// 如果没有开启事务，调用原先的连接方法进行连接
		if("wcp".equals(service)){
			projectId="default";
		}
		if(!isOpenTransaction()) {
			return getConnection(projectId, service);
		}
		// 开启事务后，优先从事务中获取连接
		String conKey = projectId + ":" + service;
		Map<String, Connection> connectionMap = connectionThreadLocal.get();
		if(connectionMap == null) {
			connectionMap = new HashMap<String, Connection>();
			connectionThreadLocal.set(connectionMap);
		}
		Connection connection = connectionMap.get(conKey);
		if(connection == null) {
			connection = getConnection(projectId, service);
			if(connection != null) {
				connection.setAutoCommit(false);
				connectionMap.put(conKey, connection);
			}
		}
		return connection;
	}
	/**
	 * 关闭事务连接
	 */
	public static void closeTransactionConnect() {
		if(!isOpenTransaction()) return;
		Map<String, Connection> connectionMap = connectionThreadLocal.get();
		if(connectionMap == null) return;
		for (Connection connection : connectionMap.values()) {
			try {
				connection.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		connectionThreadLocal.remove();
	}
	/**
	 * 是否共享的平台的数据库连接资源池
	 * @param projectId    项目编码
	 * @param service      连接服务
	 * @return
	 */
	public static boolean isSharePool(String projectId, String service) {
		Set<String> shareSet = sharePolls.get(projectId);
		if(shareSet == null) return false;
		return shareSet.contains(service);
	}
	/**
	 * 设置当前服务是共享平台连接池标志
	 * @param projectId    项目编码
	 * @param service      连接服务
	 */
	public static void setSharePool(String projectId, String service) {
		Set<String> shareSet = sharePolls.get(projectId);
		if(shareSet == null) {
			shareSet = new HashSet<String>();
			sharePolls.put(projectId, shareSet);
		}
		shareSet.add(service);
	}
	/**
	 * 移除当前项目下所有的共享平台连接池标志
	 * @param projectId    项目编码
	 */
	public static void removeSharePool(String projectId) {
		sharePolls.remove(projectId);
	}
	/**
	 * 移除当前服务是共享平台连接池标志
	 * @param projectId    项目编码
	 * @param service      连接服务
	 */
	public static void removeSharePool(String projectId, String service) {
		Set<String> shareSet = sharePolls.get(projectId);
		if(shareSet == null) return;
		shareSet.remove(service);
	}
	/**
	 * 移除指定项目和服务下的数据库连接
	 * @param projectId    项目编码
	 * @param service      连接服务
	 */
	public static void removeConnectionManager(String projectId, String service) {
		Map<String, DruidDataSource> tempMap = connectPools.get(projectId);
		if(tempMap == null) return;
		DruidDataSource druidDataSource = tempMap.remove(service);
		if(druidDataSource == null) return;
		// 只有单独使用的连接,才需要关闭
		if(!isSharePool(projectId, service)) {
			druidDataSource.close();
		}
		removeSharePool(projectId, service);
	}
	/**
	 * 移除某项目下的所有数据库连接
	 * @param projectId    项目编码
	 */
	public static void removeConnectionManager(String projectId) {
		Map<String, DruidDataSource> tempMap = connectPools.remove(projectId);
		if(tempMap == null) return;
		Iterator<Entry<String, DruidDataSource>> iterator = tempMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, DruidDataSource> entry = iterator.next();
			// 只有单独使用的连接,才需要关闭
			if(!isSharePool(projectId, entry.getKey())) {
				entry.getValue().close();
			}
		}
		removeSharePool(projectId);

		System.out.println(connectPools.size());
	}
	/**
	 * 测试当前配置是否能否连接成功
	 * @param tempjdbcService
	 * @return
	 */
	public static boolean connectTesting(WcpJdbcService tempjdbcService) {
		Connection conn = null;
		try {
			Class.forName(tempjdbcService.getDriver());
			conn = DriverManager.getConnection(tempjdbcService.getDUrl() , tempjdbcService.getDUser() , tempjdbcService.getDPsd()) ;
	        if(conn == null) return false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		} finally {
			if(conn != null) {
				try {
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return true;
	}
	
	/**
	 * 测试当前配置是否能否连接成功
	 * @param projectId
	 * @param service
	 * @return
	 */
	public  boolean connectTesting(String projectId, String service) {
		WcpJdbcService tempjdbcService = WcpJdbcService.getJdbcService(projectId, service);
		Connection conn = null;
		try {
			Class.forName(tempjdbcService.getDriver());
			conn = DriverManager.getConnection(tempjdbcService.getDUrl() , tempjdbcService.getDUser() , tempjdbcService.getDPsd()) ;
	        if(conn == null) return false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		} finally {
			if(conn != null) {
				try {
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return true;
	}
	
	/**
	 * 返回指定服务的数据连接池
	 * @param service    数据服务标志
	 * @return
	 */
	public static DruidDataSource getConnectionManager(String projectId, String service){
		if("wcp".equals(service)){
			projectId="default";
		}
		Map<String, DruidDataSource> tempDataSourceMap = connectPools.get(projectId);
		if(tempDataSourceMap == null) {
			tempDataSourceMap = new HashMap<String, DruidDataSource>();
			connectPools.put(projectId, tempDataSourceMap);
		}
		DruidDataSource dataSource = tempDataSourceMap.get(service);
		// 当数据资源未取到时,重新去获取加载对应的数据资源
		if(dataSource == null) {
			WcpJdbcService tempjdbcService = WcpJdbcService.getJdbcService(projectId, service);
				//获取不成功时启用默认服务
				if (tempjdbcService == null) {
					logger.error("数据服务：‘" + service + "’未配置");
					return null;
				}
				// 如果该项目配置了单独的数据连接资源,则直接重新创建数据连接,或者从平台获取默认的连接,如果获取不到再重析连接
//				if(!projectId.equals(jdbcService.getProject())) {
//					dataSource = getConnectionManager(jdbcService.getProject(), service);
//					setSharePool(projectId, service);
//				}
				if(dataSource == null) {
					try {
						dataSource = new DruidDataSource();
						dataSource.setDriverClassName(tempjdbcService.getDriver());
						dataSource.setUrl(tempjdbcService.getDUrl());
						dataSource.setUsername(tempjdbcService.getDUser());
						dataSource.setPassword(tempjdbcService.getDPsd());
						dataSource.setMaxWait(30000);
						//dataSource.setRemoveAbandoned(true);//超时未使用的链接自动释放
						//dataSource.setRemoveAbandonedTimeout(300);//超过180s自动断开
						dataSource.setMaxActive(50);
						dataSource.setInitialSize(5);//初始化5个链接  如果链接信息不对，会抛出异常
						dataSource.setMaxPoolPreparedStatementPerConnectionSize(Integer.parseInt(tempjdbcService.getMaxCon()));
					} catch (Exception e) {
						logger.error("创建接连池：‘" + service + "’出错，请检查");
						logger.error(e.getMessage());
						e.printStackTrace();
					}
			}
			if(dataSource != null) {
				 tempDataSourceMap.put(service, dataSource);
			}
		}
		return dataSource;
	}
	
	/**
	 * 返回指定服务service的连接
	 * @param service    数据服务标志
	 * @return 
	 */
	public static Connection getConnection(String projectId, String service) {
		int maxRetries = 3;  // 最大重试次数
		int retryCount = 0;
		long retryIntervalMillis = 10000;  // 重试间隔时间，单位毫秒
		while (retryCount < maxRetries) {
			try {
				DruidDataSource cm = getConnectionManager(projectId, service);
				if (cm == null) return null;
				//第一使用连不上会走外面的cache
				Connection conn = cm.getConnection();
				if(conn.isValid(1)){
					return conn;
				}else{
					retryCount++;
					TimeUnit.MILLISECONDS.sleep(retryIntervalMillis);
				}

			} catch (Exception e) {
				System.err.println("数据连接：‘" + service + "’获取出错，正在进行重试...");
				logger.error(e.getMessage());
				e.printStackTrace();
				retryCount++;
				// 等待一段时间后重试
				try {
					TimeUnit.MILLISECONDS.sleep(retryIntervalMillis);
				} catch (InterruptedException ex) {
					Thread.currentThread().interrupt();
				}
			}
		}

		System.err.println("数据连接：‘" + service + "’获取出错，重试次数超过最大限制");
		return null;
	}
}
