package com.wcp.db.jdbc;

import com.wcp.db.data.SQLExcuteException;
import com.wcp.db.data.TableFieldProp;
import com.wcp.db.data.TableProp;
import com.wcp.db.querydao.*;
import com.wcp.service.AcommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * 数据库读取接口，
 * 此接口支持JDBC直接读取数据库或者利用服务的形式读取数据库
 * 此接口需要用到4个配置文件，分别说明如下：
 * config.properties:数据服务端程序绑定IP和端口配置，如果用JDBC直接连，不需要配置
 * jdbc.properties:数据库连接配置，支持多数据源连接
 * remote.properties:服务连接配置，可配置主服务器和备用配置器，service.type如果为remote表示用服务器进行连接，如果此文件不存在默认为JDBC连接
 * log4j.properties:日志文件配置
 * <AUTHOR>
 */
public  class WcpDataBase {



	/**
	 * 查询数据库类型
	 * @param service 数据库注册服务
	 * @return string
	 */
	public  static String getTypeOfDB(String service) {
		return WcpDaoFactory.getDaoFactory().getTypeOfDB(service);
	}

	/**
	 * 查询表属性
	 * @param service     服务连接标志
	 * @param types       表类型,支持("TABLE"、"VIEW"、"SYSTEM TABLE"、"GLOBAL TEMPORARY"、"LOCAL TEMPORARY"、"ALIAS" 、 "SYNONYM")
	 * @return
	 */
	public  static List<TableProp> queryTable(String service, String[] types) {
		return WcpDaoFactory.getDaoFactory().queryTable( service, types);
	}

	/**
	 * 查询表字段属性
	 * @param service     服务连接标志
	 * @param tableName   表名称
	 * @return
	 */
	public static  List<TableFieldProp> queryTableField(String service, String tableName) {
		return WcpDaoFactory.getDaoFactory().queryTableField( service, tableName);
	}
	/**
	 * 查询是否存在某表
	 * @param service
	 * @param tableNamePattern
	 * @param types
	 * @return
	 */
	public static  boolean existTable(String service, String tableNamePattern, String types[]) {
		return WcpDaoFactory.getDaoFactory().existTable( service, tableNamePattern, types);
	}
	/**
	 * 执行更新、删除或插入操作
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public static  boolean execute(String service, String sql, Object[] param) {
		return WcpDaoFactory.getDaoFactory().execute( service, sql, param);
	}

	/**
	 * 执行新建表操作
	 * @param service
	 * @param sql
	 * @param param
	 * @return
	 */
	public static  boolean executeDDL(String service, String sql, Object[] param) {
		return WcpDaoFactory.getDaoFactory().executeDDL( service, sql, param);
	}
	/**
	 * 批量执行更新或插入操作
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public static  boolean execute(String service, String sql, Object[][] param) {
		return WcpDaoFactory.getDaoFactory().execute( service, sql, param);
	}
	/**
	 * 批量执行更新或插入操作
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public static  boolean execute(String service, String sql, List<Object[]> param) {
		Object[][] tempList = new Object[param.size()][];
		for (int i = 0; i < tempList.length; i++) {
			tempList[i] = param.get(i);
		}
		return WcpDaoFactory.getDaoFactory().execute( service, sql, tempList);
	}
	/**
	 * 执行插入操作
	 * @param <T>        插入类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public static  <T> boolean insert( String service, String sql, List<T> param) {
		return WcpDaoFactory.getDaoFactory().insert( service, sql, param, null);
	}

	/**
	 * 执行插入操作
	 * @param <T>        插入类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public static  <T> boolean insert(String service, String sql, T param) {
		List<T> paramList = new ArrayList<T>();
		paramList.add(param);
		return WcpDaoFactory.getDaoFactory().insert( service, sql, paramList, null);
	}
	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param clazz      返回类型字节码
	 * @return
	 */
	public static  <T> List<T> query(String service, String sql, Object[] param, Class<T> clazz) {
		return WcpDaoFactory.getDaoFactory().query( service, sql, param, null, clazz, null);
	}
	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param symbols    附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param clazz      返回类型字节码
	 * @return
	 */
	public static  <T> List<T> query(String service, String sql, Object[] param, Object[] symbols, Class<T> clazz) {
		return WcpDaoFactory.getDaoFactory().query( service, sql, param, symbols, clazz, null);
	}
	/**
	 * 执行插入操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public static  <T> boolean insert(String service, String sql, T param, String aliasStr) {
		List<T> paramList = new ArrayList<T>();
		paramList.add(param);
		return WcpDaoFactory.getDaoFactory().insert( service, sql, paramList, aliasStr);
	}
	/**
	 * 执行插入操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public static  <T> boolean insert(String service, String sql, List<T> param, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().insert( service, sql, param, aliasStr);
	}
	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param clazz      返回类型字节码
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public static  <T> List<T> query(String service, String sql, Object[] param, Class<T> clazz, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().query( service, sql, param, null, clazz, aliasStr);
	}

	@Autowired
	private WcpConnection wcpConnection;

	@Autowired
	private AcommonService acommonService;

	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @return
	 */
	public static  List<Map<String, Object>> queryMap(String service,QueryWrapper wrapper){
		return WcpDaoFactory.getDaoFactory().queryMap(service,wrapper);
	}


	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param symbols    附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param clazz      返回类型字节码
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public static  <T> List<T> query(String service, String sql, Object[] param, Object[] symbols, Class<T> clazz, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().query( service, sql, param, symbols, clazz, aliasStr);
	}
	/**
	 * 执行插入操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public static  boolean insertMap(String service, String sql, List<Map<String, Object>> param, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().insertMap( service, sql, param, aliasStr);
	}
	/**
	 * 执行插入操作
	 * @param service     数据源名称
	 * @param param       执行参数
	 * @return
	 */
	public static  boolean insertListMap(String service, String tableName, List<Map<String, Object>> param) {
		return WcpDaoFactory.getDaoFactory().insertListMap( service, tableName, param);
	}
	/**
	 * 执行插入操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public static  boolean insertMap(String service, String sql, Map<String, Object> param, String aliasStr) {
		List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
		list.add(param);
		return WcpDaoFactory.getDaoFactory().insertMap( service, sql, list, aliasStr);
	}
	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public static  List<Map<String, Object>> queryMap(String service, String sql, Object[] param, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().queryMap( service, sql, param, null, aliasStr);
	}
	/**
	 * 执行分页查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public static  Map<String, Object> queryMapByPaging(String service, String sql, Object[] param, String aliasStr,
														Integer currentPage,Integer pageSize, String orderBy) {
		String typeOfDB = getTypeOfDB(service);
		if (typeOfDB.equals("mysql") || typeOfDB.equals("dm") || typeOfDB.equals("oracle")){
			return WcpDaoFactory.getDaoFactory().queryMapByPaging( service, sql, param, null, aliasStr,currentPage,pageSize,null);
		}else {
			return WcpDaoFactory.getDaoFactory().queryMapByPaging( service, sql, param, null, aliasStr,currentPage,pageSize,orderBy);
		}
	}
	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public static  Map<String, Object> querySingleMap(String service, String sql, Object[] param, String aliasStr) {
		List<Map<String, Object>> tempList = WcpDaoFactory.getDaoFactory().queryMap( service, sql, param, null, aliasStr);
		if(tempList.size() == 0) {
			return null;
		}
		return tempList.get(0);
	}
	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param symbols     附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public static  List<Map<String, Object>> queryMap(String service, String sql, Object[] param, Object[] symbols, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().queryMap( service, sql, param, symbols, aliasStr);
	}

	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param symbols     附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public static  List<Map<String, Object>> queryLocalDateMap(String service, String sql, Object[] param, Object[] symbols, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().queryLocalDateMap( service, sql, param, symbols, aliasStr);
	}

	public static  List<Map<String,Object>> queryStatement(String service,String sql,Object[] param, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().queryStatement( service, sql, param, aliasStr);
	}

	public static  void executeStatement(String service,String sql,Object[] param) {
		WcpDaoFactory.getDaoFactory().executeStatement( service, sql, param);
	}


	public static  <T> boolean insertOrUpdate(String service, String sql, List<T> param, Object[] symbols, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().insertOrUpdate( service, sql, param, symbols, aliasStr);
	}

	public static  <T> boolean insertOrUpdate(String service, String sql, List<T> param, String aliasStr) {
		return WcpDaoFactory.getDaoFactory().insertOrUpdate( service, sql, param, aliasStr);
	}

	public static  <T> boolean insertOrUpdate(String service, String sql, T param, String aliasStr) {
		List<T> tempList = new ArrayList<T>();
		tempList.add(param);
		return WcpDaoFactory.getDaoFactory().insertOrUpdate( service, sql, tempList, aliasStr);
	}


	/**
	 * 返回匹配条件中的所有字段的结果
	 * @param service
	 * @param tableName      数据库表名,支持多表和单表，单表：HOURDB A或HOURDB；多表：HOURDB A,IDELEMENT B,TABLE C
	 * @param tableField     查询的字段，如果为空，则查询所有字段，A.SENID,A.TIME,B.DESC
	 * @param joinType       多表时级联类型，JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL JOIN.分别为内联连，左连接，右连接，全连接.默认为内连接，单表为空
	 * @param fieldMapping   查询的字段，多表：A.SENID,B.SENID;A.TIME,B.TIME，单表为空
	 * @return
	 */
	public static  <T> List<Map<String, Object>> queryListMapBySql(String service, String tableName,
																   String tableField, String joinType, String fieldMapping, String conditionSql, Object[] param) {
		return WcpDaoFactory.getDaoFactory().queryListMapBySql( service, tableName,
				tableField, joinType, fieldMapping, conditionSql, param);
	}

	/**
	 * 返回匹配条件中的所有字段的结果
	 * @param service
	 * @return
	 */
	public static  <T> List<Map<String, Object>> queryDataListByRule(String service, List<String> tableList,
																	 List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins, List<ConditionDao> conditions, List<GroupDao> groups,
																	 LimitDao limits, List<OrderDao> orders) throws SQLExcuteException {
		return WcpDaoFactory.getDaoFactory().queryDataListByRule( service, tableList, tableFields, tableJoins, conditions, groups, limits, orders);
	}

	public static  <T> boolean updateListsMap(String service, String tableName,List<TableFieldDao> tableFields, List<Map<String, Object>> param, List<ConditionDao> conditions) throws RemoteException {
		return WcpDaoFactory.getDaoFactory().updateListsMap( service, tableName,tableFields, param, conditions);
	}

	public static  <T> boolean deleteListsMap(String service, String tableName,List<TableFieldDao> tableFields,
											  List<ConditionDao> conditions)  throws RemoteException{
		return WcpDaoFactory.getDaoFactory().deleteListsMap( service, tableName,tableFields, conditions);
	}

	public static  <T> boolean insertListsMap(String projectId, String service, String tableName,List<TableFieldDao> tableFields,
											  List<Map<String, Object>> param) throws RemoteException{
		return WcpDaoFactory.getDaoFactory().insertListsMap(service, tableName,tableFields, param);
	}

	/**
	 * 返回匹配条件中的所有字段的结果
	 * @param projectId
	 * @param service
	 * @return
	 */
	public static  <T> int queryDataListByRuleCount(String projectId, String service, List<String> tableList,
													List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins, List<ConditionDao> conditions, List<GroupDao> groups,
													LimitDao limits, List<OrderDao> orders) throws SQLExcuteException {
		return WcpDaoFactory.getDaoFactory().queryDataListByRuleCount(service, tableList, tableFields, tableJoins, conditions, groups, limits, orders);
	}
	public static  Map<String, Object> queryDataBaseFromDB(String projectId, String service, boolean includePwd) {
		String querySql = "SELECT PROJECT,SERVICE,DB_TYPE,DB_NAME,ALIAS,DRIVER,URL,USER_NAME,USER_PWD,MAX_CLIENTS,VERSION,DB_CHARACT,TO_CHARACT,TRANS_LEVEL,CHECK_SQL,DESCR FROM WCP_SP_DB WHERE PROJECT=? AND SERVICE=?";
		String dbAlias = "projectId-PROJECT;service-SERVICE;dataBaseType-DB_TYPE;name-DB_NAME;alias-ALIAS;driver-DRIVER;url-URL;username-USER_NAME;password-USER_PWD;maxclients-MAX_CLIENTS;version-VERSION;dbcharact-DB_CHARACT;tocharact-TO_CHARACT;translevel-TRANS_LEVEL;checksql-CHECK_SQL;desc-DESCR";
		Map<String, Object> dataBaseSource =querySingleMap("wcp", querySql, new Object[] {projectId, service}, dbAlias);
		if(dataBaseSource == null) {
			return null;
		}
		if(includePwd) {
			//String secret = (String)dataBaseSource.get("secret");
			String password = (String)dataBaseSource.get("password");
			String username = (String)dataBaseSource.get("username");
//			secret = SecurityUtil.decode(secretSaltKey, secret);
			//dataBaseSource.remove("secret");
			// 给用户名解密
//			username = SecurityUtil.decode(secretKey, secret, username);
			dataBaseSource.put("username", username);
			// 给密码解密
//			password = SecurityUtil.decode(secretKey, secret, password);
			dataBaseSource.put("password", password);
		} else {
			//String secret = (String)dataBaseSource.get("secret");
			String username = (String)dataBaseSource.get("username");
//			secret = SecurityUtil.decode(secretSaltKey, secret);
			// 给用户名解密
//			username = SecurityUtil.decode(secretKey, secret, username);
			dataBaseSource.put("username", username);
			dataBaseSource.remove("secret");
			dataBaseSource.remove("password");
		}
		return dataBaseSource;
	}

}
