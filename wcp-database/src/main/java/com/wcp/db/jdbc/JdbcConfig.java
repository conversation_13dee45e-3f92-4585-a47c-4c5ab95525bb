package com.wcp.db.jdbc;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Description: 加载yml文件jdbc数据库配置
 * Author: qianchao
 * Date: 2024/1/4 21:44
 */
@Data
@Component
public class JdbcConfig {

    /* 连接服务标志 **/
    private String service="wcp";
    /* 数据连接URL **/
    @Value("${spring.datasource.url}")
    private String durl;
    /* 数据连接用户名 **/
    @Value("${spring.datasource.username}")
    private String duser;
    /* 数据连接密码 **/
    @Value("${spring.datasource.password}")
    private String dpsd;
    /* 数据连接驱动器 **/
    @Value("${spring.datasource.driver-class-name}")
    private String driver;
    /* 连接池最大连接数 **/
    private String maxCon="50";
    /* 连接池最小连接数 **/
    private String minCon="2";
    /* 所属项目 **/
    private String project = "default";

    public  WcpJdbcService initJdbcService(){
        WcpJdbcService pJdbcService=new WcpJdbcService();
        pJdbcService.setService(service);
        pJdbcService.setDriver(driver);
        pJdbcService.setDPsd(dpsd);
        pJdbcService.setDUrl(durl);
        pJdbcService.setDUser(duser);
        return pJdbcService;
    }
}
