package com.wcp.db.jdbc;


import com.wcp.db.data.SQLExcuteException;
import com.wcp.db.data.TableFieldProp;
import com.wcp.db.data.TableProp;
import com.wcp.db.querydao.*;
import com.wcp.db.service.PServiceRemote;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.rmi.Naming;
import java.rmi.RemoteException;
import java.util.*;

/**
 * 后端读取接口工厂，在此类中进行服务读取还是JDBC直接读取
 * <AUTHOR>
 */
public  class WcpDaoFactory
{
	private static Logger logger = Logger.getLogger(WcpDaoFactory.class);

	private static WcpDaoFactory factory = null;

	static {
		factory = new WcpDaoFactory();
		Properties properties = null;
		if(properties != null && properties.get("service.type") != null
				&& properties.get("service.type").equals("remote")) {
			factory.isRemote = true;
			factory.remoteUrl = properties.get("service.url").toString();
			factory.remoteBak = properties.get("service.bak").toString();
			try {
				factory.dataService = (PServiceRemote) Naming.lookup(factory.remoteUrl);
			} catch (Exception e) {
				logger.error("服务：" + factory.remoteUrl + "，连接失败！尝试连接备用服务");
				System.out.println("服务：" + factory.remoteUrl + "，连接失败！尝试连接备用服务");
				try {
					factory.dataService = (PServiceRemote)Naming.lookup(factory.remoteBak);
				} catch (Exception e2) {
					logger.error("备用服务：" + factory.remoteBak + "，连接失败！请检查服务是否启动或网络是否连通。");
					System.out.println("备用服务：" + factory.remoteBak + "，连接失败！请检查服务是否启动或网络是否连通。");
					e2.printStackTrace();
				}
			}
		} else {
			factory.isRemote = false;
		}
	}

	private boolean isRemote = false;
	private PServiceRemote dataService = null;
	private String remoteUrl = "";
	private String remoteBak = "";

	private WcpDaoFactory(){}

	/**
	 * 近回后端读取接口工厂
	 * @return
	 */
	public  static WcpDaoFactory getDaoFactory()
	{
		return factory;
	}

	/**
	 * 查询数据库类型
	 * @param service 数据库注册服务
	 * @return string
	 */
	public static  String getTypeOfDB(String service) {
		return WcpDao.getTypeOfDB(service);
	}

	/**
	 * 查询表属性
	 * @param service     服务连接标志
	 * @param types       表类型
	 * @return
	 */
	public  List<TableProp> queryTable(String service, String[] types) {
		if(isRemote) {
			try {
				return dataService.queryTable(null, service, types);
			} catch (RemoteException e) {
				return null;
			}
		} else {
			return WcpDao.queryTable(service, types);
		}
	}
	/**
	 * 查询表字段属性
	 * @param service     服务连接标志
	 * @param tableName   表名称
	 * @return
	 */
	public  List<TableFieldProp> queryTableField(String service, String tableName) {
		if(isRemote) {
			try {
				return dataService.queryTableField(null, service, tableName);
			} catch (RemoteException e) {
				return null;
			}
		} else {
			return WcpDao.queryTableField(service, tableName);
		}
	}

	public  boolean existTable(String service,	String tableNamePattern, String types[]) {
		if(isRemote) {
			try {
				return dataService.existTable(null, service, tableNamePattern, types);
			} catch (RemoteException e) {
				return false;
			}
		} else {
			return WcpDao.existTable(service, tableNamePattern, types);
		}
	}
	/**
	 * 执行更新、删除或插入操作
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public  boolean execute(String service, String sql, Object[] param) {
		if(isRemote) {
			try {
				return dataService.execute(null, service, sql, param);
			} catch (RemoteException e) {
				return false;
			}
		} else {
			return WcpDao.execute(service, sql, param);
		}
	}

	public  boolean executeDDL(String service, String sql, Object[] param) {
		if(isRemote) {
			try {
				return dataService.executeDDL(null, service, sql, param);
			} catch (RemoteException e) {
				return false;
			}
		} else {
			return WcpDao.executeDDL(service, sql, param);
		}
	}
	/**
	 * 批量执行更新或插入操作
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public  boolean execute(String service, String sql, Object[][] param) {
		if(isRemote) {
			try {
				return dataService.execute(null, service, sql, param);
			} catch (RemoteException e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.execute(service, sql, param);
		}
	}
	/**
	 * 执行插入操作
	 * @param <T>        插入类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @return
	 */
	public  <T> boolean insert(String service, String sql, List<T> param) {
		if(isRemote) {
			try {
				return dataService.insert(null,service, sql, param, null);
			} catch (RemoteException e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.insert(service, sql, param, null);
		}

	}
	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param clazz      返回类型字节码
	 * @return
	 */
	public  <T> List<T> query(String service, String sql, Object[] param, Class<T> clazz) {
		if(isRemote) {
			try {
				return dataService.query(null, service, sql, param, null, clazz, null);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<T>();
			}
		} else {
			return WcpDao.query(service, sql, param, null, clazz, null);
		}
	}
	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param symbols    附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param clazz      返回类型字节码
	 * @return
	 */
	public  <T> List<T> query(String service, String sql, Object[] param, Object[] symbols, Class<T> clazz) {
		if(isRemote) {
			try {
				return dataService.query(null, service, sql, param, symbols, clazz, null);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<T>();
			}
		} else {
			return WcpDao.query(service, sql, param, symbols, clazz, null);
		}
	}
	/**
	 * 执行插入操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public  <T> boolean insert(String service, String sql, List<T> param, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.insert(null, service, sql, param, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.insert(service, sql, param, aliasStr);
		}
	}
	/**
	 * 执行插入操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public  <T> boolean insertOrUpdate(String service, String sql, List<T> param, Object[] symbols, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.insertOrUpdate(null, service, sql, param, symbols, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.insertOrUpdate( service, sql, param, symbols, aliasStr);
		}
	}
	/**
	 * 执行插入操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public  <T> boolean insertOrUpdate(String service, String sql, List<T> param, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.insertOrUpdate(null, service, sql, param, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.insertOrUpdate(service, sql, param, aliasStr);
		}
	}
	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param clazz      返回类型字节码
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public  <T> List<T> query(String service, String sql, Object[] param, Class<T> clazz, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.query(null, service, sql, param, null, clazz, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<T>();
			}
		} else {
			return WcpDao.query(service, sql, param, null, clazz, aliasStr);
		}
	}
	/**
	 * 执行查询操作
	 * @param <T>        查询类类型
	 * @param service    数据源名称
	 * @param sql        SQL语句
	 * @param param      执行参数
	 * @param symbols    附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param clazz      返回类型字节码
	 * @param aliasStr   属性与数据库字段的对应字符串
	 * @return
	 */
	public  <T> List<T> query(String service, String sql, Object[] param, Object[] symbols, Class<T> clazz, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.query(null, service, sql, param, symbols, clazz, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<T>();
			}
		} else {
			return WcpDao.query(service, sql, param, symbols, clazz, aliasStr);
		}
	}
	/**
	 * 执行插入操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public  boolean insertMap(String service, String sql, List<Map<String, Object>> param, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.insertMap(null, service, sql, param, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.insertMap(service, sql, param, aliasStr);
		}
	}
	/**
	 * 执行插入操作
	 * @param service         SQL语句
	 * @param param       执行参数
	 * @param tableName    属性与数据库字段的对应字符串
	 * @return
	 */
	public  boolean insertListMap(String service, String tableName, List<Map<String, Object>> param) {
		if(isRemote) {
			try {
				return dataService.insertListMap(null, service, tableName, param);
			} catch (RemoteException e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.insertListMap(service, tableName, param);
		}
	}
	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public  List<Map<String, Object>> queryMap(String service, String sql, Object[] param, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.queryMap(null, service, sql, param, null, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<Map<String,Object>>();
			}
		} else {
			return WcpDao.queryMap(service, sql, param, null, aliasStr);
		}
	}
	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param symbols     附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public  List<Map<String, Object>> queryMap(String service, String sql, Object[] param, Object[] symbols, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.queryMap(null, service, sql, param, symbols, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<Map<String,Object>>();
			}
		} else {
			return WcpDao.queryMap( service, sql, param, symbols, aliasStr);
		}
	}
	/**
	 * 执行Wrapper查询操作
	 * @param service     数据源名称
	 * @return
	 */
	public  List<Map<String, Object>> queryMap(String service, QueryWrapper queryWrapper) {
		return WcpDao.queryMap( service,queryWrapper);
	}
	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param symbols     附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public  Map<String, Object> queryMapByPaging(
			String service, String sql, Object[] param, Object[] symbols, String aliasStr,
			Integer currentPage,Integer pageSize, String orderBy) {
		if(isRemote) {
			try {
				return dataService.queryMapByPaging(null, service, sql, param, symbols, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new HashMap<>();
			}
		} else {
			return WcpDao.queryMapByPaging( service, sql, param, symbols, aliasStr,currentPage,pageSize,orderBy);
		}
	}

	/**
	 * 执行查询操作
	 * @param service     数据源名称
	 * @param sql         SQL语句
	 * @param param       执行参数
	 * @param symbols     附加SQL语句参数，如果SQL语句需要参数，请以#?#作为点位符
	 * @param aliasStr    属性与数据库字段的对应字符串
	 * @return
	 */
	public  List<Map<String, Object>> queryLocalDateMap(String service, String sql, Object[] param, Object[] symbols, String aliasStr) {
		if(isRemote) {
			try {
				return dataService.queryLocalDateMap(null, service, sql, param, symbols, aliasStr);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<Map<String,Object>>();
			}
		} else {
			return WcpDao.queryLocalDateMap(service, sql, param, symbols, aliasStr);
		}
	}

	public  List<Map<String,Object>> queryStatement(String service,String sql,Object[] param, String aliasStr) {
		return WcpDao.getStatement(service, sql, param,  aliasStr);
	}

	public  void executeStatement(String service,String sql,Object[] param) {
		WcpDao.executeStatement(service, sql, param);
	}

	public  <T> List<Map<String, Object>> queryListMapBySql(String service, String tableName,
																  String tableField, String joinSql, String fieldMapping, String conditionSql, Object[] param) {
		if(isRemote) {
			try {
				return dataService.queryListMapBySql(null, service, tableName, tableField, joinSql, conditionSql, param);
			} catch (RemoteException e) {
				e.printStackTrace();
				return new ArrayList<Map<String,Object>>();
			}
		} else {
			return WcpDao.queryListMapBySql(service, tableName, tableField, joinSql, conditionSql, param);
		}
	}

	/**
	 * 返回匹配条件中的所有字段的结果
	 * @param service
	 * @param tableList      数据库表名,支持多表和单表，单表：HOURDB A或HOURDB；多表：HOURDB A,IDELEMENT B,TABLE C
	 * @param tableFields    查询的字段，如果为空，则查询所有字段，A.SENID,A.TIME,B.DESC
	 * @param tableJoins     多表时级联类型，JOIN(INNER JOIN),LEFT JOIN,RIGHT JOIN,FULL JOIN.分别为内联连，左连接，右连接，全连接.默认为内连接，单表为空
	 * @param conditions      查询条件，全部同时满足
	 * @param groups         分组条件，全部同时满足
	 * @param limits         分页条件，全部同时满足
	 * @param orders         排序，多表：A.SENID ASC,B.TIME DESC
	 * @return
	 */
	public   <T> List<Map<String, Object>> queryDataListByRule(String service, List<String> tableList,
																	 List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins, List<ConditionDao> conditions, List<GroupDao> groups,
																	 LimitDao limits, List<OrderDao> orders) throws SQLExcuteException {
		if(isRemote) {
			try {
				return dataService.queryDataListByRule(null, service, tableList, tableFields, tableJoins, conditions, groups, limits, orders);
			} catch (SQLExcuteException e) {
				e.printStackTrace();
				return new ArrayList<Map<String,Object>>();
			}
		} else {
			return WcpDao.queryDataListByRule(service, tableList, tableFields, tableJoins, conditions, groups, limits, orders);
		}
	}

	public   <T> boolean updateListsMap(String service, String tableName,List<TableFieldDao> tableFields, List<Map<String, Object>> param, List<ConditionDao> conditions) throws RemoteException {
		if(isRemote) {
			try {
				return dataService.updateListsMap(null, service, tableName,tableFields, param, conditions);
			} catch (Exception e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.updateListsMap(service, tableName,tableFields, param, conditions);
		}
	}

	public   <T> boolean insertListsMap(String service, String tableName,List<TableFieldDao> tableFields,
											  List<Map<String, Object>> param) throws RemoteException{
		if(isRemote) {
			try {
				return dataService.insertListsMap(null, service, tableName,tableFields, param);
			} catch (Exception e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.insertListsMap(service, tableName,tableFields, param);
		}
	}

	public   <T> boolean deleteListsMap(String service, String tableName,List<TableFieldDao> tableFields,
											  List<ConditionDao> conditions)  throws RemoteException{
		if(isRemote) {
			try {
				return dataService.deleteListsMap(null, service, tableName,tableFields, conditions);
			} catch (Exception e) {
				e.printStackTrace();
				return false;
			}
		} else {
			return WcpDao.deleteListsMap(service, tableName,tableFields, conditions);
		}
	}

	public  int queryDataListByRuleCount(String service, List<String> tableList,
											   List<TableFieldDao> tableFields, List<TableJoinDao> tableJoins, List<ConditionDao> conditions,
											   List<GroupDao> groups, LimitDao limits, List<OrderDao> orders) {
		if(isRemote) {
			try {
				return dataService.queryDataListByRuleCount(null, service, tableList, tableFields, tableJoins, conditions, groups, limits, orders);
			} catch (Exception e) {
				e.printStackTrace();
				return 0;
			}
		} else {
			return WcpDao.queryDataListByRuleCount( service, tableList, tableFields, tableJoins, conditions, groups, limits, orders);
		}
	}
}
