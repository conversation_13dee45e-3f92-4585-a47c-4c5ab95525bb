package com.wcp.db.enums;

import io.dataease.plugins.common.constants.DatabaseClassification;
import io.dataease.plugins.common.constants.DatasourceCalculationMode;

import java.util.List;

/**
 * Description: 定义基础数据库类型
 * Author: qianchao
 * Date: 2024/3/20 09:52
 */
public enum DatasourceTypes {
    mysql("mysql","mysql"),dm("dm","dm"), TiDB("TiDB","TiDB"), hive("hive","hive"),
    impala("impala","impala"), mariadb("mariadb","mariadb"), StarRocks("StarRocks","StarRocks"),
    ds_doris("ds_doris","ds_doris"), pg("pg","pg"), sqlServer("sqlServer","sqlServer"),
    oracle("oracle","oracle"), mongo("mongo","mongo"), ck("ck","ck"), db2("db2","db2"),
    redshift("redshift","redshift"), es("es","es"), api("api","api"), excel("excel","excel");
    private String type;
    private String name;
    private String keywordPrefix;
    private String keywordSuffix;
    private String aliasPrefix;
    private String aliasSuffix;
    private String extraParams;
    private boolean isDatasource;
    private boolean isJdbc;
    private DatasourceCalculationMode calculationMode;
    private DatabaseClassification databaseClassification;
    private List<String> charset;
    private List<String> targetCharset;
     DatasourceTypes(String type, String name) {
        this.type = type;
        this.name = name;
    }
    // 获取type字段的方法
    public String getType() {
        return type;
    }

    // 获取name字段的方法
    public String getName() {
        return name;
    }

}
