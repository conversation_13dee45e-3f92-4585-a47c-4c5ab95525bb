package com.wcp.db.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/4/16 16:24
 */

public class JdbcUrlUtils {
    public static Map<String, String> parseJdbcUrl(String jdbcUrl) {
        Map<String, String> result = new HashMap<>();

        if (jdbcUrl.startsWith("jdbc:mysql://")) {
            parseMysql(jdbcUrl, result);
        } else if (jdbcUrl.startsWith("jdbc:oracle:thin:")) {
            parseOracle(jdbcUrl, result);
        } else if (jdbcUrl.startsWith("jdbc:dm://")) {
            parseDm(jdbcUrl, result);
        } else {
            result.put("error", "不支持的数据库类型");
        }

        return result;
    }

    private static void parseMysql(String url, Map<String, String> result) {
        try {
            String main = url.substring("jdbc:mysql://".length());
            String[] parts = main.split("\\?", 2);
            String[] hostPortDb = parts[0].split("/", 2);
            String[] hostPort = hostPortDb[0].split(":");

            result.put("host", hostPort[0]);
            result.put("port", hostPort.length > 1 ? hostPort[1] : "3306");
            result.put("database", hostPortDb.length > 1 ? hostPortDb[1] : "");

            if (parts.length > 1) {
                String[] params = parts[1].split("&");
                for (String param : params) {
                    String[] kv = param.split("=");
                    if (kv.length == 2) {
                        result.put(kv[0], kv[1]);
                    }
                }
            }
        } catch (Exception e) {
            result.put("error", "MySQL 解析失败: " + e.getMessage());
        }
    }

    private static void parseOracle(String url, Map<String, String> result) {
        try {
            String main = url.substring("jdbc:oracle:thin:".length());
            String[] parts = main.split("@");
            String[] userPass = parts[0].split("/");
            result.put("user", userPass[0]);
            result.put("password", userPass.length > 1 ? userPass[1] : "");

            String[] hostPortSid = parts[1].split(":");
            result.put("ip", hostPortSid[0]);
            result.put("port", hostPortSid[1]);
            result.put("sid", hostPortSid[2]);
        } catch (Exception e) {
            result.put("error", "Oracle 解析失败: " + e.getMessage());
        }
    }

    private static void parseDm(String url, Map<String, String> result) {
        try {
            String main = url.substring("jdbc:dm://".length());
            String[] parts = main.split("\\?", 2);
            String[] hostPort = parts[0].split(":");

            result.put("host", hostPort[0]);
            result.put("port", hostPort.length > 1 ? hostPort[1] : "5236");

            if (parts.length > 1) {
                String[] params = parts[1].split("&");
                for (String param : params) {
                    String[] kv = param.split("=");
                    if (kv.length == 2) {
                        result.put(kv[0], kv[1]);
                    }
                }
            }
        } catch (Exception e) {
            result.put("error", "达梦解析失败: " + e.getMessage());
        }
    }
}
