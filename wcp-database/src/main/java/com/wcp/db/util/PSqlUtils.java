package com.wcp.db.util;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.data.DataTypeUtil;
import com.wcp.db.data.DBConst;
import com.wcp.db.data.OperateTableData;
import com.wcp.db.data.TableFieldProp;
import com.wcp.utils.ArrayUtil;
import com.wcp.utils.DateUtil;
import com.wcp.utils.StringUtil;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Date;
import java.sql.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
/**
 * Sql处理工具集合
 * <AUTHOR>
 */
@SuppressWarnings("deprecation")
public class PSqlUtils
{
	/**
	 * 返回重新组合后的SQL语句
	 * 当参数有数组时,需要在读取语句中把单?问号变成?,?,?...
	 * @param sql      SQL数据
	 * @param param    SQL参与
	 * @return
	 */
	public static String composeSql(String sql, Object[] param)
	{
		if(param == null || param.length == 0) return sql;
		StringBuffer sqlBuffer = new StringBuffer(sql);
		int charaIndex = -1;
		for(int i = 0; i < param.length; i++) {
			Object tempParam = param[i];
			charaIndex = sqlBuffer.indexOf("?", charaIndex + 1);
			if(charaIndex == -1) break;
			if(tempParam != null && (tempParam instanceof Collection || tempParam instanceof Set)) {
				tempParam = DataTypeUtil.transformDataBySimpleName("Object[]", tempParam);
			}
			if(tempParam != null && tempParam.getClass().isArray()) {
				int length = Array.getLength(tempParam);
				String tempStr = "";
				for(int j = 0; j < length; j++) {
					tempStr += (j == 0 ? "?":",?");
				}
				sqlBuffer.replace(charaIndex, charaIndex + 1, tempStr);
				charaIndex += tempStr.length();
			}
		}
		return sqlBuffer.toString();
	}

	/**
	 * 把param中的参数添加到pst中去,如果Error,返回-1,否则返回下一次处理开始的参数位置
	 * @param index 批处理参添加开始位置
	 * @param pst
	 * @param param
	 * @throws SQLException
	 * @returns
	 */
	public static int paramBinding(int index, PreparedStatement pst, Object[] param) throws SQLException
	{
		if(param == null || param.length == 0) return index;
		for(int i = 0; i < param.length; i++) {
			if(param[i] == null) {
				pst.setObject(index, null);
			} else if(param[i] instanceof Object[]) {
				//下面这行在原来的基础修改了一下index = paramBinding(index, pst, (Object[])param[i])
				index = paramBinding(index, pst, (Object[])param[i]) - 1;
			} else if(param[i] instanceof Collection) {
				Object[] tempParam = (Object[])DataTypeUtil.transformDataBySimpleName("Object[]", param[i]);
				index = paramBinding(index, pst, tempParam) - 1;
			} else if(param[i] instanceof BigDecimal) {
				pst.setBigDecimal(index, (BigDecimal)param[i]);
			} else if(param[i] instanceof String) {
				pst.setString(index, (String)param[i]);
			} else if(param[i] instanceof java.util.Date) {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime((java.util.Date)param[i]);
				pst.setTimestamp(index, new Timestamp(calendar.getTimeInMillis()));
			} else if(param[i] instanceof Calendar) {
				pst.setTimestamp(index, new Timestamp(((Calendar)param[i]).getTimeInMillis()));
			} else if(param[i] instanceof Double) {
				pst.setDouble(index, (Double)param[i]);
			} else if(param[i] instanceof Float) {
				pst.setFloat(index, (Float)param[i]);
			} else if(param[i] instanceof Integer) {
				pst.setInt(index, (Integer)param[i]);
			} else if(param[i] instanceof Long) {
				pst.setLong(index, (Long)param[i]);
			} else if(param[i] instanceof Date) {
				pst.setDate(index, (Date)param[i]);
			} else if(param[i] instanceof LocalDateTime) {
				LocalDateTime localDateTime = (LocalDateTime)param[i];
				ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
				Date date = new Date(Date.from(zdt.toInstant()).getTime());
				pst.setDate(index, date);
			} else if(param[i] instanceof Boolean) {
				pst.setBoolean(index, (Boolean)param[i]);
			} else if(param[i] instanceof Byte) {
				pst.setByte(index, (Byte)param[i]);
			} else if(param[i] instanceof byte[]) {
				pst.setBytes(index, (byte[])param[i]);
			} else if(param[i] instanceof Blob) {
				pst.setBlob(index, (Blob)param[i]);
			} else if(param[i] instanceof Clob) {
				pst.setClob(index, (Clob)param[i]);
			} else if(param[i] instanceof NClob) {
				pst.setNClob(index, (NClob)param[i]);
			} else if(param[i] instanceof Short) {
				pst.setShort(index, (Short)param[i]);
			}/* else if(param[i] instanceof SQLXML) {
				pst.setSQLXML(index, (SQLXML)param[i]);
			} */else if(param[i] instanceof Time) {
				pst.setTime(index, (Time)param[i]);
			} else if(param[i] instanceof Timestamp) {
				pst.setTimestamp(index, (Timestamp)param[i]);
			} else if(param[i] instanceof URL) {
				pst.setURL(index, (URL)param[i]);
			} else {
				pst.setObject(index, param[i]);
			}
			index++;
		}
		return index;
	}

	public static int paramBinding(int index, CallableStatement pst, Object[] param) throws SQLException {
		if(param == null || param.length == 0) return index;
		for(int i = 0; i < param.length; i++) {
			if(param[i] == null) {
				pst.setObject(index, null);
			} else if(param[i] instanceof Object[]) {
				index = paramBinding(index, pst, (Object[])param[i]);
			} else if(param[i] instanceof BigDecimal) {
				pst.setBigDecimal(index, (BigDecimal)param[i]);
			} else if(param[i] instanceof String) {
				pst.setString(index, (String)param[i]);
			} else if(param[i] instanceof Calendar) {
				pst.setTimestamp(index, new Timestamp(((Calendar)param[i]).getTimeInMillis()));
			} else if(param[i] instanceof Double) {
				pst.setDouble(index, (Double)param[i]);
			} else if(param[i] instanceof Float) {
				pst.setFloat(index, (Float)param[i]);
			} else if(param[i] instanceof Integer) {
				pst.setInt(index, (Integer)param[i]);
			} else if(param[i] instanceof Long) {
				pst.setLong(index, (Long)param[i]);
			} else if(param[i] instanceof Date) {
				pst.setDate(index, (Date)param[i]);
			} else if(param[i] instanceof LocalDateTime) {
				LocalDateTime localDateTime = (LocalDateTime)param[i];
				ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
				Date date = new Date(Date.from(zdt.toInstant()).getTime());
				pst.setDate(index, date);
			} else if(param[i] instanceof Boolean) {
				pst.setBoolean(index, (Boolean)param[i]);
			} else if(param[i] instanceof Byte) {
				pst.setByte(index, (Byte)param[i]);
			} else if(param[i] instanceof byte[]) {
				pst.setBytes(index, (byte[])param[i]);
			} else if(param[i] instanceof Blob) {
				pst.setBlob(index, (Blob)param[i]);
			} else if(param[i] instanceof Clob) {
				pst.setClob(index, (Clob)param[i]);
			} else if(param[i] instanceof NClob) {
				pst.setNClob(index, (NClob)param[i]);
			} else if(param[i] instanceof Short) {
				pst.setShort(index, (Short)param[i]);
			}/* else if(param[i] instanceof SQLXML) {
				pst.setSQLXML(index, (SQLXML)param[i]);
			} */else if(param[i] instanceof Time) {
				pst.setTime(index, (Time)param[i]);
			} else if(param[i] instanceof Timestamp) {
				pst.setTimestamp(index, (Timestamp)param[i]);
			} else if(param[i] instanceof URL) {
				pst.setURL(index, (URL)param[i]);
			} else {
				pst.setObject(index, param[i]);
			}
			index++;
		}
		return index;
	}

	/**
	 * 把param中的参数添加到pst中去,如果Error,返回-1,否则返回下一次处理开始的参数位置
	 * @param index 批处理参添加开始位置
	 * @param pst
	 * @param param
	 * @throws SQLException
	 * @returns
	 */
	@SuppressWarnings("unchecked")
	public static int paramBinding(int index, PreparedStatement pst, Object param, String[] fieldNames, String aliasStr, String sql) throws Exception
	{
		Object[] validParam = new Object[fieldNames.length];
		if(aliasStr == null || aliasStr.trim().equals("")) {
			if(param instanceof Map) {
				Map<?, ?> tempMap = (Map<?, ?>)param;
				for (int i = 0; i < fieldNames.length; i++) {
					validParam[i] = tempMap.get(fieldNames[i]);
				}
			}
			else if(param instanceof Object[]){
				System.arraycopy(param, 0, validParam, 0, validParam.length);
			}
			else {
				Object[] tempParam = convertToArray(param);
				int validCount = Math.min(fieldNames.length, tempParam.length);
				for (int i = 0; i < validCount; i++) {
					validParam[i] = tempParam[i];
				}
			}
		} else {
			Map<String, String> filedMap = analysisAliasField(aliasStr);
			Map<String, Object> tempMap = null;
			if(param instanceof Map) {
				tempMap = (Map<String, Object>)param;
			} else {
				tempMap = convertToMap(param);
			}
			for (int i = 0; i < fieldNames.length; i++) {
				String proName = filedMap.get(fieldNames[i]);
				validParam[i] = tempMap.get(proName);
			}
		}
		//打印sql语句
		//printlnSQL(sql,validParam);
		return paramBinding(index, pst, validParam);
	}

	public static void printlnSQL(String sql, Object[] params) {
		if (params == null || params.length == 0) {
			System.out.println(sql);
			return;
		}
		StringBuilder sqlBuffer = new StringBuilder(sql);
		for (Object param : params) {
			int index = sqlBuffer.indexOf("?");
			if (index >= 0) {
				String paramStr = paramToString(param);
				if (StringUtil.isNotEmpty(paramStr)){
					sqlBuffer.replace(index, index + 1, paramStr);
				}

			} else {
				if (sql.toUpperCase().startsWith("INSERT")){
					System.out.println("暂不支持，批量插入sql语句打印");
				}else {
					break; // 如果没有找到占位符，停止循环
				}
			}
		}
		System.out.println("执行SQL语句 ===> " + sqlBuffer);
	}

	private static String paramToString(Object param) {
		if (param instanceof java.util.Date){
			return DateUtil.dateToString(((java.util.Date)param));
		}
		if (param instanceof Calendar){
			return DateUtil.calendarToString(((Calendar)param));
		}
		//TODO 未完待续....
		return param.toString();
	}


	/**
	 * 把param中的参数添加到pst中去,如果Error,返回-1,否则返回下一次处理开始的参数位置
	 * @param index 批处理参添加开始位置
	 * @param pst
	 * @param param
	 * @throws SQLException
	 * @returns
	 */
	@SuppressWarnings("unchecked")
	public static int paramBinding(int index, PreparedStatement pst, Object param, String[] paramFieldNames, String[] fieldNames, String aliasStr) throws Exception
	{
		Object[] validParam = new Object[fieldNames.length];
		if(aliasStr == null || aliasStr.trim().equals("")) {
			if(param instanceof Map) {
				Map<?, ?> tempMap = (Map<?, ?>)param;
				for (int i = 0; i < fieldNames.length; i++) {
					validParam[i] = tempMap.get(fieldNames[i]);
				}
			} else if(param instanceof Object[]){
				System.arraycopy(param, 0, validParam, 0, validParam.length);
			}else {
				Object[] tempParam = convertToArray(param);
				int validCount = Math.min(fieldNames.length, tempParam.length);
				for (int i = 0; i < validCount; i++) {
					String tableFieldName = fieldNames[i];
					int tempIdx = ArrayUtil.indexOf(paramFieldNames, tableFieldName);
					if(tempIdx < 0) {
						throw new RuntimeException("数据库字段无法进行有效的对应,SQL执行绑定失败.");
					}
					validParam[i] = tempParam[tempIdx];
				}
			}
		} else {
			Map<String, String> filedMap = analysisAliasField(aliasStr);
			Map<String, Object> tempMap = null;
			if(param instanceof Map) {
				tempMap = (Map<String, Object>)param;
			} else {
				tempMap = convertToMap(param);
			}
			for (int i = 0; i < fieldNames.length; i++) {
				String proName = filedMap.get(fieldNames[i]);
				validParam[i] = tempMap.get(proName);
			}
		}
		return paramBinding(index, pst, validParam);
	}
	/**
	 * 转换接果ResultSet到类中
	 * @param <T>
	 * @param clazz
	 * @param rs
	 * @param aliasStr
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> convertResult(Class<T> clazz, ResultSet rs, String aliasStr) throws Exception
	{
		List<T> datas = new ArrayList<T>();
		if (rs == null) return datas;
		Map<String, String> filedMap = null;
		boolean usedAlias = false;
		if(aliasStr != null && !aliasStr.trim().equals("")) {
			filedMap = analysisAliasProp(aliasStr);
			usedAlias = true;
		}

		while (rs.next()) {
			if(convertSingle(datas, rs, clazz)) {
				continue;
			}
			Object obj = clazz.newInstance();
			Field[] fields = clazz.getDeclaredFields();
			int col = 1;
			for (int i = 0; i < fields.length; i++) {
				if (col > rs.getMetaData().getColumnCount())
					break;
				Field field = fields[i];
				int mod = field.getModifiers();
				if (Modifier.isAbstract(mod) || Modifier.isFinal(mod) || Modifier.isNative(mod) || Modifier.isStatic(mod))
					continue;
				String fieldType = field.getType().getSimpleName();
				try {
					field.setAccessible(true);
				} catch (SecurityException e) {
					continue;
				}
				if(field == null || (usedAlias && (filedMap == null || filedMap.get(field.getName()) == null))) {
					continue;
				}
				if (fieldType.equals("String")) {
					String tempString = null;
					if(usedAlias) {
						tempString = rs.getString(filedMap.get(field.getName()));
					} else {
						tempString = rs.getString(col);
					}
					field.set(obj, tempString);
				} else if (fieldType.equals("byte")) {
					Byte tempByte = null;
					if(usedAlias) {
						tempByte = rs.getByte(filedMap.get(field.getName()));
					} else {
						tempByte = rs.getByte(col);
					}
					field.setByte(obj, tempByte==null?new Byte("0"):tempByte);
				} else if (fieldType.equals("Byte")) {
					Byte tempByte = null;
					if(usedAlias) {
						tempByte = rs.getByte(filedMap.get(field.getName()));
					} else {
						tempByte = rs.getByte(col);
					}
					field.set(obj, tempByte);
				} else if (fieldType.equals("short")) {
					Short tempShort = null;
					if(usedAlias) {
						tempShort = rs.getShort(filedMap.get(field.getName()));
					} else {
						tempShort = rs.getShort(col);
					}
					field.setShort(obj, tempShort==null?new Short("0"):tempShort);
				} else if (fieldType.equals("Short")) {
					Short tempShort = null;
					if(usedAlias) {
						tempShort = rs.getShort(filedMap.get(field.getName()));
					} else {
						tempShort = rs.getShort(col);
					}
					field.set(obj, tempShort);
				} else if (fieldType.equals("int")) {
					Integer tempInt = null;
					if(usedAlias) {
						tempInt = rs.getInt(filedMap.get(field.getName()));
					} else {
						tempInt = rs.getInt(col);
					}
					field.setInt(obj, tempInt==null?0:tempInt);
				} else if (fieldType.equals("Integer")) {
					Integer tempInt = null;
					if(usedAlias) {
						tempInt = rs.getInt(filedMap.get(field.getName()));
					} else {
						tempInt = rs.getInt(col);
					}
					field.set(obj, tempInt);
				} else if (fieldType.equals("long")) {
					Long tempLong = null;
					if(usedAlias) {
						tempLong = rs.getLong(filedMap.get(field.getName()));
					} else {
						tempLong = rs.getLong(col);
					}
					field.setLong(obj, tempLong==null?new Long("0"):tempLong);
				} else if (fieldType.equals("Long")) {
					Long tempLong = null;
					if(usedAlias) {
						tempLong = rs.getLong(filedMap.get(field.getName()));
					} else {
						tempLong = rs.getLong(col);
					}
					field.set(obj, tempLong);
				} else if (fieldType.equals("double")) {
					Double tempDouble = null;
					if(usedAlias) {
						tempDouble = rs.getDouble(filedMap.get(field.getName()));
					} else {
						tempDouble = rs.getDouble(col);
					}
					field.setDouble(obj, tempDouble==null?0.0:tempDouble);
				} else if (fieldType.equals("Double")) {
					Double tempDouble = null;
					if(usedAlias) {
						tempDouble = rs.getDouble(filedMap.get(field.getName()));
					} else {
						tempDouble = rs.getDouble(col);
					}
					if (rs.wasNull()) {
						tempDouble = null;
					}
					field.set(obj, tempDouble);
				} else if (fieldType.equals("float")) {
					Float tempFloat = null;
					if(usedAlias) {
						tempFloat = rs.getFloat(filedMap.get(field.getName()));
					} else {
						tempFloat = rs.getFloat(col);
					}
					field.setFloat(obj, tempFloat==null?0f:tempFloat);
				} else if (fieldType.equals("Float")) {
					Float tempFloat = null;
					if(usedAlias) {
						tempFloat = rs.getFloat(filedMap.get(field.getName()));
					} else {
						tempFloat = rs.getFloat(col);
					}
					field.set(obj, tempFloat);
				} else if (fieldType.indexOf("LocalDateTime") >= 0) {
					Timestamp timestamp = null;
					if(usedAlias) {
						timestamp = rs.getTimestamp(filedMap.get(field.getName()));
					} else {
						timestamp = rs.getTimestamp(col);
					}
					if (timestamp != null) {
						Instant instant = Instant.ofEpochMilli(timestamp.getTime());
						ZoneId zone = ZoneId.systemDefault();
						field.set(obj, LocalDateTime.ofInstant(instant, zone));
					}
				}else if (fieldType.indexOf("Date") >= 0) {
					Timestamp timestamp = null;
					if(usedAlias) {
						timestamp = rs.getTimestamp(filedMap.get(field.getName()));
					} else {
						timestamp = rs.getTimestamp(col);
					}
					if (timestamp != null) {
						field.set(obj, new Date(timestamp.getTime()));
					}
				} else if (fieldType.indexOf("Calendar") >= 0) {
					Calendar calendar = Calendar.getInstance();
					Timestamp timestamp = null;
					if(usedAlias) {
						timestamp = rs.getTimestamp(filedMap.get(field.getName()));
					} else {
						timestamp = rs.getTimestamp(col);
					}
					if (timestamp != null) {
						calendar.setTimeInMillis(timestamp.getTime());
						field.set(obj, calendar);
					}
				} else if (fieldType.indexOf("Timestamp") >= 0) {
					Timestamp timestamp = null;
					if(usedAlias) {
						timestamp = rs.getTimestamp(filedMap.get(field.getName()));
					} else {
						timestamp = rs.getTimestamp(col);
					}
					field.set(obj, timestamp);
				} else if (fieldType.equals("Boolean")) {
					Boolean tempBool = null;
					if(usedAlias) {
						tempBool = rs.getBoolean(filedMap.get(field.getName()));
					} else {
						tempBool = rs.getBoolean(col);
					}
					field.set(obj, tempBool);
				} else if (fieldType.equals("BigDecimal")) {
					BigDecimal tempBigDecimal = null;
					if(usedAlias) {
						tempBigDecimal = rs.getBigDecimal(filedMap.get(field.getName()));
					} else {
						tempBigDecimal = rs.getBigDecimal(col);
					}
					field.set(obj, tempBigDecimal);
				} else if (fieldType.equals("boolean")) {
					Boolean tempBool = null;
					if(usedAlias) {
						tempBool = rs.getBoolean(filedMap.get(field.getName()));
					} else {
						tempBool = rs.getBoolean(col);
					}
					field.setBoolean(obj, (tempBool == null ? false : tempBool));
				} else if (fieldType.equals("byte[]") || fieldType.equals("Byte[]")) {
					byte[] tempBytes = null;
					if(usedAlias) {
						tempBytes = rs.getBytes(filedMap.get(field.getName()));
					} else {
						tempBytes = rs.getBytes(col);
					}
					field.set(obj, tempBytes);
				} else {
					String tempString = null;
					if(usedAlias) {
						tempString = rs.getString(filedMap.get(field.getName()));
					} else {
						tempString = rs.getString(col);
					}
					field.set(obj, tempString);
				}
				col++;
			}
			datas.add((T)obj);
		}
		return datas;
	}

	@SuppressWarnings("unchecked")
	public static <T> boolean convertSingle(List<T> list, ResultSet rs, Class<?> clazz) throws SQLException
	{
		Object obj = null;
		if (clazz.equals(Byte.class)) {
			obj = rs.getByte(1);
		} else if (clazz.equals(Short.class)) {
			obj = rs.getShort(1);
		} else if (clazz.equals(Integer.class)) {
			obj = rs.getInt(1);
		} else if (clazz.equals(Long.class)) {
			obj = rs.getLong(1);
		} else if (clazz.equals(String.class)) {
			obj = rs.getString(1);
		} else if (clazz.equals(Double.class)) {
			obj = rs.getDouble(1);
		} else if (clazz.equals(Float.class)) {
			obj = rs.getFloat(1);
		} else if (clazz.equals(Date.class)) {
			Timestamp timestamp = rs.getTimestamp(1);
			if (timestamp != null) {
				obj = new Date(timestamp.getTime());
			}
		} else if (clazz.equals(Calendar.class)) {
			Calendar calendar = Calendar.getInstance();
			Timestamp timestamp = rs.getTimestamp(1);
			if (timestamp != null) {
				calendar.setTimeInMillis(timestamp.getTime());
				obj = calendar;
			}
		} else if (clazz.equals(Timestamp.class)) {
			Timestamp timestamp = rs.getTimestamp(1);
			obj = timestamp;
		} else if (clazz.equals(Boolean.class)) {
			obj = rs.getInt(1) == 0 ? false : true;
		} else if (clazz.equals(BigDecimal.class)) {
			obj = rs.getBigDecimal(1);
		} else {
			return false;
		}
		list.add((T)obj);
		return true;
	}

	public static <T> List<Map<String, Object>> convertResult(String[] selectFields, ResultSet rs, String aliasStr) throws Exception
	{
		List<Map<String, Object>> datas = new ArrayList<Map<String, Object>>();
		if (rs == null) return datas;
		Map<String, String> filedMap = null;
		if(aliasStr != null && !aliasStr.trim().equals("")) {
			filedMap = analysisAliasField(aliasStr);
		}
		while (rs.next()) {
			Map<String, Object> tempMap = new LinkedHashMap<String, Object>();
			if(filedMap == null) {
				ResultSetMetaData resultSetMetaData = rs.getMetaData();
				int colCount = resultSetMetaData.getColumnCount();
				for (int i = 1; i <= colCount; i++) {
					Object tempObject = rs.getObject(i);
					String colName = resultSetMetaData.getColumnLabel(i);
					if (tempObject != null && tempObject instanceof Date) {
						Calendar calendar = Calendar.getInstance();
						calendar.set(Calendar.MILLISECOND, 0);
						calendar.setTimeInMillis(((Date)tempObject).getTime());
						tempObject = calendar;
					} else if (tempObject != null && tempObject instanceof Timestamp) {
						Calendar calendar = Calendar.getInstance();
						calendar.set(Calendar.MILLISECOND, 0);
						calendar.setTimeInMillis(((Timestamp)tempObject).getTime());
						tempObject = calendar;
					} else if (tempObject != null && tempObject instanceof LocalDateTime) {
						Calendar calendar = DateUtil.localDateTime2Calendar((LocalDateTime)tempObject);
						calendar.set(Calendar.MILLISECOND, 0);
						tempObject = calendar;
					} else if (tempObject != null && tempObject instanceof BigDecimal) {
						int precision = rs.getMetaData().getPrecision(i);
						int scale = rs.getMetaData().getScale(i);
						if(scale > 0) {
							tempObject = ((BigDecimal)tempObject).doubleValue();
						} else if(scale == 0 && precision < 11) {
							tempObject = ((BigDecimal)tempObject).intValue();
						}  else {
							tempObject = ((BigDecimal)tempObject).longValue();
						}
					} else if (tempObject != null && (tempObject instanceof Clob || tempObject instanceof NClob)) {
						tempObject = rs.getString(i);
					}
					tempMap.put(colName.toUpperCase(), tempObject);
				}
			} else {
				for (int i = 0; i < selectFields.length; i++) {
					String key = filedMap.get(selectFields[i].toUpperCase().trim());
					Object tempObject = rs.getObject(selectFields[i].trim());
					if (tempObject != null && tempObject instanceof Date) {
						Calendar calendar = Calendar.getInstance();
						calendar.set(Calendar.MILLISECOND, 0);
						calendar.setTimeInMillis(((Date)tempObject).getTime());
						tempObject = calendar;
					} else if (tempObject != null && tempObject instanceof Timestamp) {
						Calendar calendar = Calendar.getInstance();
						calendar.set(Calendar.MILLISECOND, 0);
						calendar.setTimeInMillis(((Timestamp)tempObject).getTime());
						tempObject = calendar;
					} else if (tempObject != null && tempObject instanceof LocalDateTime) {
						Calendar calendar = DateUtil.localDateTime2Calendar((LocalDateTime)tempObject);
						calendar.set(Calendar.MILLISECOND, 0);
						tempObject = calendar;
					} else if (tempObject != null && tempObject instanceof BigDecimal) {
						int precision = rs.getMetaData().getPrecision(i + 1);
						int scale = rs.getMetaData().getScale(i + 1);
						if(scale > 0) {
							tempObject = ((BigDecimal)tempObject).doubleValue();
						} else if(scale == 0 && precision < 11) {
							tempObject = ((BigDecimal)tempObject).intValue();
						}  else {
							tempObject = ((BigDecimal)tempObject).longValue();
						}
					} else if (tempObject != null && (tempObject instanceof Clob || tempObject instanceof NClob)) {
						tempObject = rs.getString(selectFields[i].trim());
					}
					//处理mysql中的`
					tempMap.put(key!=null?key.replaceAll("`",""):null, tempObject);
				}
			}
			datas.add(tempMap);
		}
		return datas;
	}

//	/*
//	 * clob to string 大字符串格式转换STRING
//	 * @param clob
//	 * @return 大字符串
//	 */
//	public static String clob2String(CLOB clob) {
//		// Clob转换成String 的方法
//		String content = null;
//		StringBuffer stringBuf = new StringBuffer();
//		try {
//			int length = 0;
//			Reader inStream = clob.getCharacterStream();
//			// 取得大字侧段对象数据输出流
//			char[] buffer = new char[10];
//			while ((length = inStream.read(buffer)) != -1)
//			{
//				//读取数据库 //每10个10个读取
//				for (int i = 0; i < length; i++) {
//					stringBuf.append(buffer[i]);
//				}
//			}
//
//			inStream.close();
//			content = stringBuf.toString();
//		} catch (Exception ex) {
//			System.out.println("ClobUtil.Clob2String:" + ex.getMessage());
//		}
//		return content;
//	}

	/**
	 * 把类对象转换为数组
	 * @param obj       类对象
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	public static Object[] convertToArray(Object obj) throws IllegalArgumentException, IllegalAccessException
	{
		Class<? extends Object> cls = obj.getClass();
		Field[] fields = cls.getDeclaredFields();
		List<Object> tempList = new ArrayList<Object>();
		for (int i = 0; i < fields.length; i++) {
			Field field = fields[i];
			int mod = field.getModifiers();
			if (Modifier.isAbstract(mod) || Modifier.isFinal(mod)
					|| Modifier.isNative(mod) || Modifier.isStatic(mod))
				continue;
			try {
				field.setAccessible(true);
			} catch (SecurityException e) {}
			tempList.add(field.get(obj));
		}
		Object[] objects = new Object[tempList.size()];
		for(int i = 0; i < tempList.size(); i++) {
			objects[i] = tempList.get(i);
		}
		return objects;
	}
	/**
	 * 转换json数据为表可接收的数据类型
	 * @param jsonValue
	 * @param tableFieldDao
	 * @return
	 */
	public static Object transToTableData(Object jsonValue, TableFieldProp tableFieldDao) {
		int dataType = tableFieldDao.getDataType();
		switch (dataType) {
			case Types.CHAR:
			case Types.CLOB:
			case Types.DATALINK:
			case Types.LONGNVARCHAR:
			case Types.LONGVARBINARY:
			case Types.LONGVARCHAR:
			case Types.NCHAR:
			case Types.NCLOB:
			case Types.NVARCHAR:
			case Types.VARBINARY:
			case Types.VARCHAR:
				return DataTypeUtil.transformData(String.class, jsonValue);
			case Types.BIGINT:
			case Types.BOOLEAN:
			case Types.DECIMAL:
			case Types.DOUBLE:
			case Types.FLOAT:
			case Types.INTEGER:
			case Types.NUMERIC:
			case Types.REAL:
			case Types.SMALLINT:
			case Types.TINYINT:
				if(tableFieldDao.getDecimalDigits() > 0) {
					return DataTypeUtil.transformData(Double.class, jsonValue);
				} else if(tableFieldDao.getColumnSize() > 10) {
					return DataTypeUtil.transformData(Long.class, jsonValue);
				} else {
					return DataTypeUtil.transformData(Integer.class, jsonValue);
				}
			case Types.DATE:
			case Types.TIME:
			case Types.TIMESTAMP:
				return DataTypeUtil.transformData(Calendar.class, jsonValue);
			default:
				return DataTypeUtil.transformDataBySimpleName("byte[]", jsonValue);
		}
	}
	/**
	 * 把类对象转换为Map
	 * @param obj       类对象
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	public static Map<String, Object> convertToMap(Object obj) throws IllegalArgumentException, IllegalAccessException
	{
		Map<String, Object> tempMap = new LinkedHashMap<String, Object>();
		Class<? extends Object> cls = obj.getClass();
		Field[] fields = cls.getDeclaredFields();
		for (int i = 0; i < fields.length; i++) {
			Field field = fields[i];
			int mod = field.getModifiers();
			if (Modifier.isAbstract(mod) || Modifier.isFinal(mod)
					|| Modifier.isNative(mod) || Modifier.isStatic(mod))
				continue;
			try {
				field.setAccessible(true);
			} catch (SecurityException e) {}
			tempMap.put(field.getName(), field.get(obj));
		}
		return tempMap;
	}
	/**
	 * 别名解析器，此解析器以类属性名作为Key,别名的格式为:
	 * "属性名1-字段名1;属性名2-字段名2"
	 * 前面的参数为类属性名，后面的参数为数据库字段名
	 * @param aliasStr
	 * @return
	 */
	public static Map<String, String> analysisAliasProp(String aliasStr)
	{
		Map<String, String> tempMap = new LinkedHashMap<String, String>();
		String[] groupMapStr = aliasStr.split(";");
		for (int i = 0; i < groupMapStr.length; i++) {
			String tempMapStr = groupMapStr[i];
			String[] keyValueMap = tempMapStr.split("-");
			if(keyValueMap.length != 2) {
				continue;
			}
			tempMap.put(keyValueMap[0].trim(), keyValueMap[1].trim());
		}
		return tempMap;
	}
	/**
	 * 别名解析器，此解析器以数据库字段名作为Key,别名的格式为:
	 * "属性名1-字段名1;属性名2-字段名2"
	 * 前面的参数为类属性名，后面的参数为数据库字段名
	 * @param aliasStr
	 * @return
	 */
	public static Map<String, String> analysisAliasField(String aliasStr)
	{
		Map<String, String> tempMap = new LinkedHashMap<String, String>();
		String[] groupMapStr = aliasStr.split(";");
		for (int i = 0; i < groupMapStr.length; i++) {
			String tempMapStr = groupMapStr[i];
			String[] keyValueMap = tempMapStr.split("-");
			if(keyValueMap.length != 2) {
				continue;
			}
			tempMap.put(keyValueMap[1].toUpperCase().trim(), keyValueMap[0].trim());
		}
		return tempMap;
	}
	/**
	 * 返回插入性SQL语句数据库字段名集合
	 * @param sql     插入性SQL语句
	 * @return
	 */
	public static String[] getInsertFieldNames(String sql) {
		if(sql.toUpperCase().indexOf("UPDATE") > -1) {
			sql = sql.replaceAll("[\\s]*[=][\\s]*[?]", "=?");
			String[] tempStrArr = sql.split("=\\?");
			for (int i = 0; i < tempStrArr.length; i++) {
				int idx = tempStrArr[i].lastIndexOf(" ");
				if(idx > -1) {
					tempStrArr[i] = tempStrArr[i].substring(idx + 1);
				}
				idx = tempStrArr[i].lastIndexOf(",");
				if(idx > -1) {
					tempStrArr[i] = tempStrArr[i].substring(idx + 1);
				}
			}
			for (int i = 0; i < tempStrArr.length; i++) {
				tempStrArr[i] = tempStrArr[i].trim();
			}
			return tempStrArr;
		} else {
			int begIndex = sql.indexOf("(");
			int endIndex = sql.indexOf(")");
			String tempStr = sql.substring(begIndex+1, endIndex);
			String[] tempStrArr = tempStr.split(",");
			for (int i = 0; i < tempStrArr.length; i++) {
				tempStrArr[i] = tempStrArr[i].trim();
			}
			return tempStrArr;
		}
	}

	/**
	 * 返回插入性SQL语句数据表名称
	 * @param sql     插入性SQL语句
	 * @return
	 */
	/*public static String getInsertTableName(String sql) {
		sql = sql.toUpperCase().trim();
		if(sql.indexOf("UPDATE") > -1) {
			int bidx = sql.indexOf("UPDATE");
			int eidx = sql.indexOf("SET");
			if(bidx < 0 || eidx < 0) return "";
			sql = sql.substring(bidx + 6, eidx).trim();
		} else {
			int bidx = sql.indexOf("INTO");
			if(bidx < 0) {
				bidx = sql.indexOf("INSERT");
				bidx += 6;
			} else {
				bidx += 4;
			}
			int eidx = sql.indexOf("(");
			int tempEidx = sql.indexOf("VALUES");
			if(eidx > tempEidx) {
				eidx = tempEidx;
			}
			sql = sql.substring(bidx, eidx).trim();
		}
		return sql.trim();
	}*/
	// 获取插入或更新表名
	public static String getInsertTableName(String sql) {
		sql = sql.toUpperCase().trim();

		// 处理 UPDATE 语句
		if (sql.startsWith("UPDATE")) {
			// 使用正则表达式提取 UPDATE 后的表名
			Pattern updatePattern = Pattern.compile("^UPDATE\\s+([\\w\\d_]+)");
			Matcher matcher = updatePattern.matcher(sql);
			if (matcher.find()) {
				return matcher.group(1);  // 返回匹配的表名
			}
		}

		// 处理 INSERT INTO 语句
		if (sql.startsWith("INSERT")) {
			// 使用正则表达式提取 INSERT INTO 后的表名
			Pattern insertPattern = Pattern.compile("^INSERT\\s+INTO\\s+([\\w\\d_]+)");
			Matcher matcher = insertPattern.matcher(sql);
			if (matcher.find()) {
				return matcher.group(1);  // 返回匹配的表名
			}
		}

		return "";  // 返回空字符串表示没有表名
	}


	/**
	 * 返回插入性SQL语句数据表名称
	 * @param sql     插入性SQL语句
	 * @return
	 */
	public static String[] getInsertTableFiledName(String sql) {
		int bidx = sql.indexOf("(") + 1;
		int eidx = sql.indexOf(")");
		int tempEidx = sql.indexOf("VALUES");
		if(eidx > tempEidx) {
			return null;
		}
		String[] tableFieldNames = sql.substring(bidx, eidx).trim().split(",");
		for (int i = 0; i < tableFieldNames.length; i++) {
			int lastIndex  = tableFieldNames[i].lastIndexOf(".");
			if(lastIndex > 0) {
				tableFieldNames[i] = tableFieldNames[i].substring(lastIndex + 1);
			}
			tableFieldNames[i] = tableFieldNames[i].trim();
		}
		return tableFieldNames;
	}

	/**
	 * 返回插入性SQL语句数据库字段名集合
	 * @param sql     插入性SQL语句
	 * @return
	 */
	public static String[] getSelectFieldNames(String sql){
		String tempSql = sql.toUpperCase();
		int begIndex = tempSql.indexOf("SELECT");
		int brackets = tempSql.lastIndexOf(")");
		int endIndex = tempSql.lastIndexOf("FROM");
		String tempStr = "";
		if(brackets>endIndex){
			tempStr = sql.substring(begIndex+6, tempSql.length());
		}else{
			tempStr = sql.substring(begIndex+6, endIndex);
		}
		List<String> paramStrList = new ArrayList<String>();
		Stack<String> operands = new Stack<String>();
		int index = 0;
		for (int i = 0; i <tempStr.length(); i++) {
			char c = tempStr.charAt(i);
			if(c == '('){
				operands.add("(");
			}else if(c == ')'){
				operands.pop();
			}else if(c == ','){
				if(operands.size()==0){
					paramStrList.add(tempStr.substring(index, i).trim());
					index = i+1;
				}
			}
			if(i==tempStr.length()-1){
				paramStrList.add(tempStr.substring(index, i+1).trim());
			}
		}

		String[] paramStr = new String[paramStrList.size()];

		for (int i = 0; i < paramStrList.size(); i++) {
			String[] aliasNames = paramStrList.get(i).split(" ");
			paramStr[i] = aliasNames[aliasNames.length - 1];
		}
		return paramStr;
	}

//	public static void main(String[] args) {
//		System.out.println(getSelectFieldNames("select '1' as a,(select 2 from OU) as Vtotal"));
//	}

	/**
	 * 替换SQL语句参数,转换成可以直接执行的SQL语句
	 * @param sql       原SQL语句
	 * @param symbols   SQL语句参数,SQL语句参数用#?#方式进行占位
	 * @return
	 */
	public static String consituteSymbols(String sql, Object[] symbols){
		if(symbols == null || symbols.length == 0) {
			return sql;
		}
		for(int i = 0; i < symbols.length; i++) {
			sql = sql.replaceFirst("(.*?)[#][?][#](.*?)", "$1"+symbols[i]+"$2");
		}
		return sql;
	}

	private static String CHECKSQL = "^(.+)\\sand\\s(.+)|(.+)\\sor(.+)\\s$";
	/**
	 * 防SQL注入，对参数进行校验
	 * @param params
	 * @return
	 */
	public static boolean sqlParamCheck(Object params, Set<Object> checkSet) {
		if(params == null) return false;
		checkSet.add(params);
		if(params.getClass().isArray()) {
			int tempLength = Array.getLength(params);
			for (int i = 0; i < tempLength; i++) {
				Object tempParam = Array.get(params, i);
				if(tempParam == null) continue;
				Class<?> clazz = tempParam.getClass();
				if(clazz.isArray() && !clazz.equals(new byte[0].getClass())
						&& sqlParamCheck(tempParam, new HashSet<Object>())) {
					return true;
				} else if(tempParam instanceof String && Pattern.matches(CHECKSQL, (String)tempParam)) {
					return true;
				}
			}
		} else if(params instanceof List<?>) {
			List<?> paramList = (List<?>)params;
			for (int i = 0; i < paramList.size(); i++) {
				Object tempParam = paramList.get(i);
				if(tempParam == null) continue;
				if(!checkSet.contains(tempParam) && sqlParamCheck(tempParam, checkSet)) {
					return true;
				}
			}
		} else {
			Class<?> clazz = params.getClass();
			Field[] fields = clazz.getDeclaredFields();
			for (int i = 0; i < fields.length; i++) {
				Field field = fields[i];
				int mod = field.getModifiers();
				if (Modifier.isAbstract(mod) || Modifier.isFinal(mod) || Modifier.isNative(mod) || Modifier.isStatic(mod))
					continue;
				try {
					if (!Map.class.isAssignableFrom(clazz)) {
						field.setAccessible(true);
					}

				} catch (SecurityException e) {}
				try {
					Object object = field.get(params);
					if(!checkSet.contains(object) && sqlParamCheck(object, checkSet)) {
						return true;
					}
				} catch (Exception e) {}
			}
		}
		return false;
	}

	public static String getPlaceholder(int placeholderSize) {
		if(placeholderSize <= 0) return "";
		String str = "?";
		for (int i = 1; i < placeholderSize; i++) {
			str += ",?";
		}
		return str;
	}
	/**
	 * 把前端的数据转换成表格操作数据
	 * @param tableData
	 * @param tableFeildJson
	 * @return
	 */
	public static List<Map<String, Object>> transToTableDataList(JSONArray tableData, JSONObject tableFeildJson) {
		List<Map<String, Object>> tempParamList = new ArrayList<Map<String,Object>>();
		for (int i = 0; i < tableData.size(); i++) {
			JSONObject tempJsonObject = tableData.getJSONObject(i);
			Map<String, Object> tempMap = new LinkedHashMap<>();
			Iterator<Entry<String, Object>> iterator = tempJsonObject.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, Object> entry = iterator.next();
				String fieldKey = entry.getKey();
				if(tableFeildJson == null || tableFeildJson.getString(fieldKey) == null) {
					tempMap.put(fieldKey.toUpperCase(), entry.getValue());
				} else {
					tempMap.put(tableFeildJson.getString(fieldKey).toUpperCase(), entry.getValue());
				}
			}
			tempParamList.add(tempMap);
		}
		return tempParamList;
	}
	/**
	 * 通过字段和数据组织删除语句和数据
	 * @param fieldMap
	 * @param param
	 * @return
	 */
	public static OperateTableData getJudgeSql(Map<String, TableFieldProp> fieldMap, List<Map<String, Object>> param) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		String tempSql = "";
		boolean hasFieldKey = false;
		for (TableFieldProp tempData : fieldMap.values()) {
			if(tempData.isPrimary()) {
				hasFieldKey = true;
				break;
			}
		}
		Map<String, Object> firstRowMap = param.get(0);
		List<TableFieldProp> fieldKeyList = new ArrayList<>();
		Iterator<Entry<String, TableFieldProp>> iterator = fieldMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, TableFieldProp> entry = iterator.next();
			TableFieldProp tableFieldDao = entry.getValue();
			if (tableFieldDao.isPrimary() || (!hasFieldKey && firstRowMap.containsKey(tableFieldDao.getColumnName()))) {
				tempSql += (tempSql.equals("") ? "" : " AND ") + tableFieldDao.getColumnName() + "=?";
				fieldKeyList.add(tableFieldDao);
			}
		}
		Object[][] deleteParams = new Object[param.size()][fieldKeyList.size()];
		for (int i = 0; i < param.size(); i++) {
			Map<String, Object> rowParam = param.get(i);
			for (int j = 0; j < fieldKeyList.size(); j++) {
				deleteParams[i][j] = transToTableData(rowParam.get(fieldKeyList.get(j).getColumnName()), fieldKeyList.get(j));
			}
		}
		return new OperateTableData(tempSql, deleteParams);
	}
	/**
	 * 通过字段返回字段所在的表
	 * @param fieldName
	 * @param fieldMap
	 * @return
	 */
	public static String queryTableByField(String fieldName, Map<String, Map<String, TableFieldProp>> fieldMap) {
		Iterator<Entry<String, Map<String, TableFieldProp>>> iterator = fieldMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, Map<String, TableFieldProp>> entry = iterator.next();
			String tableName = entry.getKey();
			Map<String, TableFieldProp> entryValue = entry.getValue();
			if(entryValue.containsKey(fieldName)) {
				return tableName;
			}
		}
		return null;
	}
	/**
	 * 通过字段和数据组织删除语句和数据
	 * @param fieldMap
	 * @param param
	 * @return
	 */
	public static OperateTableData getQuerySqlOfMultiTable(Map<String, Map<String, TableFieldProp>> fieldMap, Map<String, Object> param) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		String tempSql = "";
		List<TableFieldProp> fieldKeyList = new ArrayList<>();
		if(fieldMap.size() == 1) {
			// 单表不加别名在字段前面
			for (String fieldKey : param.keySet()) {
				String tableName = queryTableByField(fieldKey, fieldMap);
				if(tableName != null) {
					fieldKeyList.add(fieldMap.get(tableName).get(fieldKey));
					tempSql += (tempSql.equals("") ? "" : " AND ") + fieldKey + "=?";
				}
			}
		} else {
			// 多表添加别名在字段前面
			for (String fieldKey : param.keySet()) {
				String tableName = queryTableByField(fieldKey, fieldMap);
				if(tableName != null) {
					fieldKeyList.add(fieldMap.get(tableName).get(fieldKey));
					tempSql += (tempSql.equals("") ? "" : " AND ") + tableName + "." + fieldKey + "=?";
				}
			}
		}
		Object[][] queryParams = new Object[1][fieldKeyList.size()];
		for (int j = 0; j < fieldKeyList.size(); j++) {
			queryParams[0][j] = transToTableData(param.get(fieldKeyList.get(j).getColumnName()), fieldKeyList.get(j));
		}
		return new OperateTableData(tempSql, queryParams);
	}
	/**
	 * 返回子查询SQL
	 * @param fieldMap
	 * @param param
	 * @param queryParam
	 * @return
	 */
	private static String getChildQuerySql(Map<String, TableFieldProp> fieldMap, List<Map<String, Object>> param, List<Object> queryParam) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		String querySql = "";
		for (int i = 0; i < param.size(); i++) {
			Map<String, Object> tempParam = param.get(i);
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> children = (List<Map<String, Object>>) tempParam.get(DBConst.SQLCriteria.CHILDREN);
			if(children != null && children.size() > 0) {
				String tempSql = getChildQuerySql(fieldMap, param, queryParam);
				if(tempSql != null) {
					querySql += "(" + tempSql + ")";
				}
			} else {
				String fieldName = (String) tempParam.get(DBConst.SQLCriteria.FIELD_NAME);
				Object fieldValue = tempParam.get(DBConst.SQLCriteria.FIELD_VALUE);
				String judging = (String) tempParam.get(DBConst.SQLCriteria.JUDGING);
				Boolean andOr = (Boolean) tempParam.get(DBConst.SQLCriteria.ANDOR);
				String andOrStr = "AND";
				if (andOr != null && !andOr) {
					andOrStr = "OR";
				}
				if (judging == null) {
					judging = "=";
				}
				TableFieldProp tableFieldData = fieldMap.get(fieldName);
				querySql += (querySql.equals("") ? "" : andOrStr) + fieldName + " " + judging;
				if(DBConst.JudgingConst.isNeedParam(judging)) {
					if(DBConst.JudgingConst.isLike(judging)) {
						querySql += " %?%";
						Object tempP = transToTableData(fieldValue, tableFieldData);
						queryParam.add(tempP);
					} else if(DBConst.JudgingConst.isIn(judging)) {
						Object[] temp = new Object[] { fieldValue };
						if(fieldValue instanceof Collection || fieldValue.getClass().isArray()) {
							temp = (Object[])DataTypeUtil.transformDataBySimpleName("Object[]", fieldValue);
						}
						for (int j = 0; j < temp.length; j++) {
							temp[j] = transToTableData(temp[j], tableFieldData);
						}
						queryParam.add(temp);
						querySql += " (" + getPlaceholder(temp.length) + ")";
					} else {
						Object tempP = transToTableData(fieldValue, tableFieldData);
						queryParam.add(tempP);
						querySql += "?";
					}
				}
			}
		}
		return querySql;
	}
	/**
	 * 返回子查询SQL
	 * @param fieldMap
	 * @param param
	 * @param queryParam
	 * @return
	 */
	private static String getChildMultiQuerySql(Map<String, Map<String, TableFieldProp>> fieldMap, List<Map<String, Object>> param, List<Object> queryParam) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		String querySql = "";
		for (int i = 0; i < param.size(); i++) {
			Map<String, Object> tempParam = param.get(i);
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> children = (List<Map<String, Object>>) tempParam.get(DBConst.SQLCriteria.CHILDREN);
			if(children != null && children.size() > 0) {
				String tempSql = getChildMultiQuerySql(fieldMap, param, queryParam);
				if(tempSql != null) {
					querySql += "(" + tempSql + ")";
				}
			} else {
				String fieldName = (String) tempParam.get(DBConst.SQLCriteria.FIELD_NAME);
				Object fieldValue = tempParam.get(DBConst.SQLCriteria.FIELD_VALUE);
				String judging = (String) tempParam.get(DBConst.SQLCriteria.JUDGING);
				Boolean andOr = (Boolean) tempParam.get(DBConst.SQLCriteria.ANDOR);
				String andOrStr = "AND";
				if (andOr != null && !andOr) {
					andOrStr = "OR";
				}
				if (judging == null) {
					judging = "=";
				}
				String tableName = queryTableByField(fieldName, fieldMap);
				TableFieldProp tableFieldData = fieldMap.get(tableName).get(fieldName);
				querySql += (querySql.equals("") ? "" : andOrStr) +
						((fieldMap.size() > 1) ? "" : tableName) + "." + fieldName + " " + judging;
				if(DBConst.JudgingConst.isNeedParam(judging)) {
					if(DBConst.JudgingConst.isLike(judging)) {
						querySql += " %?%";
						Object tempP = transToTableData(fieldValue, tableFieldData);
						queryParam.add(tempP);
					} else if(DBConst.JudgingConst.isIn(judging)) {
						Object[] temp = new Object[] { fieldValue };
						if(fieldValue instanceof Collection || fieldValue.getClass().isArray()) {
							temp = (Object[])DataTypeUtil.transformDataBySimpleName("Object[]", fieldValue);
						}
						for (int j = 0; j < temp.length; j++) {
							temp[j] = transToTableData(temp[j], tableFieldData);
						}
						queryParam.add(temp);
						querySql += " (" + getPlaceholder(temp.length) + ")";
					} else {
						Object tempP = transToTableData(fieldValue, tableFieldData);
						queryParam.add(tempP);
						querySql += "?";
					}
				}
			}
		}
		return querySql;
	}
	/**
	 * 返回按复杂查询条件组织后的SQL语句和数据
	 * @param fieldMap
	 * @param param
	 * @return
	 */
	public static OperateTableData getQueryConditionSqlOfTable(Map<String, TableFieldProp> fieldMap, List<Map<String, Object>> param) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		List<Object> queryParam = new ArrayList<>();
		// 多表添加别名在字段前面
		String tempSql = getChildQuerySql(fieldMap, param, queryParam);
		Object[][] queryParams = new Object[1][];
		queryParams[0] = queryParam.toArray();
		return new OperateTableData(tempSql, queryParams);
	}
	/**
	 * 返回按复杂查询条件组织后的SQL语句和数据
	 * @param fieldMap
	 * @param param
	 * @return
	 */
	public static OperateTableData getQueryConditionSqlOfMultiTable(Map<String, Map<String, TableFieldProp>> fieldMap, List<Map<String, Object>> param) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		List<Object> queryParam = new ArrayList<>();
		// 多表添加别名在字段前面
		String tempSql = getChildMultiQuerySql(fieldMap, param, queryParam);
		Object[][] queryParams = new Object[1][];
		queryParams[0] = queryParam.toArray();
		return new OperateTableData(tempSql, queryParams);
	}
	/**
	 * 通过字段和数据组织删除语句和数据
	 * @param fieldMap
	 * @param param
	 * @return
	 */
	public static OperateTableData getDeleteSql(Map<String, TableFieldProp> fieldMap, List<Map<String, Object>> param) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		String tempSql = "";
		List<TableFieldProp> fieldKeyList = new ArrayList<>();
		Map<String, Object> firstRowMap = param.get(0);
		Iterator<Entry<String, TableFieldProp>> iterator = fieldMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, TableFieldProp> entry = iterator.next();
			TableFieldProp tableFieldDao = entry.getValue();
			if (firstRowMap.containsKey(tableFieldDao.getColumnName())) {
				tempSql += (tempSql.equals("") ? "" : " AND ") + tableFieldDao.getColumnName() + "=?";
				fieldKeyList.add(tableFieldDao);
			}
		}
		Object[][] deleteParams = new Object[param.size()][fieldKeyList.size()];
		for (int i = 0; i < param.size(); i++) {
			Map<String, Object> rowParam = param.get(i);
			for (int j = 0; j < fieldKeyList.size(); j++) {
				deleteParams[i][j] = transToTableData(rowParam.get(fieldKeyList.get(j).getColumnName()), fieldKeyList.get(j));
			}
		}
		return new OperateTableData(tempSql, deleteParams);
	}

	/**
	 * 通过字段和数据组织删除语句和数据
	 * @param fieldMap
	 * @param param
	 * @return
	 */
	public static OperateTableData getUpdateSql(Map<String, TableFieldProp> fieldMap, List<Map<String, Object>> param) {
		if (fieldMap == null || fieldMap.size() == 0 || param == null || param.size() == 0)
			return null;
		String tempSql = "";
		List<TableFieldProp> fieldKeyList = new ArrayList<>();
		Map<String, Object> firstRowMap = param.get(0);
		Iterator<Entry<String, TableFieldProp>> iterator = fieldMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, TableFieldProp> entry = iterator.next();
			TableFieldProp tableFieldDao = entry.getValue();
			if (firstRowMap.containsKey(tableFieldDao.getColumnName())) {
				tempSql += (tempSql.equals("") ? "" : ",") + tableFieldDao.getColumnName() + "=?";
				fieldKeyList.add(tableFieldDao);
			}
		}
		Object[][] deleteParams = new Object[param.size()][fieldKeyList.size()];
		for (int i = 0; i < param.size(); i++) {
			Map<String, Object> rowParam = param.get(i);
			for (int j = 0; j < fieldKeyList.size(); j++) {
				deleteParams[i][j] = transToTableData(rowParam.get(fieldKeyList.get(j).getColumnName()), fieldKeyList.get(j));
			}
		}
		return new OperateTableData(tempSql, deleteParams);
	}
	/**
	 * 合并参数
	 * @param params
	 * @return
	 */
	public static Object[][] mergeParams(Object[][]... params) {
		if(params.length == 0 || params[0].length == 0) return null;
		int dimOneLength = params[0].length;
		Object[][] param = new Object[params[0].length][];
		for (int i = 0; i < dimOneLength; i++) {
			List<Object> tempList = new ArrayList<>();
			for (int j = 0; j < params.length; j++) {
				for (int j2 = 0; j2 < params[j][i].length; j2++) {
					tempList.add(params[j][i][j2]);
				}
			}
			param[i] = tempList.toArray();
		}
		return param;
	}
	/**
	 * 把数据查询字段组织成映射,为查询表中有as时用
	 * @param selfields
	 * @return
	 */
	public static String generatorAliasStr(String[] selfields) {
		String tempStr = "";
		for (int i = 0; i < selfields.length; i++) {
			if(i > 0) tempStr += ";";
			tempStr += selfields[i] + "-" + selfields[i];
		}
		return tempStr;
	}

	/**
	 * 通过SQL判断是否存在别名
	 * @param sql
	 * @return
	 */
	public static boolean judgeSqlContainAlias(String sql) {
		String tempSqlStr = sql.toUpperCase().trim();
		int selIdx = tempSqlStr.indexOf("SELECT ");
		int fromIdx = tempSqlStr.indexOf(" FROM");
		if(selIdx >= 0 && fromIdx > 0) {
			String subSqlStr = tempSqlStr.substring(selIdx + 7, fromIdx).trim();
			if(subSqlStr.contains(" AS ")) {
				return true;
			}
			String[] spltArr = subSqlStr.split(",");
			for (int i = 0; i < spltArr.length; i++) {
				if(spltArr[i].trim().contains(" ")) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 判断是否启用别名
	 * @param sql
	 * @param aliasStr
	 * @param rs
	 * @return
	 * @throws SQLException
	 */
	public static String judgeAlias(String sql, String aliasStr, ResultSet rs) throws SQLException {
		if (StringUtil.isEmpty(aliasStr) && judgeSqlContainAlias(sql)) {
			ResultSetMetaData metaData = rs.getMetaData();
			int count = metaData.getColumnCount();
			String selectFields[] = new String[count];
			for (int i = 1; i <= count; i++) {
				String label = metaData.getColumnLabel(i);
				String name = metaData.getColumnName(i);
				selectFields[i - 1] = label;
			}
			aliasStr = PSqlUtils.generatorAliasStr(selectFields);
		}
		return aliasStr;
	}

	/**
	 * 给AS后面的别名加特殊符号
	 *
	 * @param sql
	 * @param prefix
	 * @param suffix
	 * @return
	 */
	public static String addQuotToAsName(String sql, String prefix, String suffix) {
		if(!sql.contains(" AS ") && !sql.contains(" as ")) {
			return sql;
		}
		StringBuilder stringBuffer = new StringBuilder();
		int length = sql.length();
		String tempSql = sql;
		while(tempSql.length() > 0) {
			if(tempSql.startsWith(" AS ") || tempSql.startsWith(" as ")) {
				stringBuffer.append(" AS ");
				tempSql = tempSql.substring(4);
				int endIdx = tempSql.indexOf(",");
				int endIdx1 = tempSql.toUpperCase().indexOf(" FROM ");
				if(endIdx < 0 || (endIdx1 >= 1 && endIdx1 < endIdx)) {
					endIdx = endIdx1;
				}
				String tempStr = tempSql.substring(0, endIdx).trim();
				if(tempStr.indexOf(prefix) < 0 && tempStr.indexOf("'") < 0) {
					stringBuffer.append(prefix + tempStr + suffix);
				} else {
					stringBuffer.append(tempStr);
				}
				tempSql = tempSql.substring(endIdx);
			} else {
				stringBuffer.append(tempSql.charAt(0));
				tempSql = tempSql.substring(1);
			}
		}
		return stringBuffer.toString();
	}

	public static <T> List<Map<String, Object>> convertLocalDateResult(String[] selectFields, ResultSet rs, String aliasStr) throws Exception
	{
		List<Map<String, Object>> datas = new ArrayList<Map<String, Object>>();
		if (rs == null) return datas;
		Map<String, String> filedMap = null;
		if(aliasStr != null && !aliasStr.trim().equals("")) {
			filedMap = analysisAliasField(aliasStr);
		}
		while (rs.next()) {
			Map<String, Object> tempMap = new LinkedHashMap<String, Object>();
			if(filedMap == null) {
				ResultSetMetaData resultSetMetaData = rs.getMetaData();
				int colCount = resultSetMetaData.getColumnCount();
				for (int i = 1; i <= colCount; i++) {
					Object tempObject = rs.getObject(i);
					String colName = resultSetMetaData.getColumnLabel(i);
					if (tempObject != null && tempObject instanceof Date) {
						Instant instant = ((Date)tempObject).toInstant();
						ZoneId zone = ZoneId.systemDefault();
						tempObject = LocalDateTime.ofInstant(instant, zone);
					} else if (tempObject != null && tempObject instanceof Timestamp) {
						tempObject = ((Timestamp)tempObject).toLocalDateTime();
					} else if (tempObject != null && tempObject instanceof Calendar) {
						Instant instant = ((Calendar)tempObject).toInstant();
						ZoneId zone = ZoneId.systemDefault();
						tempObject = LocalDateTime.ofInstant(instant, zone);
					} else if (tempObject != null && tempObject instanceof BigDecimal) {
						int precision = rs.getMetaData().getPrecision(i);
						int scale = rs.getMetaData().getScale(i);
						if(scale > 0) {
							tempObject = ((BigDecimal)tempObject).doubleValue();
						} else if(scale == 0 && precision < 11) {
							tempObject = ((BigDecimal)tempObject).intValue();
						}  else {
							tempObject = ((BigDecimal)tempObject).longValue();
						}
					} else if (tempObject != null && (tempObject instanceof Clob || tempObject instanceof NClob)) {
						tempObject = rs.getString(i);
					}
					tempMap.put(colName.toUpperCase(), tempObject);
				}
			} else {
				for (int i = 0; i < selectFields.length; i++) {
					String key = filedMap.get(selectFields[i].trim());
					Object tempObject = rs.getObject(selectFields[i].trim());
					if (tempObject != null && tempObject instanceof Date) {
						Instant instant = ((Date)tempObject).toInstant();
						ZoneId zone = ZoneId.systemDefault();
						tempObject = LocalDateTime.ofInstant(instant, zone);
					} else if (tempObject != null && tempObject instanceof Timestamp) {
						tempObject = ((Timestamp)tempObject).toLocalDateTime();
					}  else if (tempObject != null && tempObject instanceof Calendar) {
						Instant instant = ((Calendar)tempObject).toInstant();
						ZoneId zone = ZoneId.systemDefault();
						tempObject = LocalDateTime.ofInstant(instant, zone);
					} else if (tempObject != null && tempObject instanceof BigDecimal) {
						int precision = rs.getMetaData().getPrecision(i + 1);
						int scale = rs.getMetaData().getScale(i + 1);
						if(scale > 0) {
							tempObject = ((BigDecimal)tempObject).doubleValue();
						} else if(scale == 0 && precision < 11) {
							tempObject = ((BigDecimal)tempObject).intValue();
						}  else {
							tempObject = ((BigDecimal)tempObject).longValue();
						}
					} else if (tempObject != null && (tempObject instanceof Clob || tempObject instanceof NClob)) {
						tempObject = rs.getString(selectFields[i].trim());
					}
					tempMap.put(key, tempObject);
				}
			}
			datas.add(tempMap);
		}
		return datas;
	}

	public static String convertTableNamesToLowerCase(String sql) {
		Pattern pattern = Pattern.compile("\\b(?:DELETE FROM|DELETE|FROM|JOIN LEFT|JOIN RIGHT|JOIN|UPDATE|INTO)\\s+([\\w]+)", Pattern.CASE_INSENSITIVE);
		Matcher matcher = pattern.matcher(sql);
		StringBuffer newSql = new StringBuffer();

		while (matcher.find()) {
			String oldName = matcher.group(1);
			String newName = oldName.toLowerCase();
			matcher.appendReplacement(newSql, matcher.group().replace(oldName, newName));
		}
		matcher.appendTail(newSql);

		return newSql.toString();
	}
}
