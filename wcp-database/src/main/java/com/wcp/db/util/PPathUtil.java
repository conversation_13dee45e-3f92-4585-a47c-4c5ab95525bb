package com.wcp.db.util;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.List;

/**
 * 路径工具类
 * <AUTHOR>
 * @date 2018.11.15
 */
public class PPathUtil 
{
	private static String registerPath = "";
	//手动注注册连接文件根目录
	public static void setRegisterPath(String registerPath) {
		PPathUtil.registerPath = registerPath;
	}
	/**
	 * 获取工程项目根目录的绝对路径
	 * @return
	 */
	public static String getRootPath() {
		URL url = PPathUtil.class.getClassLoader().getResource("/");
		if(url == null) {
			File file = new File(".");
			try {
				return file.getCanonicalPath();
			} catch (IOException e) {
				return file.getAbsolutePath();
			}
		}
		String classPath = url.getPath();
		String rootPath = "";
		// windows下
		if ("\\".equals(File.separator)) {
			rootPath = classPath.substring(1, classPath.indexOf("/WEB-INF/classes"));
			rootPath = rootPath.replace("/", "\\");
		} else if ("/".equals(File.separator)) {
		// linux下
			rootPath = classPath.substring(0, classPath.indexOf("/WEB-INF/classes"));
			rootPath = rootPath.replace("\\", "/");
		}
		rootPath = URLDecoderString(rootPath);
		return rootPath;
	}
	/**
	 * 获取工程项目默认配置文件的绝对路径
	 * @return
	 */
	public static String getFilePath() {
		//在某些调试模式下可以手动注册目录
		if(registerPath != null && !registerPath.equals("")) return registerPath;
		URL url = PPathUtil.class.getClassLoader().getResource("/");
		if(url == null) {
			File file = new File(".");
			try {
				return file.getCanonicalPath();
			} catch (IOException e) {
				return file.getAbsolutePath();
			}
		}
		String classPath = url.getPath();
		String rootPath = "";
		// windows下
		if ("\\".equals(File.separator)) {
			rootPath = classPath.substring(1, classPath.indexOf("/classes"));
			rootPath = rootPath.replace("/", "\\");
		} else if ("/".equals(File.separator)) {
		// linux下
			rootPath = classPath.substring(0, classPath.indexOf("/classes"));
			rootPath = rootPath.replace("\\", "/");
		}
		rootPath = URLDecoderString(rootPath);
		return rootPath;
	}
	
	public static String URLDecoderString(String str) {
        String result = "";
        if (null == str) {
            return "";
        }
        try {
            result = java.net.URLDecoder.decode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return result;
    }
	/**
	 * 日志特殊字符校验
	 * @param log
	 * @return
	 */
	public static String convertValidLog(String log){
	    List<String> list = new ArrayList<String>();
	    list.add("%0d");
	    list.add("\r");
	    list.add("%0a");
	    list.add("\n");
	    // 将日志内容归一化
	    String encode = Normalizer.normalize(log, Normalizer.Form.NFKC);
	    for(String toReplaceStr : list){
	        encode = encode.replace(toReplaceStr, "");
	    }
	    return encode;
	}
}
