package com.wcp.db.util;
import com.alibaba.fastjson.JSONObject;
import com.wcp.db.data.DmConfiguration;
import com.wcp.db.enums.DatasourceTypes;
import com.wcp.utils.AESUtil;
import io.dataease.plugins.common.base.domain.Datasource;
import io.dataease.plugins.common.dto.datasource.TableDesc;
import io.dataease.plugins.common.dto.datasource.TableField;
import io.dataease.plugins.common.request.datasource.DatasourceRequest;
import io.dataease.plugins.datasource.entity.MysqlConfiguration;
import io.dataease.plugins.datasource.entity.OracleConfiguration;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;
import java.util.Map;

/**
 * Description: 获取系统建模数据库相关元数据
 * Author: qianchao
 * Date: 2024/3/19 15:00
 */
public class DatabaseMetadataUtil {
    public static void main(String[] args) {
        String url="jdbc:dm://*************:5898?schema=WCP_XIAMEN";
        String username="wcp";
        String password="Whzh$123456";
        try{
            System.out.println(AESUtil.encrypt("Whzh$@123456"));
           /* Class.forName("dm.jdbc.driver.DmDriver");
            Connection connection= DriverManager.getConnection(url,username,password);*/
            /*MysqlConfiguration mysqlConfiguration=new MysqlConfiguration();
            mysqlConfiguration.setHost("**************");
            mysqlConfiguration.setPort(8306);
            mysqlConfiguration.setDataBase("ebi_pro");
            mysqlConfiguration.setUsername("ebi_pro");
            mysqlConfiguration.setPassword("5ENJFBjnnCwdE3rY");
            mysqlConfiguration.setDataSourceType(DatasourceTypes.mysql.getType());

            DatasourceRequest datasourceRequest=new DatasourceRequest();
            Datasource datasource=new Datasource();
            datasource.setConfiguration(JSONObject.toJSONString(mysqlConfiguration));
            datasource.setType(DatasourceTypes.mysql.getType());
            datasourceRequest.setDatasource(datasource);
            JdbcProvider provider=new JdbcProvider();
         //   List<TableDesc> tables=provider.getTables(datasourceRequest);
            datasourceRequest.setQuery("SELECT * FROM WCP_SP_USER LIMIT 10");
            List<Map<String,Object>> dataList=provider.getDataList(datasourceRequest);
            dataList.forEach(t->{
                System.out.println(t);
            });*/
         /*   OracleConfiguration oracleConfiguration=new OracleConfiguration();
            oracleConfiguration.setHost("*************");
            oracleConfiguration.setPort(1521);
            oracleConfiguration.setUsername("wcp");
            oracleConfiguration.setPassword("Whzh$123456");
            oracleConfiguration.setDataBase("orcl");
            oracleConfiguration.setSchema("WCP");
            oracleConfiguration.setDataSourceType(DatasourceTypes.oracle.getType());
            oracleConfiguration.setConnectionType(DatasourceTypes.oracle.getType());*/

            DmConfiguration dmConfiguration=new DmConfiguration();
            dmConfiguration.setHost("***************");
            dmConfiguration.setUsername("SYSDBA");
            dmConfiguration.setPassword("Whzh$@123456");
            dmConfiguration.setPort(8898);
            dmConfiguration.setDataBase("DB_FFS");
            dmConfiguration.setSchema("DB_FFS");
            dmConfiguration.setDataSourceType(DatasourceTypes.dm.getType());
            DatasourceRequest datasourceRequest=new DatasourceRequest();
            Datasource datasource=new Datasource();
            datasource.setConfiguration(JSONObject.toJSONString(dmConfiguration));
            datasource.setType(DatasourceTypes.dm.getType());
            datasourceRequest.setDatasource(datasource);
            datasourceRequest.setQuery("SELECT * FROM WCP_GC_DATA LIMIT 10");
            JdbcProvider provider=new JdbcProvider();
        //    List<TableDesc> tables=provider.getTables(datasourceRequest);
            List<Map<String,Object>> dataList=provider.getDataList(datasourceRequest);
            dataList.forEach(t->{
                System.out.println(t);
            });
           /* datasourceRequest.setTable("WCP_GA_ROLE");
            List<TableField> tableFields=provider.getTableFields(datasourceRequest);
            tableFields.forEach(t->{
                System.out.println(t.getFieldName()+">>>"+t.getFieldType()+"("+t.getFieldSize()+")>>>>"+t.getRemarks());
            });
*/

        }catch (Exception e){
            e.printStackTrace();
        }


    }

    }
