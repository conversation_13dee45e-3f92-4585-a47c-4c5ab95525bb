package com.wcp.db.util;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 查询组织工具
 * <AUTHOR>
 * @date 2021.09.04
 */
public class PQueryUtil {
	/**
	 * 查询表的别名
	 * @param tableNameCache
	 * @param tableName
	 * @return
	 */
	public static String queryTableAlias(Map<String, String> tableNameCache, String tableName) {
		if(tableNameCache == null || tableNameCache.size() == 0) {
			tableNameCache.put(tableName, "A");
			return "A";
		}
		String tableAlias = tableNameCache.get(tableName);
		if(tableAlias == null) {
			String lastChar = getTailByReflection((LinkedHashMap<String, String>)tableNameCache).getValue();
			char tempChar = lastChar.charAt(0);
			tableAlias = ++tempChar + "";
			tableNameCache.put(tableName, tableAlias);
		}
		return tableAlias;
	}

	/**
	 * 查询表的别名
	 * @param tableFieldNameCache
	 * @param tableFieldName
	 * @return
	 */
	public static String queryTableFieldAlias(Map<String, String> tableFieldNameCache, String tableFieldName) {
		String tableFieldAlias = tableFieldNameCache.get(tableFieldName);
		if(tableFieldAlias == null) {
			tableFieldNameCache.put(tableFieldName, tableFieldName);
		}
		return tableFieldName;
	}
	/**
	 * 获取Map的最后一个元素
	 * @param <K>
	 * @param <V>
	 * @param map
	 * @return
	 * @throws NoSuchFieldException
	 * @throws IllegalAccessException
	 */
	@SuppressWarnings("unchecked")
	public static <K, V> Entry<K, V> getTailByReflection(LinkedHashMap<K, V> map) {
		try {
			Field tail = map.getClass().getDeclaredField("tail");
			tail.setAccessible(true);
			return (Entry<K, V>) tail.get(map);
		} catch (Exception e) {
		}
		return null;
	}
}
