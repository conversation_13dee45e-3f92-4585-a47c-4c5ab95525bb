package com.wcp.db.data;

public class TableFieldProp {
    /**
     * 数据类型 java.sql.Types
     */
    private int dataType;
    /**
     * 列字段名称
     */
    private String columnName;
    /**
     * 整数位数长度
     */
    private Integer columnSize;
    /**
     * 小数位数长度
     */
    private Integer decimalDigits;
    /**
     * 是否是主健
     */
    private boolean isPrimary;
    /**
     * 是否可以为空
     * 0:不能为空
     * 1:可以为空
     * 2:不确定
     */
    private int nullable;
    /**
     * 默认值
     */
    private String defaultValue;
    /**
     * 主健索引
     */
    private int primarySeq;
    /**
     * 列字段名称注释
     */
    private String fileRemark;

    /**
     * 创建字段属性
     *
     * @param dataType      数据类型 java.sql.Types
     * @param clumnName     列字段名称
     * @param columnSize    整数位数长度
     * @param decimalDigits 小数位数长度
     */
    public TableFieldProp(int dataType, String clumnName, Integer columnSize, Integer decimalDigits) {
        this(dataType, clumnName, columnSize, decimalDigits, "");
    }

    /**
     * 创建字段属性
     *
     * @param dataType      数据类型 java.sql.Types
     * @param clumnName     列字段名称
     * @param columnSize    整数位数长度
     * @param decimalDigits 小数位数长度
     */
    public TableFieldProp(int dataType, String clumnName, Integer columnSize, Integer decimalDigits, String fileRemark) {
        this(dataType, clumnName, columnSize, decimalDigits, 1, null, fileRemark);
    }

    public TableFieldProp(int dataType, String columnName, Integer columnSize, Integer decimalDigits, int primarySeq,
                          int nullable, String defaultValue, boolean isPrimary) {
        this.dataType = dataType;
        this.columnName = columnName;
        this.columnSize = columnSize;
        this.decimalDigits = decimalDigits;
        this.nullable = nullable;
        this.defaultValue = defaultValue;
        this.primarySeq = primarySeq;
        this.isPrimary = isPrimary;
    }

    public TableFieldProp(int dataType, String columnName, Integer columnSize, Integer decimalDigits,
                          int nullable, String defaultValue, String fileRemark) {
        this.dataType = dataType;
        this.columnName = columnName;
        this.columnSize = columnSize;
        this.nullable = nullable;
        this.defaultValue = defaultValue;
        this.decimalDigits = decimalDigits;
        this.fileRemark = fileRemark;
    }

    public int getDataType() {
        return dataType;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public Integer getColumnSize() {
        return columnSize;
    }

    public void setColumnSize(Integer columnSize) {
        this.columnSize = columnSize;
    }

    public Integer getDecimalDigits() {
        return decimalDigits;
    }

    public void setDecimalDigits(Integer decimalDigits) {
        this.decimalDigits = decimalDigits;
    }

    public int getPrimarySeq() {
        return primarySeq;
    }

    public void setPrimarySeq(int primarySeq) {
        this.primarySeq = primarySeq;
    }

    public boolean isPrimary() {
        return isPrimary;
    }

    public void setPrimary(boolean isPrimary) {
        this.isPrimary = isPrimary;
    }

    public int getNullable() {
        return nullable;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public String getFileRemark() {
        return fileRemark;
    }

    public void setFileRemark(String fileRemark) {
        this.fileRemark = fileRemark;
    }

    public void setNullable(int nullable) {
        this.nullable = nullable;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public String toString() {
        return "TableFieldProp{" +
                "dataType=" + dataType +
                ", columnName='" + columnName + '\'' +
                ", columnSize=" + columnSize +
                ", decimalDigits=" + decimalDigits +
                ", isPrimary=" + isPrimary +
                ", nullable=" + nullable +
                ", defaultValue='" + defaultValue + '\'' +
                ", primarySeq=" + primarySeq +
                ", fileRemark='" + fileRemark + '\'' +
                '}';
    }
}
