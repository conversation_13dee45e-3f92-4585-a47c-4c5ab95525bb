package com.wcp.db.data;

public class TableProp 
{
	/** 表类别 */
	private String tableCat;
	/** 表模式 */
	private String tableScheme;
	/** 表名称 */
	private String tableName;
	/** 表类型("TABLE"、"VIEW"、"SYSTEM TABLE"、"GLOBAL TEMPORARY"、"LOCAL TEMPORARY"、"ALIAS" 、 "SYNONYM") */
	private String tableType;
	/** 表描述 */
	private String tableDesc;

	public TableProp() {
		
	}
	/**
	 * 构造表结果
	 * @param tableCat          表类别
	 * @param tableScheme       表模式
	 * @param tableName         表名称
	 * @param tableType         表类型
	 * @param tableDesc         表描述
	 */
	public TableProp(String tableCat, String tableScheme, String tableName, String tableType, String tableDesc) {
		this.tableCat = tableCat;
		this.tableScheme = tableScheme;
		this.tableName = tableName;
		this.tableType = tableType;
		this.tableDesc = tableDesc;
	}
	/**
	 * 返回表所在的类别
	 * @return
	 */
	public String getTableCat() {
		return tableCat;
	}
	/**
	 * 设置表所在的类别
	 * @param tableCat
	 */
	public void setTableCat(String tableCat) {
		this.tableCat = tableCat;
	}
	/**
	 * 返回表所在的模式
	 * @return
	 */
	public String getTableScheme() {
		return tableScheme;
	}
	/**
	 * 设置表所在的模式
	 * @param tableScheme
	 */
	public void setTableScheme(String tableScheme) {
		this.tableScheme = tableScheme;
	}
	/**
	 * 返回表名称
	 * @return
	 */
	public String getTableName() {
		return tableName;
	}
	/**
	 * 设置表名称
	 * @param tableName
	 */
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	/**
	 * 返回表类型("TABLE"、"VIEW"、"SYSTEM TABLE"、"GLOBAL TEMPORARY"、"LOCAL TEMPORARY"、"ALIAS" 、 "SYNONYM")
	 * @return
	 */
	public String getTableType() {
		return tableType;
	}
	/**
	 * 设置表类型("TABLE"、"VIEW"、"SYSTEM TABLE"、"GLOBAL TEMPORARY"、"LOCAL TEMPORARY"、"ALIAS" 、 "SYNONYM")
	 * @param tableType
	 */
	public void setTableType(String tableType) {
		this.tableType = tableType;
	}
	/**
	 * 返回表描述
	 * @return
	 */
	public String getTableDesc() {
		return tableDesc;
	}
	/**
	 * 设置表描述
	 * @param tableDesc
	 */
	public void setTableDesc(String tableDesc) {
		this.tableDesc = tableDesc;
	}

    @Override
    public String toString() {
        return "TableProp{" +
                "tableCat='" + tableCat + '\'' +
                ", tableScheme='" + tableScheme + '\'' +
                ", tableName='" + tableName + '\'' +
                ", tableType='" + tableType + '\'' +
                ", tableDesc='" + tableDesc + '\'' +
                '}';
    }
}
