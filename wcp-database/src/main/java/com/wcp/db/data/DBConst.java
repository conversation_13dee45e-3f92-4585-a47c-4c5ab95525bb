package com.wcp.db.data;

import java.util.LinkedHashMap;
import java.util.Map;

public class DBConst 
{
	/**
	 * 级联查询定义
	 * <AUTHOR>
	 * @date 2020.11.09
	 */
	public static class JoinConst {
		public final static String innerJoin = "innerJoin";
		public final static String leftJoin = "leftJoin";
		public final static String rightJoin = "rightJoin";
		public final static String fullJoin = "fullJoin";
		public final static String crossJoin = "crossJoin"; 
		
		/**
		 * 通过级连联标志返回级联语句
		 * @param joinType
		 * @return
		 */
		public static String queryJoinSql(String joinType) {
			if(joinType == null) {
				return "JOIN";
			} else if(joinType.equals(leftJoin)) {
				return "LEFT JOIN";
			} else if(joinType.equals(rightJoin)) {
				return "RIGHT JOIN";
			} else if(joinType.equals(fullJoin)) {
				return "FULL JOIN";
			} else if(joinType.equals(crossJoin)) {
				return "CROSS JOIN";
			}
			return "JOIN";
		}
		/**
		 * 返回连接映射关系集合
		 * @return
		 */
		public static Map<String, String> queryJoinMapping() {
			Map<String, String> tempMap = new LinkedHashMap<String, String>();
			tempMap.put(innerJoin, "内连接");
			tempMap.put(leftJoin, "左连接");
			tempMap.put(rightJoin, "右连接");
			tempMap.put(fullJoin, "全连接");
			tempMap.put(fullJoin, "交叉连接");
			return tempMap;
		}
	}
	
	/**
	 * 级联查询定义
	 * <AUTHOR>
	 * @date 2020.11.09
	 */
	public static class JudgingConst {
		public final static String greater = ">";
		public final static String greaterEqual = ">=";
		public final static String notEqual = "<>";
		public final static String equal = "=";
		public final static String less = "<";
		public final static String lessEqual = "<=";
		public final static String like = "LIKE";
		public final static String isNull = "IS NULL";
		public final static String isNotNull = "IS NOT NULL";
		public final static String in = "IN";
		public final static String notIn = "ONT IN";
		
		/**
		 * 判断是否需要参数
		 * @param judging
		 * @return
		 */
		public static boolean isNeedParam(String judging) {
			if(judging != null && (judging.equals(isNull) || judging.equals(isNotNull))) {
				return false;
			}
			return true;
		}
		/**
		 * 判断是否需是相似判断
		 * @param judging
		 * @return
		 */
		public static boolean isLike(String judging) {
			if(judging != null && judging.equals(like)) {
				return true;
			}
			return false;
		}
		/**
		 * 判断是否需是包含判断
		 * @param judging
		 * @return
		 */
		public static boolean isIn(String judging) {
			if(judging != null && (judging.equals(in) || judging.equals(notIn))) {
				return true;
			}
			return false;
		}
		/**
		 * 返回判断映射关系集合
		 * @return
		 */
		public static Map<String, String> queryJudgingMapping() {
			Map<String, String> tempMap = new LinkedHashMap<String, String>();
			tempMap.put("<>", "不等于");
			tempMap.put("=", "等于");
			tempMap.put(">", "大于");
			tempMap.put(">=", "大于等于");
			tempMap.put("<", "小于");
			tempMap.put("<=", "小于等于");
			tempMap.put("LIKE", "相似");
			tempMap.put("IS NULL", "等于空");
			tempMap.put("IS NOT NULL", "不等于空");
			tempMap.put("IN", "包含");
			tempMap.put("ONT IN", "不包含");
			return tempMap;
		}
	}
	
	/**
	 * 模版与数据库表映射查询条件定义
	 * <AUTHOR>
	 * @date 2020.05.04
	 */
	public static class SQLCriteria {
		/** 子查询 */
		public static final String CHILDREN = "children";
		/** 缓存字段名称 */
		public static final String FIELD_NAME = "fieldName";
		/** 查询对应的字段值 */
		public static final String FIELD_VALUE = "fieldValue";
		/** 判断条件 */
		public static final String JUDGING = "judging";
		/** AND OR */
		public static final String ANDOR = "andOr";
		/** 前缀 */
		public static final String PRFIX = "prefix";
		/** 后缀 */
		public static final String SUFFIX = "suffix";
		/** 排序是倒序或正序 */
		public static final String ASC = "asc";
	}
}
