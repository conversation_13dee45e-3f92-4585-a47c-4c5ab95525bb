package com.wcp.db.data;

import io.dataease.plugins.datasource.entity.JdbcConfiguration;
import lombok.Getter;
import lombok.Setter;

/**
 * 达梦驱动类
 */
@Getter
@Setter
public class DmConfiguration extends JdbcConfiguration {

    private String driver = "dm.jdbc.driver.DmDriver";
    private String extraParams;


    public String getJdbc() {
        return "jdbc:dm://HOST:PORT?SCHEMA=VALUE"
                .replace("HOST", getHost().trim())
                .replace("PORT", getPort().toString())
                .replace("VALUE",getDataBase().trim());
    }
}
