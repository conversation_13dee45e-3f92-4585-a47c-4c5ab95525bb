<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wcp</artifactId>
        <groupId>com.wcp</groupId>
        <version>3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>wcp-database</artifactId>

        <dependencies>
            <!-- 通用工具-->
            <dependency>
                <groupId>com.wcp</groupId>
                <artifactId>wcp-common</artifactId>
                <version>3.0</version>
            </dependency>
            <!--数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.2</version>
            </dependency>
            <dependency>
                <groupId>io.dataease</groupId>
                <artifactId>dataease-plugin-datasource</artifactId>
                <version>1.18.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.xiaoymin</groupId>
                        <artifactId>knife4j-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <!--稳定版本的仓库地址,必须是允许上传的私服地址-->
        <repository>
            <!--id和maven setting文件server id保持一致则不需要配置用户名密码，否则需要在pom中配置用户名密码-->
            <id>maven-releases</id>
            <url>http://171.43.138.200:8085/repository/maven-releases/</url>
        </repository>
    </distributionManagement>
</project>