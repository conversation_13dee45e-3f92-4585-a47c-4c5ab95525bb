<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wcp</artifactId>
        <groupId>com.wcp</groupId>
        <version>3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>wcp-platform</artifactId>
    <description>
        WCP服务入口
    </description>

    <dependencies>
        <!-- 通用工具-->
        <!--<dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-common</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>-->
        <!-- pagehelper 分页插件 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.3</version>
        </dependency>
        <!--websocket-->
        <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <!-- spring security 安全认证 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <!-- 自定义验证注解 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!--工作流-->
        <dependency>
            <groupId>org.dromara.warm</groupId>
            <artifactId>warm-flow-mybatis-plus-sb-starter</artifactId>
            <version>1.7.2-beat</version>
        </dependency>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-core</artifactId>
            <version>3.0</version>
        </dependency>
        <!-- database调用-->
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-database</artifactId>
            <version>3.0</version>
        </dependency>
<!--        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.7.0</version>
        </dependency>-->
        <!--swagger        -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <!--doc.html模式    -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.2</version>
        </dependency>
        <!--netty -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.75.Final</version>
        </dependency>
       <!-- <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-dysmsapi20170525</artifactId>
            <version>4.0.3</version>
        </dependency>-->


    </dependencies>
    <build>
        <!--打包后的jar名称-->
        <finalName>wcp-platform</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <!--MANIFEST.MF 中 Class-Path 加入前缀！用命令java -jar运行jar时就不用-Dloader.path指定外部资源路径了-->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!--jar包名字是否包含唯一版本标识-->
                            <useUniqueVersions>false</useUniqueVersions>
                            <!--指定含main方法的主类入口-->
                            <mainClass>com.wcp.PlatfromApplication</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!--MANIFEST.MF 中 Class-Path 加入资源文件目录！用命令java -jar时就不用-Dloader.path指定外部资源路径了 -->
                            <Class-Path>resources/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <!-- 打包时从jar包里排除资源文件 -->
                    <excludes>
                        <exclude>*.yml</exclude>
                        <exclude>*.properties</exclude>
                    </excludes>
                    <!-- 指定项目打成jar包输出位置 -->
                    <outputDirectory>${project.build.directory}/wcp-platform</outputDirectory>
                </configuration>
            </plugin>
            <!-- 拷贝依赖jar包！插件maven-jar-plugin只是打包排除文件，
            而把依赖jar包拷贝到外部lib目录就需要maven-dependency-plugin插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/wcp-platform/lib/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--拷贝资源文件！ 插件maven-jar-plugin只负责打包时排除文件，
                而把资源文件拷贝到外部resource目录就需要maven-dependency-plugin插件-->
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                               <!-- <resource>
                                    <directory>src/main/resources/config</directory>
                                    <filtering>true</filtering>
                                </resource>-->
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>true</filtering>
                                    <excludes>
                                        <exclude>mapper/**</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                           <!-- <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <includes>
                                        <include>**/mapper/**</include>
                                    </includes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>-->
                            <!-- 把“<resource><directory>”指定目录中的文件输出到此处指定目录 -->
                            <outputDirectory>${project.build.directory}/wcp-platform/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>