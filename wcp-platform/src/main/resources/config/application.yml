server:
  port: 9090
  servlet:
    context-path: /wcp-platform
spring:
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 50MB
  security: true #true代表启用权限拦截,false代表关闭权限拦截
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  redis:
    host: **************
    port: 8379
    password: 5ENJFBjnnCwdE3rY
    redis-message: 127.0.0.1:9091   # 根据配置的ID 修改数据源的时候调用刷新接口 用逗号分隔 Ip:Port,Ip:Port
  minio:
    endpoint: http://**************:9000
    accessKey: zonghe666
    secretKey: zonghe666
    bucketName: zonghe
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    #url: jdbc:mysql://**************:8306/ebi?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    #username: ebi
    #password: 5ENJFBjnnCwdE3rY
    url: ************************************************************************************************************
    username: root
    password: Admin@123456
    # 初始连接数
    initialSize: 5
    # 最小连接池数量
    minIdle: 10
    # 最大连接池数量
    maxActive: 20
    # 配置获取连接等待超时的时间
    maxWait: 60000
    # 配置连接超时时间
    connectTimeout: 30000
    # 配置网络超时时间
    socketTimeout: 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    timeBetweenEvictionRunsMillis: 30000
    # 配置一个连接在池中最小生存的时间，单位是毫秒
    minEvictableIdleTimeMillis: 60000
    # 配置一个连接在池中最大生存的时间，单位是毫秒
    maxEvictableIdleTimeMillis: 900000
    # 配置检测连接是否有效
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
  sso:
    secretKey: wcp-sso-by-andylu-uzhitingnizhenbangcereshuzhiti
    header: Authorization
    token-expire-time-web: 10080   #默认为60分钟
    isCache: false
#微信开发平台配置
wxopen:
  appid: wx65d365488cfe64db
  appsecret: 16bf662d110953bf51f96e5541a8d7e4
mybatis-plus:
  mapper-locations: mapper/*Mapper.xml
  type-aliases-package: com.wcp.domain
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
wcp:
  quartz-auto-start: false #定时器是否自动启动，默认为false
  project-path: E:\projectPath #用于项目导入导出
# warm-flow工作流配置
warm-flow:
  # 是否开启工作流，默认true
  enabled: true
  # 是否显示banner图，默认是
  banner: true
  # id生成器类型, 不填默认为orm扩展自带生成器或者warm-flow内置的19位雪花算法, SnowId14:14位，SnowId15:15位， SnowFlake19：19位
  key_type: SnowId19
  # 内部已实现自动获取，失效时使用此配置（在使用mybatis扩展包时, 由于各数据库sql语句存在差异, 通过此配置兼容，默认为mysql）
  data_source_type: mysql

