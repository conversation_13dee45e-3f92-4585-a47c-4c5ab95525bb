<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcp.mapper.PlatformUserMapper">

    <resultMap type="com.wcp.domain.PlmUser" id="UserInfoResult">
        <id     property="id"      column="ID"      />
        <result property="userName"    column="NAME"    />

    </resultMap>

    <sql id="selectUserVo" >
       select ID,NAME from WCP_GA_USER
    </sql>

    <select id="selectUserAll" parameterType="com.wcp.domain.PlmUser" resultMap="UserInfoResult">
        <include refid="selectUserVo"/>
        <where>
            <if test="userName != null  and userName != ''">and NAME like  '%${userName}%'</if>
        </where>
    </select>
</mapper>