package com.wcp.enums;

/**
 * 1. @Description 编辑权限枚举
 * 2. <AUTHOR>
 * 3. @Date 2025/3/13 10:27
 */
public enum UserRoleEnum {
    ADMIN(0, "admin"), //管理员
    MEMBER(1, "member"), //普通成员
    CREATE(2, "create"); //创建人

    private final int code;
    private final String name;

    UserRoleEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据 code 获取对应的枚举值
     */
    public static UserRoleEnum fromCode(int code) {
        for (UserRoleEnum type : UserRoleEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的 code: " + code);
    }

    public static UserRoleEnum fromName(String name) {
        for (UserRoleEnum type : UserRoleEnum.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的 code: " + name);
    }
}
