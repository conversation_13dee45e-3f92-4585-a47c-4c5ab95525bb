package com.wcp.enums;

/**
 * 1. @Description 编辑权限枚举
 * 2. <AUTHOR>
 * 3. @Date 2025/3/13 10:27
 */
public enum PermissionEnum {
    VIEW(0, "view"),
    EDIT(1, "edit"),
    PRIVATE(2,"private");//私有这个值作用在可以查看当前节点，不能拥有下级节点的操作权限，也没有查看下级所有子节点的权限

    private final int code;
    private final String name;

    PermissionEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据 code 获取对应的枚举值
     */
    public static PermissionEnum fromCode(int code) {
        for (PermissionEnum type : PermissionEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的 code: " + code);
    }

    public static PermissionEnum fromName(String name) {
        for (PermissionEnum type : PermissionEnum.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的 name: " + name);
    }
}
