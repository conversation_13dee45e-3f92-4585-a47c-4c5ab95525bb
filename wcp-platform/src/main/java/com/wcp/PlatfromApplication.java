package com.wcp;
import com.github.xiaoymin.swaggerbootstrapui.annotations.EnableSwaggerBootstrapUI;
import com.wcp.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * @Description 程序启动入口
 * <AUTHOR>
 * @Date 2023/6/21 下午 5:30
 * @Version 3.0
 */

@Slf4j
@MapperScan("com.wcp.mapper")
@ComponentScan(basePackages={"com.wcp.*"})
@SpringBootApplication
@EnableSwagger2//开启swagger功能
@EnableSwaggerBootstrapUI//swagger界面
public class PlatfromApplication {
    public static void main(String[] args) {
       // SpringApplication.run(WcpApplication.class, args);
        ApplicationContext context = SpringApplication.run(PlatfromApplication.class, args);

        Environment environment = context.getEnvironment();
        int port = environment.getProperty("server.port", Integer.class);
        log.info("接口文档地址: http://" + IpUtil.getLocalIPList().get(0) + ":"+ port +"/wcp-platform/doc.html");
        log.info("接口文档地址: http://" + IpUtil.getLocalIPList().get(1) + ":"+ port +"/wcp-platform/doc.html");
        // 获取所有带有@Controller注解的bean
        /*String[] controllerBeans = context.getBeanNamesForAnnotation(org.springframework.stereotype.Controller.class);

        // 打印扫描到的Controller类
        System.out.println("Scanned Controllers:");
        for (String controllerBean : controllerBeans) {
            System.out.println(controllerBean);
        }*/

    }
}
