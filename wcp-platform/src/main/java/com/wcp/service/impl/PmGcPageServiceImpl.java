package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.domain.PmGcNode;
import com.wcp.domain.PmGcPage;
import com.wcp.domain.PmTeamProject;
import com.wcp.execption.ServiceException;
import com.wcp.mapper.PmGcNodeMapper;
import com.wcp.mapper.PmGcPageMapper;
import com.wcp.mapper.PmTeamProjectMapper;
import com.wcp.service.PmGcPageService;
import com.wcp.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 1. @Description 图元路由
 * 2. <AUTHOR>
 * 3. @Date 2025/2/12 17:22
 */
@Service
public class PmGcPageServiceImpl extends ServiceImpl<PmGcPageMapper,PmGcPage> implements PmGcPageService {

    @Autowired
    private PmGcNodeMapper pmGcNodeMapper;

    @Autowired
    private PmTeamProjectMapper pmTeamProjectMapper;
    /**
     * 图元路由发布
     * @param pmGcPage
     */
    @Transactional
    public int publishGraphPage(PmGcPage pmGcPage){
        LambdaQueryWrapper<PmGcPage> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(PmGcPage::getRouter,pmGcPage.getRouter());
        queryWrapper.eq(PmGcPage::getProjectId,pmGcPage.getProjectId());
        List<PmGcPage> pmGcPageList=this.baseMapper.selectList(queryWrapper);
        if(pmGcPageList!=null && pmGcPageList.size()>0 && !pmGcPageList.get(0).getId().equals(pmGcPage.getId())){
            throw new ServiceException("该路由地址已被占用,发布失败");
        }
        //删除一次图元，清除就图元信息
        LambdaQueryWrapper<PmGcPage> deleteWrapper=new LambdaQueryWrapper<>();
        deleteWrapper.eq(PmGcPage::getId, pmGcPage.getId())
                .eq(PmGcPage::getProjectId, pmGcPage.getProjectId());
        this.baseMapper.delete(deleteWrapper);
        pmGcPage.setCreateTime(new Date());
        //修改图元状态为已发布
      /*  PmGcNode pmGcNode=new PmGcNode();
        pmGcNode.setPid(pmGcPage.getId());//图元id
        pmGcNode.setProjectId(pmGcPage.getProjectId());
        pmGcNode.setPubStatus(1);//修改为已发布*/
        LambdaUpdateWrapper<PmGcNode> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PmGcNode::getPid, pmGcPage.getId())
                .eq(PmGcNode::getProjectId, pmGcPage.getProjectId())
                .set(PmGcNode::getPubStatus, 1);
        int result=pmGcNodeMapper.update(null,updateWrapper);
        if(result>0){
            int v=this.baseMapper.insert(pmGcPage);
            if(v>0 && pmGcPage.getIsHomeRouter()){
                QueryWrapper<PmTeamProject>  wrapper=new QueryWrapper<>();
                wrapper.eq("PROJECT_ID", pmGcPage.getProjectId());
                PmTeamProject pmTeamProject=pmTeamProjectMapper.selectOne(wrapper);
                pmTeamProject.setHomeRouter(pmGcPage.getRouter());
                return  pmTeamProjectMapper.updateById(pmTeamProject);
            }else{
                return v;
            }
        }
        return 0;
    }

    /**
     * 删除图元路由
     * @param id
     * @return
     */
    @Override
    public int deleteRoute(String projectId,String id) {
        //删除一次图元，清除就图元信息
        LambdaQueryWrapper<PmGcPage> deleteWrapper=new LambdaQueryWrapper<>();
        deleteWrapper.eq(PmGcPage::getId, id)
                .eq(PmGcPage::getProjectId, projectId);
        // 修改图元节点状态为“未发布”
        UpdateWrapper<PmGcNode> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("PID", id).eq("PROJECT_ID", projectId);
        //修改图元状态为已发布
        PmGcNode pmGcNode=new PmGcNode();
        pmGcNode.setPid(id);//图元id
        pmGcNode.setProjectId(projectId);
        pmGcNode.setPubStatus(0);//修改为未发布
        int result=pmGcNodeMapper.update(pmGcNode,updateWrapper);
        return result>0?this.baseMapper.delete(deleteWrapper):0;
    }

    /**
     * 获取图元路由信息
     * @param projectId
     * @param pmGcPage
     * @return
     */
    @Override
    public PmGcPage getRouteInfo(String projectId, PmGcPage pmGcPage) {
        LambdaQueryWrapper<PmGcPage> wrapper=new LambdaQueryWrapper<>();
       /* wrapper.eq(PmGcPage::getId, pmGcPage.getId())
                .or()
                .eq(PmGcPage::getRouter, pmGcPage.getRouter())
                .eq(PmGcPage::getProjectId, projectId);*/
        wrapper.nested(w -> w.eq(PmGcPage::getId, pmGcPage.getId())
                        .or()
                        .eq(PmGcPage::getRouter, pmGcPage.getRouter()))
                .eq(PmGcPage::getProjectId, projectId);
        PmTeamProject pmTeamProject=pmTeamProjectMapper.selectById(projectId);
        PmGcPage page= baseMapper.selectOne(wrapper);
        if (page != null) {
            if(pmTeamProject!=null && StringUtil.isNotEmpty(pmTeamProject.getHomeRouter())){
                page.setIsHomeRouter(pmTeamProject.getHomeRouter().equals(page.getRouter())?true:false);
            }else{
                page.setIsHomeRouter(false);
            }
        }

        return page;

    }

}
