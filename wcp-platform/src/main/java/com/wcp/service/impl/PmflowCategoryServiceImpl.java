package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.domain.PmflowCategory;
import com.wcp.execption.ServiceException;
import com.wcp.mapper.PmflowCategoryMapper;
import com.wcp.service.PmflowCategoryService;
import com.wcp.thread.WcpThreadLocal;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/5/19 13:49
 */
@Service
public class PmflowCategoryServiceImpl extends ServiceImpl<PmflowCategoryMapper, PmflowCategory> implements PmflowCategoryService {
    @Override
    public int add(PmflowCategory category) {
        boolean exists = this.baseMapper.exists(new QueryWrapper<PmflowCategory>()
                .eq("CATEGORY_CODE", category.getCategoryCode())
                .eq("PROJECT_ID", category.getProjectId()));
        if (exists) {
            throw new ServiceException("分类编码已存在，不能重复添加！");
        }
        return this.baseMapper.insert(category);
    }

    @Override
    public int update(PmflowCategory category) {
        boolean exists = this.baseMapper.exists(new QueryWrapper<PmflowCategory>()
                .eq("CATEGORY_CODE", category.getCategoryCode())
                .eq("PROJECT_ID", category.getProjectId())
                .ne("CATEGORY_ID", category.getCategoryId()));
        if (exists) {
            throw new ServiceException("分类编码已存在，不能重复！");
        }

       return this.baseMapper.update(category, new UpdateWrapper<PmflowCategory>()
                .eq("CATEGORY_ID", category.getCategoryId())
                .eq("PROJECT_ID", category.getProjectId()));
    }

    @Override
    public int delete(String categoryId,String projectId) {
        int deleted = this.baseMapper.delete(new QueryWrapper<PmflowCategory>()
                .eq("CATEGORY_ID", categoryId)
                .eq("PROJECT_ID", projectId));
        if (deleted == 0) {
            throw new ServiceException("删除失败，分类可能不存在！");
        }
        return deleted;
    }

    @Override
    public List<PmflowCategory> list(String projectId) {
        return this.baseMapper.selectList(new QueryWrapper<PmflowCategory>()
                .eq("PROJECT_ID", projectId)
                .orderByAsc("SORT_INDEX"));
    }
}
