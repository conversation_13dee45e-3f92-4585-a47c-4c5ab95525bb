package com.wcp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.text.PmIdUtils;
import com.wcp.domain.PlmUser;
import com.wcp.domain.PmSpProjectFavorite;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;
import com.wcp.enums.PermissionEnum;
import com.wcp.enums.UserRoleEnum;
import com.wcp.mapper.PmSpProjectFavoriteMapper;
import com.wcp.minio.MinioService;
import com.wcp.service.PmSpProjectFavoriteService;
import com.wcp.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 1. @Description 收藏业务类
 * 2. <AUTHOR>
 * 3. @Date 2025/3/24 10:10
 */
@Service
public class PmSpProjectFavoriteServiceImpl extends ServiceImpl<PmSpProjectFavoriteMapper, PmSpProjectFavorite> implements PmSpProjectFavoriteService {
    /**
     * 项目收藏
     * @param favorite
     * @return
     */
    public boolean add(PmSpProjectFavorite favorite) {
        String userId=SecurityUtils.getUserId();
        favorite.setId(PmIdUtils.fastSimpleUUID());
        favorite.setUserId(userId);
        favorite.setCreatedTime(new Date());
        return this.save(favorite);
    }

    /**
     * 取消项目收藏
     * @param favorite
     * @return
     */
    public int delete(PmSpProjectFavorite favorite) {
        return this.baseMapper.deleteById(favorite);
    }

    /**
     * 项目收藏
     * @param
     * @return
     */
    public List<PmSpTeamGroupProjectDto> queryFavoriteProject(String orderBy) {
        String userId=SecurityUtils.getUserId();
        PlmUser plmUser =SecurityUtils.getLoginUser().getUser();
        String sortOrder = "DESC";
        if ("asc".equalsIgnoreCase(orderBy)) {
            sortOrder = "ASC";
        }
        List<PmSpTeamGroupProjectDto> list=baseMapper.queryOpenProject(userId,sortOrder);
        if(list!=null){
            list.forEach(t->{
                try {
                    t.setImgUrl(StringUtil.isNotEmpty(t.getImgUrl())? MinioService.getPresignedObjectUrl(t.getImgUrl()):null);
                    t.setPermission(Integer.parseInt(t.getPermission())== PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                    t.setUserRole(t.getCreateUser().equals(plmUser.getAccount())? UserRoleEnum.CREATE.getName() :  UserRoleEnum.fromCode(Integer.parseInt(t.getUserRole())).getName());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return list;
    }
}
