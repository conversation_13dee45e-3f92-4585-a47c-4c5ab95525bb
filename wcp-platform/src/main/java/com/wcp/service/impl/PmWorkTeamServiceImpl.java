package com.wcp.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.http.PmResult;
import com.wcp.core.text.PmIdUtils;
import com.wcp.domain.*;
import com.wcp.domain.dto.*;
import com.wcp.service.*;
import com.wcp.domain.vo.PmTeamUserVo;
import com.wcp.enums.PermissionEnum;
import com.wcp.enums.UserRoleEnum;
import com.wcp.mapper.*;
import com.wcp.minio.MinioService;
import com.wcp.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class PmWorkTeamServiceImpl extends ServiceImpl<PmWorkTeamMapper, PmWorkTeam> implements PmWorkTeamService {

    @Autowired
    private PmWorkTeamMapper workTeamMapper;

    @Autowired
    private PmTeamProjectMapper pmTeamProjectMapper;

    @Autowired
    private PmTeamUserMapper pmTeamUserMapper;

    @Autowired
    private PmWorkSpaceUserMapper pmWorkSpaceUserMapper;

    @Autowired
    private PmSpTeamTreeMapper pmSpTeamTreeMapper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    /**
     * 获取控件下面的团队
     * @param spaceId
     * @return
     * @throws PmServiceException
     */
    @Override
    public List<PmWorkTeamDto> queryWorkTeamById(String spaceId) throws PmServiceException{
        if (StringUtil.isEmpty(spaceId)) {
            throw new PmServiceException("用户ID不能为空！请重新输入。");
        }
        String loginUserId = SecurityUtils.getUserId();
        //先判断当前用户是否在空间下,如果在空间下就可以看到空间下的所有团队，除了团队是私有的之外
        LambdaQueryWrapper<PmWorkSpaceUser> wrapper = Wrappers.<PmWorkSpaceUser>lambdaQuery()
                .eq(PmWorkSpaceUser::getSpaceId,spaceId)
                .eq(PmWorkSpaceUser::getUserId, loginUserId);
        PmWorkSpaceUser pmWorkSpaceUser=pmWorkSpaceUserMapper.selectOne(wrapper);
        List<PmWorkTeamDto> workTeamList=new ArrayList<>();
        //在空间里面，所有团队都可见
        if(pmWorkSpaceUser!=null && pmWorkSpaceUser.getPermission()!=PermissionEnum.PRIVATE.getCode()){
            workTeamList=workTeamMapper.queryWorkTeamById(spaceId,loginUserId,null);
            if (workTeamList != null && workTeamList.size() > 0) {
                workTeamList = workTeamList.stream()
                        .filter(t -> {
                            // 如果团队是公开的，所有用户都可以看到
                            if (t.getIsPublic() == 1) {
                                return true;
                            } else {
                                // 如果不是公开的，当前登录人在团队成员里面也是可见的
                                return t.getUserId()==loginUserId?true:false;
                            }
                        })
                        .collect(Collectors.toList());
            }

        }/*else if(pmWorkSpaceUser!=null && pmWorkSpaceUser.getPermission()==PermissionEnum.PRIVATE.getCode()){
            //这种只能看加入
            List<Integer> permission = new ArrayList<>(Arrays.asList(2));
            workTeamList=workTeamMapper.queryWorkTeamById(spaceId,loginUserId, permission);
        }*/
        else{
           // List<Integer> permission = new ArrayList<>(Arrays.asList(0,1));

            //不在空间里就要查询哪些团队是当前用户加入了的 可以查看view和edit
            workTeamList=workTeamMapper.queryWorkTeamById(spaceId,loginUserId,null);
        }
        workTeamList.forEach(t->{
            try {
                t.setImgUrl(StringUtil.isNotEmpty(t.getImgUrl())?MinioService.getPresignedObjectUrl(t.getImgUrl()):null);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        // 返回过滤后的团队列表
        return workTeamList;
    }

    @Override
    public List<PmWorkTeamDto> queryWorkTeamById() throws PmServiceException{
        String loginUserId = SecurityUtils.getUserId();
        List<PmWorkTeamDto> workTeamList=workTeamMapper.queryWorkTeamById(null,loginUserId,null);
        if(workTeamList!=null && workTeamList.size()>0){
            workTeamList.forEach(t->{
                try {
                    LambdaQueryWrapper<PmSpTeamTree> wrapper = Wrappers.<PmSpTeamTree>lambdaQuery()
                            .eq(PmSpTeamTree::getTeamId,t.getTeamId());
                    List<PmSpTeamTree> pmSpTeamTreeList=pmSpTeamTreeMapper.selectList(wrapper);
                    t.setChildren(pmSpTeamTreeList);
                    t.setImgUrl(StringUtil.isNotEmpty(t.getImgUrl())?MinioService.getPresignedObjectUrl(t.getImgUrl()):null);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return workTeamList;
    }
    /**
     * 创建团队
     * @param team
     * @return
     */
    @Transactional
    public Boolean createWorkTeam(MultipartFile file, PmWorkTeam team) {
        if (StringUtil.isEmpty(team.getTeamName())) {
            throw new PmServiceException("团队名称不能为空！请重新输入。");
        }
       /* if (StringUtil.isEmpty(team.getSpaceId())) {
            throw new PmServiceException("空间ID不能为空！请重新输入。");
        }*/
        if (file != null && !file.isEmpty()) {
            String originalFilename = file.getOriginalFilename();
            String url="ebi/platform/"+originalFilename;
            MinioService.uploadFile(url,file);
            team.setImgUrl(url);
        }
        String teamId = PmIdUtils.fastSimpleUUID();
        String userId = SecurityUtils.getLoginUser().getUser().getUserId();
        team.setTeamId(teamId);
        team.setCreateUser(userId);
        team.setCreateTime(new Date());
        team.setIsDelete(0);//默认正常
        int index=workTeamMapper.insert(team);
        if(index>0){
            PmTeamUser pmTeamUser=new PmTeamUser();
            pmTeamUser.setId(PmIdUtils.fastSimpleUUID());
            pmTeamUser.setTeamId(teamId);//团队
            pmTeamUser.setUserId(userId);
            pmTeamUser.setUserRole(UserRoleEnum.CREATE.getCode());//管理员角色
            pmTeamUser.setPermission(PermissionEnum.EDIT.getCode());//拥有编辑权限
            pmTeamUser.setCreateUser(userId);
            pmTeamUser.setCreateTime(new Date());
            return pmTeamUserMapper.insert(pmTeamUser)>0;
        }
        return false;
    }

    /**
     * 修改团队
     * @param team
     * @return
     */
    public Boolean updateWorkTeam(PmWorkTeam team){
        try{
            String userId = SecurityUtils.getLoginUser().getUser().getUserId();
            team.setUpdateUser(userId);
            team.setUpdateTime(new Date());
            return workTeamMapper.updateById(team)>0;
        }catch (Exception e){
            throw new PmServiceException("修改失败");
        }
    }

    /**
     * 查询用户信息
     * @param teamId
     * @return
     */
    public PmWorkTeamDto queryDetail(String teamId){
            PmWorkTeamDto pmWorkTeamDto=new PmWorkTeamDto();
        try{
            PmWorkTeam pmWorkTeam=workTeamMapper.selectById(teamId);
            if(pmWorkTeam!=null){
                pmWorkTeamDto.setTeamId(pmWorkTeam.getTeamId());
                pmWorkTeamDto.setTeamName(pmWorkTeam.getTeamName());
                pmWorkTeamDto.setDescr(pmWorkTeam.getDescr());
            }
            if(StringUtil.isNotEmpty(pmWorkTeam.getImgUrl())){
                pmWorkTeamDto.setImgUrl(MinioService.getPresignedObjectUrl(pmWorkTeam.getImgUrl()));
            }
            //获取登录人
            String loginUserId = SecurityUtils.getUserId();
            PlmUser plmUser=platformUserMapper.selectById(pmWorkTeam.getCreateUser());
            pmWorkTeamDto.setCreateUser(plmUser.getUserName());
            if(plmUser!=null){
                pmWorkTeamDto.setIsCreator(plmUser.getUserId().equals(loginUserId)?true:false);
            }
            return pmWorkTeamDto;
        }catch (Exception e){
            throw new PmServiceException("查询失败:"+e.getMessage());
        }
    }

    @Override
    @Transactional
    public Boolean deleteWorkTeam(String teamId) {
        if (StringUtil.isEmpty(teamId)) {
            throw new PmServiceException("团队ID不能为空！请重新输入。");
        }
        String loginUserId = SecurityUtils.getUserId();
        UpdateWrapper<PmWorkTeam> wrapper = new UpdateWrapper<>();
        wrapper.eq("TEAM_ID",teamId).set("IS_DELETE",1);//将团队逻辑删除
        Boolean result=workTeamMapper.update(null,wrapper)>0;
        if(result){
            LambdaQueryWrapper<PmTeamProject> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(PmTeamProject::getTeamId,teamId);
            List<PmTeamProject> teamProjectList=pmTeamProjectMapper.selectList(queryWrapper);
            if(teamProjectList!=null && teamProjectList.size()>0){
                //解散后团队下的项目也标记成为删除
                UpdateWrapper<PmTeamProject> projectWrapper = new UpdateWrapper<>();
                projectWrapper.eq("TEAM_ID",teamId).set("IS_DELETE",1);//将团队逻辑删除
                Boolean result2=pmTeamProjectMapper.update(null,projectWrapper)>0;
                return result2;
            }
            return true;
        }
        return false;

    }

    /**
     * 团队成员加入
     * @param teamUsers
     * @return
     */
    @Transactional
    public Boolean  addTeamUser(List<PmTeamUserVo> teamUsers){
        String loginUserId = SecurityUtils.getUserId();
        teamUsers.forEach(t->{
            LambdaQueryWrapper<PmTeamUser> wrapper = Wrappers.<PmTeamUser>lambdaQuery()
                    .eq(PmTeamUser::getTeamId, t.getTeamId())
                    .eq(PmTeamUser::getUserId, t.getUserId());
            PmTeamUser teamUser=pmTeamUserMapper.selectOne(wrapper);
            if(teamUser==null){
                PmTeamUser user=new PmTeamUser();
                user.setId(PmIdUtils.fastSimpleUUID());
                user.setTeamId(t.getTeamId());
                user.setUserId(t.getUserId());
                user.setUserRole(UserRoleEnum.MEMBER.getCode());//默认加入的是成员角色
                user.setPermission(t.getPermission());
                user.setCreateUser(loginUserId);
                user.setCreateTime(new Date());
                pmTeamUserMapper.insert(user);
            }else{
                teamUser.setPermission(t.getPermission());
                pmTeamUserMapper.updateById(teamUser);
            }
        });
        /*teamUsers.forEach(t->{
            LambdaQueryWrapper<PmWorkSpaceUser> wrapper = Wrappers.<PmWorkSpaceUser>lambdaQuery()
                    .eq(PmWorkSpaceUser::getSpaceId, t.getSpaceId())
                    .eq(PmWorkSpaceUser::getUserId, t.getUserId());
            PmWorkSpaceUser workSpaceUser=pmWorkSpaceUserMapper.selectOne(wrapper);
            //如果不在团队中就创建一个private私有的
            if(workSpaceUser==null){
                PmWorkSpaceUser pmWorkSpaceUser=new PmWorkSpaceUser();
                pmWorkSpaceUser.setId(PmIdUtils.fastSimpleUUID());
                pmWorkSpaceUser.setSpaceId(t.getSpaceId());//空间ID
                pmWorkSpaceUser.setUserId(t.getUserId());
                pmWorkSpaceUser.setUserRole(UserRoleEnum.MEMBER.getCode());//管理员角色
                pmWorkSpaceUser.setPermission(PermissionEnum.PRIVATE.getCode());//拥有编辑权限
                pmWorkSpaceUser.setCreateUser(loginUserId);
                pmWorkSpaceUser.setCreateTime(new Date());
                pmWorkSpaceUserMapper.insert(pmWorkSpaceUser);
            }
        });*/
        return true;
    }

    /**
     * 用户搜索
     * @param content
     * @return
     */
    public List<PlmUserDto> searchuser(String temId, String content){
        List<PlmUserDto> dtoList=null;
        String phoneRegex = "^1[3-9]\\d{9}$";
        Pattern pattern = Pattern.compile(phoneRegex);
        Matcher matcher = pattern.matcher(content);
        String emailRegex = "^[a-zA-Z0-9_.%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$";
        Pattern pattern2 = Pattern.compile(emailRegex);
        Matcher matcher2 = pattern2.matcher(content);
        if(StringUtil.isEmpty(content)){
            content=" ";
        }
        String type="user";
        if (matcher.matches()) {
            dtoList=pmTeamUserMapper.selectTeamByUser(temId,null,content,null);
            type="phone";
        }else if (matcher2.matches()) {
            dtoList=pmTeamUserMapper.selectTeamByUser(temId,null,null,content);
            type="email";
        }else{
            String account="%" + content + "%";
            dtoList=pmTeamUserMapper.selectTeamByUser(temId,account,null,null);
        }
        if(dtoList!=null && dtoList.size()>0){
            for(PlmUserDto t:dtoList){
                t.setType(type);//默认表示使用账号搜索
                if(StringUtil.isNotEmpty(t.getPermission())){
                    t.setIsInTeam(true);//是否在团队中
                }else{
                    t.setIsInTeam(false);
                }
            }
        }
        return dtoList;
    }

    /**
     * 查看团队中的成员
     * @param teamId
     * @return
     * @throws PmServiceException
     */
    public List<TeamUserDto> queryTeamUserById(String teamId) throws PmServiceException {
        if (StringUtil.isEmpty(teamId)) {
            throw new PmServiceException("团队ID不能为空！请重新输入。");
        }
        List<TeamUserDto> projectUserDtoList=pmTeamUserMapper.queryWorkTeamByUser(teamId);
        if(projectUserDtoList!=null && projectUserDtoList.size()>0){
            projectUserDtoList.forEach(t->{
                t.setUserRole(UserRoleEnum.fromCode(Integer.parseInt(t.getUserRole())).getName());
                t.setPermission(PermissionEnum.fromCode(Integer.parseInt(t.getPermission())).getName());
            });
        }
        return projectUserDtoList;
    }

    /**
     * 根据团队ID按照分组查看项目
     * @param teamId
     */
    public List<Map<String, Object>> queryTeamProjectByGroup(String teamId){
        String userId=SecurityUtils.getUserId();
        List<PmSpTeamGroupProjectDto> dataList=pmSpTeamTreeMapper.queryTeamPtojectByGroup(teamId);
        LambdaQueryWrapper<PmTeamUser> wrapper = Wrappers.<PmTeamUser>lambdaQuery()
                .eq(PmTeamUser::getTeamId, teamId)
                .eq(PmTeamUser::getUserId, userId);
        PmTeamUser pmTeamUser=pmTeamUserMapper.selectOne(wrapper);
        // 转换为目标格式
        List<Map<String, Object>> result = new ArrayList<>();
        if(dataList!=null && dataList.size()>0){
            // 按照 groupName 和 id 分组
            Map<String, Map<String, List<PmSpTeamGroupProjectDto>>> groupedData = dataList.stream()
                    .collect(Collectors.groupingBy(
                            PmSpTeamGroupProjectDto::getGroupName, // 第一级分组：groupName
                            Collectors.groupingBy(PmSpTeamGroupProjectDto::getId) // 第二级分组：id
                    ));
            groupedData.forEach((groupName, idMap) -> {
                idMap.forEach((id, projects) -> {
                    Map<String, Object> groupMap = new HashMap<>();
                    groupMap.put("id", id);
                    groupMap.put("groupName", groupName);
                    // 只保留 projectName 和 projectId
                    List<PmSpTeamGroupProjectDto> simplifiedProjectList = projects.stream()
                            .filter(project -> {
                                // 如果项目不公开，判断当前用户是否是创建人
                                if (StringUtil.isNotEmpty(project.getCreateUser()) && project.getIsPublic() == 0) {
                                    return project.getCreateUser().equals(userId); // 只有创建人可以访问
                                }
                                return true; // 公开项目，所有人都可以访问
                            })
                            .filter(project -> project.getProjectId() != null)
                            .map(project -> {

                                /*Map<String, String> projectMap = new HashMap<>();
                                projectMap.put("projectId", project.getProjectId());
                                projectMap.put("projectName", project.getProjectName());
                                projectMap.put("userRole", UserRoleEnum.fromCode(pmTeamUser.getUserRole()).getName());
                                projectMap.put("permission", PermissionEnum.fromCode(pmTeamUser.getPermission()).getName());

                                return projectMap;*/
                                try {
                                    project.setUserRole(UserRoleEnum.fromCode(pmTeamUser.getUserRole()).getName());
                                    project.setPermission(PermissionEnum.fromCode(pmTeamUser.getPermission()).getName());
                                    project.setImgUrl(StringUtil.isNotEmpty(project.getImgUrl())?MinioService.getPresignedObjectUrl(project.getImgUrl()):null);
                                    project.setTeamId(teamId);
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                return project;
                            })
                            .collect(Collectors.toList());
                    groupMap.put("count", simplifiedProjectList.size());
                    // 如果项目数量超过 7 条，则只返回前 7 条数据
                    if (simplifiedProjectList.size() > 7) {
                        groupMap.put("projectList", simplifiedProjectList.subList(0, 7));
                    } else {
                        groupMap.put("projectList", simplifiedProjectList);
                    }
                    result.add(groupMap);
                });
            });
        }
        return result;
    }

    /**
     * 查询回收站项目
     * @param teamId
     * @return
     */
    public List<PmSpTeamRecycleProjectDto> queryTeamRecycle(String teamId){
        String userId=SecurityUtils.getUserId();
        List<PmSpTeamRecycleProjectDto>  list=baseMapper.queryTeamRecycle(userId,teamId);
        if(list!=null && list.size()>0){
            list.forEach(t->{
                try {
                    t.setImgUrl(StringUtil.isNotEmpty(t.getImgUrl())?MinioService.getPresignedObjectUrl(t.getImgUrl()):null);
                    t.setUserRole(UserRoleEnum.fromCode(Integer.parseInt(t.getUserRole())).getName());
                    t.setPermission(PermissionEnum.fromCode(Integer.parseInt(t.getPermission())).getName());
                    t.setSurplusDate(30);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return list;
    }

    /**
     * 恢复团队下的回收站
     * @param teamId
     * @return
     */
    public int recycleRestore(String teamId,String projectId){
        PmTeamProject pmTeamProject=pmTeamProjectMapper.selectById(projectId);
        if(pmTeamProject!=null){
            pmTeamProject.setUpdateTime(new Date());
            pmTeamProject.setIsDelete(0);//还原
            pmTeamProject.setDeleteTime(null);
            return pmTeamProjectMapper.updateById(pmTeamProject);
        }else {
            return 0;
        }

    }

    /**
     * 清空回收站
     * @param teamId
     * @param projectId
     * @return
     */
    public int recycleClear(String teamId,String projectId){
        QueryWrapper<PmTeamProject> wrapper=new QueryWrapper<PmTeamProject>();
        wrapper.eq("TEAM_ID",teamId);
        return pmTeamProjectMapper.delete(wrapper);
    }

    /**
     *修改权限
     * @return
     */
    public int updatePermission(TeamUserDto teamUserDto){
        //退出团队
        if("exitTeam".equals(teamUserDto.getUserRole())){
            return pmTeamUserMapper.deleteById(teamUserDto.getId());
        }else{
        //修改权限
            PmTeamUser pmTeamUser=new PmTeamUser();
            pmTeamUser.setPermission(PermissionEnum.fromName(teamUserDto.getPermission()).getCode());
            pmTeamUser.setId(teamUserDto.getId());
            pmTeamUser.setUserRole(UserRoleEnum.fromName(teamUserDto.getUserRole()).getCode());
            return pmTeamUserMapper.updateById(pmTeamUser);
        }
    }

    /**
     * 修改项目图标
     * @param profileImage
     * @param teamId
     * @return
     */
    public PmResult uploadTeamProfile(MultipartFile profileImage, String teamId) {
        // 查询头像是否存在
        String image = workTeamMapper.selectById(teamId).getImgUrl();

        // 构建新头像完整路径
        String originalFilename = profileImage.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String newFileName  = "ebi/platform/" + StringUtil.generateUUID() + extension;
        try {
            // 删除原有头像（如果存在）
            if (image != null && !image.isEmpty()) {
                String fullOldPath = "ebi/platform/" + image;
                MinioService.removeFile(fullOldPath);
            }
            // 上传新头像
            MinioService.uploadFile(newFileName,profileImage);


            // 更新数据库的头像路径
            UpdateWrapper<PmWorkTeam> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("TEAM_ID", teamId);
            updateWrapper.set("IMG_URL", newFileName);
            // 执行更新（只更新设置的字段）
            int result = workTeamMapper.update(null, updateWrapper);
            if (result == 0) {
                return PmResult.error("图标修改失败");
            }
            return PmResult.success("图标更新成功");

        } catch (Exception e) {
            return PmResult.error(e.getMessage());
        }
    }

    /**
     * 团队移交
     */
    @Transactional
    public Boolean teamTransfer(JSONObject jsonObject){
        //当前登录人
        String toUserId=jsonObject.getString("toUserId");
        String teamId=jsonObject.getString("teamId");
        String userId=SecurityUtils.getUserId();
        PmWorkTeam pmWorkTeam=workTeamMapper.selectById(teamId);
        if(pmWorkTeam!=null){
            //原来旧的创建这
            String oldCreateUserId=pmWorkTeam.getCreateUser();
            pmWorkTeam.setCreateUser(toUserId);//移交给toUserId
            pmWorkTeam.setUpdateTime(new Date());
            pmWorkTeam.setUpdateUser(userId);//当前操作人
            workTeamMapper.updateById(pmWorkTeam);//更新

            //需要将原来的创建人权限更新成普通用户
             //判断当前登录人是否是当前团队的创建人
            LambdaUpdateWrapper<PmTeamUser> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(PmTeamUser::getTeamId, teamId)
                    .eq(PmTeamUser::getUserId, oldCreateUserId)
                    .set(PmTeamUser::getUserRole, UserRoleEnum.CREATE.getCode());
            PmTeamUser pmTeamUserOld=pmTeamUserMapper.selectOne(queryWrapper);
            //查询交接的用户信息
            LambdaUpdateWrapper<PmTeamUser> toQueryWrapper = new LambdaUpdateWrapper<>();
            toQueryWrapper.eq(PmTeamUser::getTeamId, teamId)
                    .eq(PmTeamUser::getUserId, toUserId);
            PmTeamUser toPmTeamUser=pmTeamUserMapper.selectOne(toQueryWrapper);

            if(pmTeamUserOld!=null && toPmTeamUser!=null){
                //将原来的更新成普通用户
                pmTeamUserOld.setUserRole(UserRoleEnum.MEMBER.getCode());
                int result1=pmTeamUserMapper.updateById(pmTeamUserOld);
                //将新的用户更新成管理员
                toPmTeamUser.setUserRole(UserRoleEnum.CREATE.getCode());
                int result2=pmTeamUserMapper.updateById(toPmTeamUser);
                return  result1>0&result2>0;
            }
            else{
                return false;
            }
        }
        return false;
    }
}
