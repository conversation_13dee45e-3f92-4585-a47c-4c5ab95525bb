package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wcp.domain.PmSpIcon;
import com.wcp.domain.PmSpIconGroup;
import com.wcp.mapper.PmIconGroupMapper;
import com.wcp.mapper.PmSpIconMapper;
import com.wcp.domain.PmSpIconService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 图标相关业务类
 * Author: qianchao
 * Date: 2024/3/5 11:05
 */
@Service
public class PmSpIconServiceImpl implements PmSpIconService {
    @Autowired
    private PmIconGroupMapper pmIconGroupMapper;
    @Autowired
    private PmSpIconMapper pmSpIconMapper;

    /**
     * 查询平台图标
     * @return
     */
    public List<PmSpIconGroup> queryIcons() {
        // 查询所有分组
        List<PmSpIconGroup> pmSpIconGroupList = pmIconGroupMapper.selectList(null);
        if (pmSpIconGroupList != null && !pmSpIconGroupList.isEmpty()) {
            // 查询出平台中所有图标
            List<PmSpIcon> pmSpIcons = pmSpIconMapper.selectList(null);
            // 按照 group_id 进行分组
            Map<String, List<PmSpIcon>> iconGroupMap = pmSpIcons.stream()
                    .collect(Collectors.groupingBy(PmSpIcon::getGroupId));

            // 将图标列表设置到对应的分组中，如果分组没有图标则设置为空集合
            pmSpIconGroupList.forEach(iconGroup ->
                    iconGroup.setIcons(iconGroupMap.getOrDefault(iconGroup.getGroupId(), Collections.emptyList())));
        }
        return pmSpIconGroupList;
    }

    /**
     * 查询图标分组
     * @return
     */
    public List<PmSpIconGroup> queryGroups(){
        //获取图标分组集合
        List<PmSpIconGroup> pmSpIconGroupList=pmIconGroupMapper.selectList(null);
        return pmSpIconGroupList;
    }

    /**
     * 上传平台图标信息
     * @param pmSpIcon
     * @return
     */
    public int saveIcon(PmSpIcon pmSpIcon){
        return pmSpIconMapper.insert(pmSpIcon);
    }

    /**
     * 保存分组
     * @param pmSpIconGroup
     * @return
     */
    public int saveGroup(PmSpIconGroup pmSpIconGroup){
        return pmIconGroupMapper.insert(pmSpIconGroup);
    }

    /**
     * 修改分组
     * @param pmSpIconGroup
     * @return
     */
    public int updateGroup(PmSpIconGroup pmSpIconGroup){
        return pmIconGroupMapper.updateById(pmSpIconGroup);
    }

    /**
     * 删除分组
     * @param groupId
     * @return
     */
    @Transactional
    public int deleteGroup(String  groupId){
        LambdaQueryWrapper<PmSpIconGroup> delwrapper=new LambdaQueryWrapper<>();
        delwrapper.eq(PmSpIconGroup::getGroupId,groupId);
        int index=pmIconGroupMapper.delete(delwrapper);
        LambdaQueryWrapper<PmSpIcon> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(PmSpIcon::getGroupId,groupId);
        int result=pmSpIconMapper.delete(wrapper);
        return index;
    }

    /**
     * 删除指定分组图标
     * @param groupId
     * @param groupName
     * @return
     */
    public int deleteIcon(String groupId,String groupName){
        LambdaQueryWrapper<PmSpIcon> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(PmSpIcon::getGroupId,groupId);
        wrapper.eq(PmSpIcon::getIconName,groupName);
        int result=pmSpIconMapper.delete(wrapper);
        return result;
    }
}
