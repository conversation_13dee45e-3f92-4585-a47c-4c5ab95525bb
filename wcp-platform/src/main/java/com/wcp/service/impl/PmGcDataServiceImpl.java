package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PmGcData;
import com.wcp.domain.PmGcNode;
import com.wcp.mapper.PmGcDataMapper;
import com.wcp.mapper.PmGcNodeMapper;
import com.wcp.service.PmGcDataService;
import com.wcp.utils.GzipCompress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * Description: 图元业务实现类
 * Author: qianchao
 * Date: 2024/3/13 16:03
 */
@Service
public class PmGcDataServiceImpl extends ServiceImpl<PmGcDataMapper,PmGcData> implements PmGcDataService {

    @Autowired
    PmGcNodeMapper pmGcNodeMapper;
    /**
     * 保存图元
     * @param pmGcData
     * @return
     */
    @Override
    public int saveOrUpdateGraph(PmGcData pmGcData) {
        String account= SecurityUtils.getLoginUser().getUser().getAccount();
        //更新图元节点的account
        LambdaQueryWrapper<PmGcNode> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(PmGcNode::getPid, pmGcData.getGraphId())
                .eq(PmGcNode::getProjectId, pmGcData.getProjectId());
        PmGcNode pmGcNode=pmGcNodeMapper.selectOne(queryWrapper);
        if(pmGcNode!=null){
            UpdateWrapper<PmGcNode> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("PID", pmGcData.getGraphId())
                    .eq("PROJECT_ID", pmGcData.getProjectId())
                    .set("ACCOUNT", account)
                    .set("UPDATE_TIME", new Date());
            pmGcNodeMapper.update(null, updateWrapper);

          /*  pmGcNode.setAccount(account);
            pmGcNode.setUpdateTIme(new Date());
            pmGcNodeMapper.updateById(pmGcNode);*/
        }
        //先做一次更新，如果更新返回了成功数量，表示这条图元已经存在
        pmGcData.setUpdateTIme(new Date());//更新时间
        if(PmStringUtils.isNotEmpty(pmGcData.getGraphData())){
            //图元内容进行压缩
            String graphData=pmGcData.graphData.toJSONString();
           String graphCell=GzipCompress.compress(graphData,"UTF-8");
            // 将字符串转换为字节数组，使用 UTF-8 编码
            byte[] bytes = graphCell.getBytes(StandardCharsets.UTF_8);
            pmGcData.setGraphCell(bytes);
           // int result=this.baseMapper.updateById(pmGcData);
            UpdateWrapper<PmGcData> updatePmgcDataWrapper = new UpdateWrapper<>();
            updatePmgcDataWrapper.eq("GRAPH_ID", pmGcData.getGraphId())
                    .eq("PROJECT_ID", pmGcData.getProjectId())
                    .set("GRAPH_CELL", bytes)
                    .set("UPDATE_TIME", new Date());
            //表示数据库不存在该条图元信息，需要执行insert
            int result=this.baseMapper.update(null, updatePmgcDataWrapper);
            if(result<=0){
                pmGcData.setCreateTime(new Date());
              return   this.baseMapper.insert(pmGcData);
            }else{
                return result;
            }
        }
        return 0;//默认返回0，代表保存失败
    }

    /**
     * 通过图元点号获取图元内容
     * @param graphId
     * @return
     */
    public Object getProejctGraph(String graphId,String rootId,String encoding,String projectid) throws IOException {
        //路由直接删除
        LambdaQueryWrapper<PmGcData> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper
                .and(wrapper -> wrapper
                        .eq(PmGcData::getGraphId, graphId)
                        .or()
                        .eq(PmGcData::getRootId, rootId)
                )
                .eq(PmGcData::getProjectId, projectid);
        PmGcData pmGcData=this.baseMapper.selectOne(queryWrapper);
        if(pmGcData!=null){
            byte[] graphCell=pmGcData.getGraphCell();
            String graphData = new String(graphCell, StandardCharsets.UTF_8);
            //如果encodng是gzip就解压，就返回压缩的，如果是json就是需要解压
           return  "gzip".equals(encoding)?graphData: GzipCompress.uncompress(graphData);
        }
        return null;
    }


}
