package com.wcp.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.text.PmIdUtils;
import com.wcp.domain.PmProjectUser;
import com.wcp.domain.PmTeamProject;
import com.wcp.domain.PmTeamUser;
import com.wcp.domain.PmWorkSpaceUser;
import com.wcp.domain.dto.PlmUserDto;
import com.wcp.domain.dto.PmTeamProjectSerachDto;
import com.wcp.domain.vo.PmProjectUserVo;
import com.wcp.enums.PermissionEnum;
import com.wcp.enums.UserRoleEnum;
import com.wcp.mapper.*;
import com.wcp.minio.MinioService;
import com.wcp.service.PmTeamProjectService;
import com.wcp.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.wcp.utils.IdGenerator;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class PmTeamProjectServiceImpl extends ServiceImpl<PmTeamProjectMapper, PmTeamProject> implements PmTeamProjectService{

    @Autowired
    private PmTeamProjectMapper teamProjectMapper;
    @Autowired
    private PmTeamProjectUserMapper pmTeamProjectUserMapper;
    @Autowired
    private PmWorkSpaceUserMapper pmWorkSpaceUserMapper;
    @Autowired
    private PmTeamUserMapper pmTeamUserMapper;

    @Override
    public List<PmTeamProject> queryTeamProjectById(String spaceId,String teamId) throws PmServiceException{
        if (StringUtil.isEmpty(spaceId)) {
            throw new PmServiceException("空间ID不能为空！请重新输入。");
        }
        if (StringUtil.isEmpty(teamId)) {
            throw new PmServiceException("团队ID不能为空！请重新输入。");
        }
        String loginUserId = SecurityUtils.getUserId();
        //先判断当前用户是否在空间下,如果在空间下就可以看到空间下的所有团队，除了团队是私有的之外
        LambdaQueryWrapper<PmWorkSpaceUser> wrapper = Wrappers.<PmWorkSpaceUser>lambdaQuery()
                .eq(PmWorkSpaceUser::getSpaceId,spaceId)
                .eq(PmWorkSpaceUser::getUserId, loginUserId);
        PmWorkSpaceUser pmWorkSpaceUser=pmWorkSpaceUserMapper.selectOne(wrapper);
        // 存储用户可以访问的项目
        List<PmTeamProject> accessibleProjects = new ArrayList<>();

        //如果当前用户在当前空间中并且权限不是私有的,就可以看到所有项目
        if(pmWorkSpaceUser!=null && pmWorkSpaceUser.getPermission()!=PermissionEnum.PRIVATE.getCode()){
            List<PmTeamProject> projects = teamProjectMapper.selectList(Wrappers.<PmTeamProject>lambdaQuery().eq(PmTeamProject::getTeamId, teamId).eq(PmTeamProject::getIsDelete,0).orderByDesc(PmTeamProject::getCreateTime));
            if(projects!=null && projects.size()>0){
                projects.forEach(t->{
                    t.setPermission(pmWorkSpaceUser.getPermission()==PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                    t.setUserRole(pmWorkSpaceUser.getUserRole()==UserRoleEnum.ADMIN.getCode()?UserRoleEnum.ADMIN.getName() :UserRoleEnum.MEMBER.getName() );
                    if(StringUtil.isNotEmpty(t.getImgUrl())){
                        try {
                            t.setImgUrl(MinioService.getPresignedObjectUrl(t.getImgUrl()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });


            }
            return projects;
        }
        // 2. 用户不在空间中，检查是否在 teamId 对应的团队中
        LambdaQueryWrapper<PmTeamUser> teamWrapper = Wrappers.<PmTeamUser>lambdaQuery()
                .eq(PmTeamUser::getTeamId, teamId)
                .eq(PmTeamUser::getUserId, loginUserId);
        PmTeamUser pmTeamUser = pmTeamUserMapper.selectOne(teamWrapper);
        // 在团队中，且不是PRIVATE 权限，可以看到团队下的项目
        if (pmTeamUser != null && pmTeamUser.getPermission() != PermissionEnum.PRIVATE.getCode()) {
            List<PmTeamProject> projects = teamProjectMapper.selectList(
                    Wrappers.<PmTeamProject>lambdaQuery().eq(PmTeamProject::getTeamId, teamId).eq(PmTeamProject::getIsDelete,0).orderByDesc(PmTeamProject::getCreateTime)
            );
            if(projects!=null && projects.size()>0){
                projects.forEach(t->{
                    t.setPermission(pmTeamUser.getPermission()==PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                    t.setUserRole(pmWorkSpaceUser.getUserRole()==UserRoleEnum.ADMIN.getCode()?UserRoleEnum.ADMIN.getName() :UserRoleEnum.MEMBER.getName() );
                    if(StringUtil.isNotEmpty(t.getImgUrl())){
                        try {
                            t.setImgUrl(MinioService.getPresignedObjectUrl(t.getImgUrl()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
                return projects;

            }
        }

        // 3. 用户不在团队中，检查是否在 teamId 下面的某个项目中
          //先查出团队中的项目
        List<PmTeamProject> teamProjectList=teamProjectMapper.selectList(Wrappers.<PmTeamProject>lambdaQuery().eq(PmTeamProject::getTeamId, teamId).eq(PmTeamProject::getIsDelete,0).orderByDesc(PmTeamProject::getCreateTime));
        if(teamProjectList!=null && teamProjectList.size()>0){
            teamProjectList.forEach(t->{
                LambdaQueryWrapper<PmProjectUser> projectWrapper = Wrappers.<PmProjectUser>lambdaQuery()
                        .eq(PmProjectUser::getProjectId, t.getProjectId())
                        .eq(PmProjectUser::getUserId, loginUserId);
                PmProjectUser projectUsers = pmTeamProjectUserMapper.selectOne(projectWrapper);
                if(projectUsers!=null){
                    t.setPermission(projectUsers.getPermission()==PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                    t.setUserRole(pmWorkSpaceUser.getUserRole()==UserRoleEnum.ADMIN.getCode()?UserRoleEnum.ADMIN.getName() :UserRoleEnum.MEMBER.getName() );
                    if(StringUtil.isNotEmpty(t.getImgUrl())){
                        try {
                            t.setImgUrl(MinioService.getPresignedObjectUrl(t.getImgUrl()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                    accessibleProjects.add(t);
                }
            });
        }
        return accessibleProjects;


    }

    @Override
    public List<PmTeamProject> queryTeamProject(String teamId,String groupId) throws PmServiceException{
        if (StringUtil.isEmpty(teamId)) {
            throw new PmServiceException("团队ID不能为空！请重新输入。");
        }
        String loginUserId = SecurityUtils.getUserId();
        //先判断当前用户是否在团队下,如果在空间下就可以看到空间下的所有团队，除了团队是私有的之外
        LambdaQueryWrapper<PmTeamUser> wrapper = Wrappers.<PmTeamUser>lambdaQuery()
                .eq(PmTeamUser::getTeamId,teamId)
                .eq(PmTeamUser::getUserId, loginUserId);
        PmTeamUser pmTeamUser=pmTeamUserMapper.selectOne(wrapper);
      /*  List<PmTeamProject> teamProjectList=teamProjectMapper.selectList(Wrappers.<PmTeamProject>lambdaQuery()
                .eq(PmTeamProject::getTeamId, teamId)
                .eq(PmTeamProject::getGroupId,groupId)
                .eq(PmTeamProject::getIsDelete,0).orderByDesc(PmTeamProject::getCreateTime));*/
        List<PmTeamProject> teamProjectList=teamProjectMapper.selectProjectList(teamId,groupId);
        if(teamProjectList!=null && teamProjectList.size()>0){
            // 过滤项目列表
            teamProjectList = teamProjectList.stream()
                    .filter(project -> {
                        // 如果项目不公开，判断当前用户是否是创建人
                        if (StringUtil.isNotEmpty(project.getCreateUser()) && project.getIsPublic() == 0) {
                            boolean isCreator = project.getCreateUser().equals(loginUserId);
                            return isCreator; // 只有创建人可以访问
                        }
                        return true; // 公开项目，所有人都可以访问
                    }).collect(Collectors.toList());
            teamProjectList.forEach(t->{

                t.setUserRole(t.getCreateUser().equals(loginUserId)? UserRoleEnum.CREATE.getName() :  UserRoleEnum.fromCode(pmTeamUser.getUserRole()).getName());
                t.setPermission(PermissionEnum.fromCode(pmTeamUser.getPermission()).getName());
                //t.setPermission(pmTeamUser.getPermission()==PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                //t.setUserRole(pmTeamUser.getUserRole()==UserRoleEnum.ADMIN.getCode()?UserRoleEnum.ADMIN.getName() :UserRoleEnum.MEMBER.getName() );
                if(StringUtil.isNotEmpty(t.getImgUrl())){
                    try {
                        t.setImgUrl(MinioService.getPresignedObjectUrl(t.getImgUrl()));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
            return teamProjectList;
        }
        return null;
        //在团队中并且不是私有的，就需要查询分组下的所有项目
        /*if(pmTeamUser != null && pmTeamUser.getPermission() != PermissionEnum.PRIVATE.getCode()){
            List<PmTeamProject> teamProjectList=teamProjectMapper.selectList(Wrappers.<PmTeamProject>lambdaQuery()
                    .eq(PmTeamProject::getTeamId, teamId)
                    .eq(PmTeamProject::getGroupId,groupId)
                    .eq(PmTeamProject::getIsDelete,0).orderByDesc(PmTeamProject::getCreateTime));
            if(teamProjectList!=null && teamProjectList.size()>0){
                // 过滤项目列表
                teamProjectList = teamProjectList.stream()
                        .filter(t -> t.getIsPublic() == 0 || t.getCreateUser().equals(loginUserId))
                        .collect(Collectors.toList());
                teamProjectList.forEach(t->{
                    t.setPermission(pmTeamUser.getPermission()==PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                    t.setUserRole(pmTeamUser.getUserRole()==UserRoleEnum.ADMIN.getCode()?UserRoleEnum.ADMIN.getName() :UserRoleEnum.MEMBER.getName() );
                    if(StringUtil.isNotEmpty(t.getImgUrl())){
                        try {
                            t.setImgUrl(MinioService.getPresignedObjectUrl(t.getImgUrl()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
            }
            return teamProjectList;
        }else{
            // 存储用户可以访问的项目
            List<PmTeamProject> accessibleProjects = new ArrayList<>();
            List<PmTeamProject> teamProjectList=teamProjectMapper.selectList(Wrappers.<PmTeamProject>lambdaQuery()
                    .eq(PmTeamProject::getTeamId, teamId)
                    .eq(PmTeamProject::getGroupId,groupId)
                    .eq(PmTeamProject::getIsDelete,0).orderByDesc(PmTeamProject::getCreateTime));
            if(teamProjectList!=null && teamProjectList.size()>0){
                teamProjectList.forEach(t->{
                    LambdaQueryWrapper<PmProjectUser> projectWrapper = Wrappers.<PmProjectUser>lambdaQuery()
                            .eq(PmProjectUser::getProjectId, t.getProjectId())
                            .eq(PmProjectUser::getUserId, loginUserId);
                    PmProjectUser projectUsers = pmTeamProjectUserMapper.selectOne(projectWrapper);
                    if(projectUsers!=null){
                        t.setPermission(projectUsers.getPermission()==PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                        t.setUserRole(projectUsers.getUserRole()==UserRoleEnum.ADMIN.getCode()?UserRoleEnum.ADMIN.getName() :UserRoleEnum.MEMBER.getName() );
                        if(StringUtil.isNotEmpty(t.getImgUrl())){
                            try {
                                t.setImgUrl(MinioService.getPresignedObjectUrl(t.getImgUrl()));
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                        accessibleProjects.add(t);
                    }
                });
            }
            return accessibleProjects;
        }*/
    }
    /**
     * 团队下创建项目
     * @param file
     * @param project
     * @return
     * @throws PmServiceException
     */
    @Override
    @Transactional
    public Boolean createTeamProject(MultipartFile file,PmTeamProject project) throws PmServiceException{

        if (StringUtil.isEmpty(project.getTeamId())) {
            throw new PmServiceException("团队ID不能为空！请重新输入。");
        }
        if (StringUtil.isEmpty(project.getProjectName())) {
            throw new PmServiceException("项目名称不能为空！请重新输入。");
        }
        String pid = PmIdUtils.fastSimpleUUID();
        if (file != null && !file.isEmpty()) {
            String originalFilename = file.getOriginalFilename();
            String url="ebi/project/"+pid+"/"+originalFilename;
            MinioService.uploadFile(url,file);
            project.setImgUrl(url);
        }
        String loginUserId = SecurityUtils.getUserId();
        project.setTeamId(project.getTeamId());
        project.setProjectId(pid);
        project.setCreateUser(loginUserId);
        project.setCreateTime(new Date());
        project.setUpdateTime(new Date());
        project.setIsDelete(0);//默认正常状态
        project.setPrefix(IdGenerator.generateUniqueId());
        int result=teamProjectMapper.insert(project);
        if(result>0){
            PmProjectUser pmProjectUser=new PmProjectUser();
            pmProjectUser.setId(PmIdUtils.fastSimpleUUID());
            pmProjectUser.setProjectId(pid);//项目id
            pmProjectUser.setUserId(loginUserId);
            pmProjectUser.setUserRole(UserRoleEnum.CREATE.getCode());//创建者角色
            pmProjectUser.setPermission(PermissionEnum.EDIT.getCode());//拥有编辑权限
            pmProjectUser.setCreateUser(loginUserId);
            pmProjectUser.setCreateTime(new Date());
            return pmTeamProjectUserMapper.insert(pmProjectUser)>0;
        }
        return false;
    }

    /**
     * 修改项目
     * @param file
     * @param project
     * @return
     * @throws PmServiceException
     */
    @Override
    public Boolean updateTeamProject(MultipartFile file,PmTeamProject project) throws PmServiceException{
        try {
       /* if (StringUtil.isEmpty(project.getTeamId())) {
            throw new PmServiceException("团队ID不能为空！请重新输入。");
        }*/
        if (StringUtil.isEmpty(project.getProjectName())) {
            throw new PmServiceException("项目名称不能为空！请重新输入。");
        }
        if (file != null && !file.isEmpty()) {
            LambdaQueryWrapper<PmTeamProject> wrapper = Wrappers.<PmTeamProject>lambdaQuery()
                    .eq(PmTeamProject::getProjectId, project.getProjectId());
            PmTeamProject pmTeamProject=teamProjectMapper.selectOne(wrapper);
            if (pmTeamProject != null && StringUtil.isNotEmpty(pmTeamProject.getImgUrl())) {
                //删除minio缩略图地址
                MinioService.removeFile(pmTeamProject.getImgUrl());
            }
            String originalFilename = file.getOriginalFilename();
            String url="ebi/project/"+project.getProjectId()+"/"+originalFilename;
            MinioService.uploadFile(url,file);
            project.setImgUrl(url);
        }
        String prefix=project.getPrefix();
        //校验项目前缀不能为空
        if(StringUtil.isNotEmpty(prefix)){
            LambdaQueryWrapper<PmTeamProject> prefixWrapper = Wrappers.<PmTeamProject>lambdaQuery()
                    .eq(PmTeamProject::getPrefix, project.getPrefix())
                    .ne(PmTeamProject::getProjectId, project.getProjectId());
            Long count=teamProjectMapper.selectCount(prefixWrapper);
            if(count>0){
                throw new PmServiceException("该项目标识已存在");
            }
        }
        String loginUserId = SecurityUtils.getUserId();
     //   project.setTeamId(project.getTeamId());
        //project.setCreateUser(loginUserId);
        project.setUpdateUser(loginUserId);
        project.setUpdateTime(new Date());
        //project.setCreateTime(new Date());
        project.setIsDelete(0);//默认正常状态
        return teamProjectMapper.updateById(project)>0;
        }catch (Exception e){
            throw new PmServiceException("修改失败");
        }
    }

    /**
     * 删除项目至回收站
     * @param teamProject
     * @return
     * @throws PmServiceException
     */
    @Override
    public Boolean deleteTeamProject(PmTeamProject teamProject) throws PmServiceException{
        String loginUserId = SecurityUtils.getUserId();
        if (StringUtil.isEmpty(teamProject.getProjectId())) {
            throw new PmServiceException("项目ID不能为空！请重新输入。");
        }
        LambdaQueryWrapper<PmTeamProject> wrapper = Wrappers.<PmTeamProject>lambdaQuery()
                .eq(PmTeamProject::getProjectId, teamProject.getProjectId());
        teamProject=teamProjectMapper.selectOne(wrapper);
        teamProject.setIsDelete(1);//逻辑删除
        teamProject.setUpdateUser(loginUserId);
        teamProject.setUpdateTime(new Date());
        teamProject.setDeleteTime(new Date());
        return teamProjectMapper.updateById(teamProject) > 0;
    }


    /**
     * 用户搜索
     * @param content
     * @return
     */
    public List<PlmUserDto> searchuser(String projectId,String content){
        List<PlmUserDto> dtoList=null;
        String phoneRegex = "^1[3-9]\\d{9}$";
        Pattern pattern = Pattern.compile(phoneRegex);
        Matcher matcher = pattern.matcher(content);
        String emailRegex = "^[a-zA-Z0-9_.%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$";
        Pattern pattern2 = Pattern.compile(emailRegex);
        Matcher matcher2 = pattern2.matcher(content);
        if(StringUtil.isEmpty(content)){
            content=" ";
        }
        String type="user";
        if (matcher.matches()) {
            dtoList=pmTeamProjectUserMapper.selectProjectByUser(projectId,null,content,null);
            type="phone";
        }else if (matcher2.matches()) {
            dtoList=pmTeamProjectUserMapper.selectProjectByUser(projectId,null,null,content);
            type="email";
        }else{
            String account="%" + content + "%";
            dtoList=pmTeamProjectUserMapper.selectProjectByUser(projectId,account,null,null);
        }
        if(dtoList!=null && dtoList.size()>0){
            for(PlmUserDto t:dtoList){
                t.setType(type);//默认表示使用账号搜索
                if(StringUtil.isNotEmpty(t.getPermission())){
                    t.setIsInTeam(true);//是否在团队中
                }else{
                    t.setIsInTeam(false);
                }
            }
        }
        return dtoList;
    }
    /**
     * 要用项目成员加入
     * @param projectUsers
     * @return
     */
    @Transactional
    public Boolean  addProjectUser(List<PmProjectUserVo> projectUsers){
        String loginUserId = SecurityUtils.getUserId();
        projectUsers.forEach(t->{
            LambdaQueryWrapper<PmProjectUser> wrapper = Wrappers.<PmProjectUser>lambdaQuery()
                    .eq(PmProjectUser::getProjectId, t.getProjectId())
                    .eq(PmProjectUser::getUserId, t.getUserId());
            PmProjectUser pmProjectUser=pmTeamProjectUserMapper.selectOne(wrapper);
            if(pmProjectUser==null){
                PmProjectUser user=new PmProjectUser();
                user.setId(PmIdUtils.fastSimpleUUID());
                user.setProjectId(t.getProjectId());//项目id
                user.setUserId(t.getUserId());
                user.setUserRole(UserRoleEnum.MEMBER.getCode());//默认加入的是成员角色
                user.setPermission(t.getPermission());
                user.setCreateUser(loginUserId);
                user.setCreateTime(new Date());
                pmTeamProjectUserMapper.insert(user);
            }
        });
        projectUsers.forEach(t->{
            LambdaQueryWrapper<PmTeamUser> wrapper = Wrappers.<PmTeamUser>lambdaQuery()
                    .eq(PmTeamUser::getTeamId, t.getTeamId())
                    .eq(PmTeamUser::getUserId, t.getUserId());
            PmTeamUser teamUser=pmTeamUserMapper.selectOne(wrapper);
            //如果不在团队中就创建一个private私有的
            if(teamUser==null){
                PmTeamUser pmTeamUser=new PmTeamUser();
                pmTeamUser.setId(PmIdUtils.fastSimpleUUID());
                pmTeamUser.setTeamId(t.getTeamId());//团队
                pmTeamUser.setUserId(t.getUserId());
                pmTeamUser.setUserRole(UserRoleEnum.MEMBER.getCode());//管理员角色
                pmTeamUser.setPermission(PermissionEnum.PRIVATE.getCode());//拥有编辑权限
                pmTeamUser.setCreateUser(loginUserId);
                pmTeamUser.setCreateTime(new Date());
                pmTeamUserMapper.insert(pmTeamUser);
            }
        });

        /*projectUsers.forEach(t->{
            LambdaQueryWrapper<PmWorkSpaceUser> wrapper = Wrappers.<PmWorkSpaceUser>lambdaQuery()
                    .eq(PmWorkSpaceUser::getSpaceId, t.getSpaceId())
                    .eq(PmWorkSpaceUser::getUserId, t.getUserId());
            PmWorkSpaceUser workSpaceUser=pmWorkSpaceUserMapper.selectOne(wrapper);
            //如果不在团队中就创建一个private私有的
            if(workSpaceUser==null){
                PmWorkSpaceUser pmWorkSpaceUser=new PmWorkSpaceUser();
                pmWorkSpaceUser.setId(PmIdUtils.fastSimpleUUID());
                pmWorkSpaceUser.setSpaceId(t.getSpaceId());//空间ID
                pmWorkSpaceUser.setUserId(t.getUserId());
                pmWorkSpaceUser.setUserRole(UserRoleEnum.MEMBER.getCode());//管理员角色
                pmWorkSpaceUser.setPermission(PermissionEnum.PRIVATE.getCode());//拥有编辑权限
                pmWorkSpaceUser.setCreateUser(loginUserId);
                pmWorkSpaceUser.setCreateTime(new Date());
                pmWorkSpaceUserMapper.insert(pmWorkSpaceUser);
            }
        });*/
        return true;
    }
    /**
     * 项目搜索
     * @param content
     * @return
     */
    public JSONArray searchTeamList(String content){
        String loginUserId = SecurityUtils.getUserId();
        String keyWord="%"+content+"%";
        List<PmTeamProjectSerachDto> teamProjectSerachDtoList=teamProjectMapper.searchTeamList(keyWord,loginUserId);
        JSONArray jsonArray=new JSONArray();
        if(teamProjectSerachDtoList!=null && teamProjectSerachDtoList.size()>0){
            teamProjectSerachDtoList.forEach(t->{
                if(StringUtil.isNotEmpty(t.getImgUrl())){
                    try {
                        String url=MinioService.getPresignedObjectUrl(t.getImgUrl());
                        t.setImgUrl(url);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
            JSONObject resultJson=new JSONObject();
            resultJson.put("title","团队");
            resultJson.put("list",teamProjectSerachDtoList);
            jsonArray.add(resultJson);
        }

        List<PmTeamProjectSerachDto> projectSerachDtoList=teamProjectMapper.searchProjectList(keyWord,loginUserId);
        if(projectSerachDtoList!=null && projectSerachDtoList.size()>0){
            projectSerachDtoList.forEach(t->{
                if(StringUtil.isNotEmpty(t.getImgUrl())){
                    try {
                        String url=MinioService.getPresignedObjectUrl(t.getImgUrl());
                        t.setImgUrl(url);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
            JSONObject resultJson2=new JSONObject();
            resultJson2.put("title","项目");
            resultJson2.put("list",projectSerachDtoList);
            jsonArray.add(resultJson2);
        }

        return jsonArray;
    }
}
