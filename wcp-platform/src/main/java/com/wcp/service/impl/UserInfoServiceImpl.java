package com.wcp.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.config.TokenService;
import com.wcp.core.config.WeChatConfig;
import com.wcp.core.constant.Constants;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.http.PmResult;
import com.wcp.core.text.PmIdUtils;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.*;
import com.wcp.enums.PermissionEnum;
import com.wcp.enums.UserRoleEnum;
import com.wcp.mapper.PlatformUserMapper;
import com.wcp.mapper.PlmUserRoleMapper;
import com.wcp.mapper.PmWorkSpaceUserMapper;
import com.wcp.minio.MinioService;
import com.wcp.service.*;
import com.wcp.utils.AESUtil;
import com.wcp.utils.HttpClientUtil;
import com.wcp.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class UserInfoServiceImpl  extends ServiceImpl<PlatformUserMapper, PlmUser> implements IUserInfoService {
    @Autowired
    private WeChatConfig weChatConfig;
    @Autowired
    public PlatformUserMapper userInfoMapper;
    @Autowired
    public PlmUserRoleMapper plmUserRoleMapper;
    @Autowired
    private TokenService tokenService;
    @Resource
    private AuthenticationManager authenticationManager;
    @Autowired
    public PmWorkSpaceService spaceService;

    @Autowired
    public PmWorkTeamService workTeamService;

    @Autowired
    public PmTeamUserService teamUserService;
    @Autowired
    private PmWorkSpaceUserMapper pmWorkSpaceUserMapper;
    /**
     * 使用案例
     *
     * @param userName
     * @return
     */
    public List<PlmUser> getInfo(String userName) {
        /*PlatformUserInfo u=new PlatformUserInfo();
        u.setUserName(userName);
        return userInfoMapper.selectUserAll(u);*/

        QueryWrapper<PlmUser> wrapper = new QueryWrapper<PlmUser>();
        return userInfoMapper.selectList(wrapper);
    }

    /**
     * 账号查询用户信息
     *
     * @param account
     * @return
     */
    public PlmUser selectUserByUserName(String account) {
        QueryWrapper<PlmUser> wrapper = new QueryWrapper<PlmUser>();
        wrapper.eq("ACCOUNT", account);
        PlmUser plmUser = userInfoMapper.selectOne(wrapper);
        if (plmUser != null && plmUser.getHeadImage() != null) {
            // 构建完整的MinIO对象路径
            String objectPath = "ebi/platform/" + plmUser.getHeadImage();

            // 生成可访问的URL
            String avatarUrl = null;
            try {
                avatarUrl = MinioService.getPresignedObjectUrl(objectPath);
            } catch (Exception e) {
                // 记录错误日志
                log.error("生成用户头像URL失败");
                // 设置默认提示信息
                plmUser.setHeadImage("头像加载失败，请联系管理员");
            }

            // 设置完整URL
            plmUser.setHeadImage(avatarUrl);
        }
        return plmUser;
    }

    /**
     * 登录
     *
     * @param username
     * @param password
     * @return
     */
    public Map<String,Object> login(String username, String password) {
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            if(StringUtil.isNotEmpty(password)){
                authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(username, password));
            }else{
                authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(username, null));
            }

        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw new PmServiceException("用户不存在/密码错误");
            } else {
                throw new PmServiceException(e.getMessage());
            }
        }
        Map<String,Object> map=new HashMap<>();
        PmLoginUser loginUser = (PmLoginUser) authentication.getPrincipal();
        map.put("userId",loginUser.getUserId());
        map.put("account",loginUser.getUser().getAccount());
        map.put("userName",loginUser.getUser().getUserName());
        map.put("headImage",loginUser.getUser().getHeadImage());
        map.put(Constants.TOKEN,tokenService.createToken(loginUser));
        // 生成token
        return map;
    }

    /**
     * 用户ID获取用户信息
     *
     * @param userId
     * @return
     */
    public PlmUser queryMe(String userId) {
        QueryWrapper<PlmUser> wrapper = new QueryWrapper<PlmUser>();
        wrapper.select("USER_ID", "ACCOUNT", "PHONE_NUMBER", "USER_NAME", "OPEN_ID", "COMPANY");
        wrapper.eq("USER_ID", userId);
        return userInfoMapper.selectOne(wrapper);
    }

    /**
     * 注册
     */
    @Transactional
    @Override
    public String register(PlmUser plmUser) {
        String msg = "";
        if (PmStringUtils.isEmpty(plmUser.getUserName())) {
            msg = "用户名不能为空";
        } else if (PmStringUtils.isEmpty(plmUser.getPwd())) {
            msg = "用户密码不能为空";
        } else if (PmStringUtils.isEmpty(plmUser.getPhone())) {
            msg = "手机号不能为空";
        } else if (PmStringUtils.isEmpty(plmUser.getEmail())) {
            msg = "邮箱不能为空";
        } else if (Constants.NOT_UNIQUE.equals(checkUserNameUnique(plmUser.getAccount()))) {
            msg = "保存用户'" + plmUser.getAccount() + "'失败，注册账号已存在";
        } else if (Constants.NOT_UNIQUE.equals(checkPhoneUnique(plmUser.getPhone()))) {
            msg = "手机号已被注册";
        } else if (Constants.NOT_UNIQUE.equals(checkEmailUnique(plmUser.getEmail()))) {
            msg = "邮箱已被注册";
        } else {
            String id = PmIdUtils.fastSimpleUUID();
            plmUser.setUserId(id);
            plmUser.setUserStatus(1);
            String password=AESUtil.decrypt(plmUser.getPwd());
            plmUser.setPwd(SecurityUtils.encryptPassword(password));
            plmUser.setCreateTime(new Date());
            //插入用户
            int regFlag = userInfoMapper.insert(plmUser);
            //默认注册使用普通用户
            PlmUserRole plmUserRole = new PlmUserRole();
            plmUserRole.setRoleId(1);//默认给普通角色
            plmUserRole.setUserId(id);
            plmUserRoleMapper.insert(plmUserRole);//保存用户角色管理
            if (regFlag==0) {
                msg = "注册失败,请联系系统管理人员";
            }
            if (regFlag > 0) {
                //默认注册个人空间
                String spaceId = PmIdUtils.fastSimpleUUID();
                PmWorkSpace pws = new PmWorkSpace();
                pws.setSpaceId(spaceId);
                pws.setSpaceName("我的空间");
                pws.setCreateUser(id);
                pws.setCreateTime(new Date());
                boolean save = spaceService.save(pws);
                if (save) {
                        PmWorkSpaceUser pmWorkSpaceUser=new PmWorkSpaceUser();
                        pmWorkSpaceUser.setId(PmIdUtils.fastSimpleUUID());
                        pmWorkSpaceUser.setSpaceId(spaceId);//空间ID
                        pmWorkSpaceUser.setUserId(id);
                        pmWorkSpaceUser.setUserRole(UserRoleEnum.ADMIN.getCode());//管理员角色
                        pmWorkSpaceUser.setPermission(PermissionEnum.EDIT.getCode());//拥有编辑权限
                        pmWorkSpaceUser.setCreateUser(id);
                        pmWorkSpaceUser.setCreateTime(new Date());
                    if (pmWorkSpaceUserMapper.insert(pmWorkSpaceUser) > 0) {
                        msg = ""; // 成功时清空错误消息
                    } else {
                        msg = "空间用户关系创建失败";
                    }
                        //pmWorkSpaceUserMapper.insert(pmWorkSpaceUser)>0;
                }else {
                    msg = "个人空间创建失败";
                }

            }
        }
        return msg;
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param account 用户名称
     * @return 结果
     */
    public String checkUserNameUnique(String account) {
        QueryWrapper<PlmUser> wrapper = new QueryWrapper<PlmUser>();
        wrapper.select("USER_ID", "ACCOUNT", "PHONE_NUMBER", "USER_NAME", "OPEN_ID", "COMPANY");
        wrapper.eq("ACCOUNT", account);
        PlmUser user = userInfoMapper.selectOne(wrapper);
        if (user != null) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 校验手机号是否唯一
     *
     * @param phoneNumber 手机号
     * @return 结果
     */
    public String checkPhoneUnique(String phoneNumber) {
        QueryWrapper<PlmUser> wrapper = new QueryWrapper<>();
        wrapper.eq("PHONE_NUMBER", phoneNumber);
        PlmUser user = userInfoMapper.selectOne(wrapper);
        return user != null ? Constants.NOT_UNIQUE : Constants.UNIQUE;
    }

    /**
     * 校验邮箱是否唯一
     *
     * @param email 邮箱
     * @return 结果
     */
    public String checkEmailUnique(String email) {
        QueryWrapper<PlmUser> wrapper = new QueryWrapper<>();
        wrapper.eq("EMAIL", email);
        PlmUser user = userInfoMapper.selectOne(wrapper);
        return user != null ? Constants.NOT_UNIQUE : Constants.UNIQUE;
    }

    /**
     * 微信扫码登录获取用户信息
     * @param code
     * @return
     */
    @Override
    public PmLoginUser saveWeChatUser(String code) {
        String accessTokenUrl = String.format(WeChatConfig.getOpenAccessTokenUrl(), weChatConfig.getOpenAppid(), weChatConfig.getOpenAppsecret(), code);
        //获取access_token

        JSONObject baseMap = HttpClientUtil.doGet(accessTokenUrl);
        if (CollectionUtils.isEmpty(baseMap)) {
            return null;
        }
        String accessToken = (String) baseMap.get("access_token");
        String openId = (String) baseMap.get("openid");
        QueryWrapper<PlmUser> wrapper = new QueryWrapper<PlmUser>();
        wrapper.eq("OPEN_ID",openId);
        PlmUser dbUser = userInfoMapper.selectOne(wrapper);
        if (null != dbUser) { // 一：更新用户，二：直接返回
            PmLoginUser pmLoginUser=new PmLoginUser();
            pmLoginUser.setUser(dbUser);
            pmLoginUser.setUserId(dbUser.getUserId());
            return pmLoginUser;
        }

        //获取用户基本信息
        String userInfoUrl = String.format(WeChatConfig.getOpenUserInfoUrl(), accessToken, openId);
        //获取access_token
        Map<String, Object> baseUserMap = HttpClientUtil.doGet(userInfoUrl);
        Double sexTemp  = (Double) baseUserMap.get("sex");
        int sex = sexTemp.intValue();
        String province = (String) baseUserMap.get("province");
        String city = (String) baseUserMap.get("city");
        String country = (String) baseUserMap.get("country");
        String headimgurl = (String) baseUserMap.get("headimgurl");
        StringBuilder sb = new StringBuilder(country).append("||").append(province).append("||").append(city);
        String finalAddress = sb.toString();
        if (CollectionUtils.isEmpty(baseUserMap)) {
            return null;
        }
        String nickname = (String) baseUserMap.get("nickname");
        try {
            //解决乱码
            nickname = new String(nickname.getBytes("ISO-8859-1"), "UTF-8");
            finalAddress = new String(finalAddress.getBytes("ISO-8859-1"), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //需要执行插入用户
        PlmUser user = new PlmUser();
        String id = PmIdUtils.fastSimpleUUID();
        user.setUserId(id);
        user.setUserStatus(1);
        user.setUserName(nickname);
        //插入用户
       /* int regFlag = userInfoMapper.insert(plmUser);
        //默认注册使用普通用户
        PlmUserRole plmUserRole = new PlmUserRole();
        plmUserRole.setRoleId(1);//默认给普通角色
        plmUserRole.setUserId(id);
        plmUserRoleMapper.insert(plmUserRole);//保存用户角色管理*/
        /*user.setUserName(nickname);
        user.setOpenId(openId);

        userInfoMapper.insert(user);*/
        PmLoginUser loginUser=new PmLoginUser();
        loginUser.setHeadImg(headimgurl);
        loginUser.setUser(user);
        return loginUser;
    }

    /**
     * 查询真实用户名
     * @param identifier 手机号或邮箱
     * @return
     */
    @Override
    public PlmUser findUserByIdentifier(String identifier) {
        // 先按用户名查询
        PlmUser user = userInfoMapper.selectOne(
                new QueryWrapper<PlmUser>().eq("ACCOUNT", identifier)
        );

        // 再按手机号查询
        if (user == null) {
            user = userInfoMapper.selectOne(
                    new QueryWrapper<PlmUser>().eq("PHONE_NUMBER", identifier)
            );
        }

        // 最后按邮箱查询
        if (user == null) {
            user = userInfoMapper.selectOne(
                    new QueryWrapper<PlmUser>().eq("EMAIL", identifier)
            );
        }

        return user;
    }

    /**
     * 旧密码验证
     * @param account 用户名
     * @param password 密码
     */
    @Override
    public void validatePassword(String account, String password) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(account, password)
            );
        } catch (BadCredentialsException e) {
            throw new PmServiceException("旧密码错误");
        } catch (Exception e) {
            throw new PmServiceException("密码验证失败");
        }
    }

    /**
     * 修改密码
     * @param userId 用户ID
     * @param newPassword 新密码
     */
    @Override
    public void changePassword(String userId, String newPassword) {
        UpdateWrapper<PlmUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("USER_ID", userId);
        updateWrapper.set("PWD", SecurityUtils.encryptPassword(newPassword));
        // 执行更新（只更新设置的字段）
        int result = userInfoMapper.update(null, updateWrapper);
        if (result == 0) {
            throw new PmServiceException("密码修改失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAccount(String userId) {
        try {
            // 标记用户为已注销状态
            PlmUser user = new PlmUser();
            user.setUserId(userId);
            user.setUserStatus(0);

            int result = userInfoMapper.updateById(user);
            if (result == 0) {
                throw new PmServiceException("账户注销失败");
            }

        } catch (Exception e) {
            log.error("账户注销异常", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new PmServiceException("账户注销失败，请联系管理员");
        }
    }

    @Override
    public PmResult updateUserInfo(PlmUser plmUser, PlmUser currentUser) {
        UpdateWrapper<PlmUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("USER_ID", plmUser.getUserId());

        // 设置需要更新的字段
        if (StringUtil.isNotEmpty(plmUser.getUserName())) {
            updateWrapper.set("USER_NAME", plmUser.getUserName());
        }
        if (StringUtil.isNotEmpty(plmUser.getHeadImage())) {
            updateWrapper.set("HEAD_IMAGE", plmUser.getHeadImage());
        }
        if (StringUtil.isNotEmpty(plmUser.getEmail())) {
            updateWrapper.set("EMAIL", plmUser.getEmail());
        }


        // 邮箱唯一性校验
        if (StringUtil.isNotEmpty(plmUser.getEmail()) &&
                !plmUser.getEmail().equals(currentUser.getEmail())) {

            if (checkEmailUnique(plmUser.getEmail()).equals(Constants.NOT_UNIQUE)) {
                return PmResult.error("邮箱已被注册");
            }
        }

        // 手机号唯一性校验
        if (StringUtil.isNotEmpty(plmUser.getPhone()) &&
                !plmUser.getPhone().equals(currentUser.getPhone())) {

            if (checkPhoneUnique(plmUser.getPhone()).equals(Constants.NOT_UNIQUE)) {
                return PmResult.error("手机号已被注册");
            }
        }

        // 执行更新（只更新设置的字段）
        int result = userInfoMapper.update(null, updateWrapper);
        if (result == 0) {
            return PmResult.error("用户信息更新失败");
        }
        return PmResult.success("用户信息更新成功");
    }

    /**
     * 修改用户头像
     * @param headImage 头像file
     * @param loginUser 登录信息
     * @return
     */
    @Override
    public PmResult updateAvatar(MultipartFile headImage, PmLoginUser loginUser) {
        // 查询头像是否存在
        String userId = loginUser.getUserId();
        String image = userInfoMapper.selectById(userId).getHeadImage();

        // 构建新头像完整路径
        String originalFilename = headImage.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String newFileName  = "ebi/platform/" + StringUtil.generateUUID() + extension;
        try {
            // 删除原有头像（如果存在）
            if (image != null && !image.isEmpty()) {
                String fullOldPath = "ebi/platform/" + image;
                MinioService.removeFile(fullOldPath);
            }
            // 上传新头像
            MinioService.uploadFile(newFileName,headImage);

            // 提取需要存入数据库的相对路径（随机文件名）
            int fileIndex = newFileName.indexOf("/platform/") + "/platform/".length();
            String relativePath = newFileName.substring(fileIndex);

            // 更新数据库的头像路径
            UpdateWrapper<PlmUser> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("USER_ID", loginUser.getUserId());
            updateWrapper.set("HEAD_IMAGE", relativePath);
            // 执行更新（只更新设置的字段）
            int result = userInfoMapper.update(null, updateWrapper);
            if (result == 0) {
                return PmResult.error("头像修改失败");
            }
            return PmResult.success("头像更新成功");

        } catch (Exception e) {
            return PmResult.error(e.getMessage());
        }

    }
}
