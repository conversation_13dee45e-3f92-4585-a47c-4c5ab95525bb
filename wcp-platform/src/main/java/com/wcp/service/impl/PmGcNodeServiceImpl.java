package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.domain.PmGcNode;
import com.wcp.domain.PmGcPage;
import com.wcp.mapper.PmGcNodeMapper;
import com.wcp.mapper.PmGcPageMapper;
import com.wcp.service.PmGcNodeService;
import com.wcp.utils.ConstantsUtil;
import com.wcp.utils.DateUtil;
import com.wcp.utils.TreeNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Description: 图元树service
 * Author: qianchao
 * Date: 2024/3/4 15:43
 */
@Service
public class PmGcNodeServiceImpl extends ServiceImpl<PmGcNodeMapper,PmGcNode> implements PmGcNodeService {

    @Autowired
    private PmGcPageMapper pmGcPageMapper;
    /**
     * 添加图元树形节点
     * @param pmGcNode
     */
    @Override
    public boolean saveNode(PmGcNode pmGcNode) {
        //设置项目ID

        pmGcNode.setCreateTime(new Date());
        pmGcNode.setUpdateTIme(new Date());
        pmGcNode.setPubStatus(0);//默认未发布
        pmGcNode.setAccount(SecurityUtils.getUserId());
        return this.save(pmGcNode);
    }

    @Override
    public boolean updateNode(PmGcNode pmGcNode) {
        //设置项目ID
        pmGcNode.setCreateTime(new Date());
        pmGcNode.setUpdateTIme(new Date());
        pmGcNode.setAccount(SecurityUtils.getUserId());
        UpdateWrapper<PmGcNode> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("PID", pmGcNode.getPid())
                .eq("PROJECT_ID", pmGcNode.getProjectId());
       // return this.updateById(pmGcNode);
        return this.update(pmGcNode, updateWrapper);

    }

    /**
     * 图元左侧树形菜单查询
     * @param projectId
     * @return
     */
    @Override
    public List<TreeNode> queryNodes(String projectId) {
        LambdaQueryWrapper<PmGcNode> wrapper = new LambdaQueryWrapper<PmGcNode>();
        wrapper.eq(PmGcNode::getProjectId,projectId);
        wrapper.eq(PmGcNode::getDelFlag,ConstantsUtil.NOT_DELETED);//查询未删除的数据
        List<PmGcNode> pmGcNodeList=baseMapper.selectList(wrapper);
        List<TreeNode> treeNodeList=convertToTreeNodeList(pmGcNodeList);
        return treeNodeList;
    }

    /**
     * 树形结构数据转换
     * @param pmGcNodeList
     * @return
     */
    public  List<TreeNode> convertToTreeNodeList(List<PmGcNode> pmGcNodeList) {
        List<TreeNode> treeNodeList = new ArrayList<>();
        for (PmGcNode pmGcNode : pmGcNodeList) {
            if (pmGcNode.getParentId() == null || "".equals(pmGcNode.getParentId())) {
                TreeNode treeNode = convertToTreeNode(pmGcNode, pmGcNodeList);
                treeNodeList.add(treeNode);
            }
        }
        return treeNodeList;
    }

    /**
     * 递归设置属性文件夹的层级
     * @param pmGcNode
     * @param pmGcNodeList
     * @return
     */
    public static TreeNode convertToTreeNode(PmGcNode pmGcNode, List<PmGcNode> pmGcNodeList) {
        TreeNode treeNode = new TreeNode();
        treeNode.setKey(pmGcNode.getPid());
        treeNode.setNodeName(pmGcNode.getNodeName());
        treeNode.setNodeType(pmGcNode.getNodeType());
        //格式化时间
        treeNode.setCreateTime(DateUtil.ymdhms.format(pmGcNode.getCreateTime()));
        treeNode.setParentId(pmGcNode.getParentId());
        treeNode.setPubStatus(pmGcNode.getPubStatus());
        List<TreeNode> children = new ArrayList<>();
        for (PmGcNode childNode : pmGcNodeList) {
            //如果父节点不为空，递归调用
            if (childNode.getParentId() != null && childNode.getParentId().equals(pmGcNode.getPid())) {
                TreeNode childTreeNode = convertToTreeNode(childNode, pmGcNodeList);
                children.add(childTreeNode);
            }
        }
        treeNode.setChildren(children);

        return treeNode;
    }

    /**
     * 删除菜单节点
     * @param pId
     * @param nodeType
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteNode(String pId,String nodeType,String projectId){
        //当前删的节点是图元类型
        PmGcNode pm=new PmGcNode();
        pm.setPid(pId);
        pm.setUpdateTIme(new Date());//删除时间
        pm.setDelFlag(ConstantsUtil.IS_DELETE);//状态修改为已删除
        pm.setPubStatus(0);//删除图元的时候,将图元发布状态变更为未发布,因为图元是逻辑删除
        int result=ConstantsUtil.NODE_TYPE_GRAPH.equals(nodeType)?this.baseMapper.updateById(pm):baseMapper.deleteById(pId);
        //路由直接删除
        LambdaQueryWrapper<PmGcPage> deleteWrapper=new LambdaQueryWrapper<>();
        deleteWrapper.eq(PmGcPage::getId, pId).eq(PmGcPage::getProjectId,projectId);
        pmGcPageMapper.delete(deleteWrapper);
        //如果删除的当前节点是文件夹
        if(ConstantsUtil.NODE_TYPE_FOLDER.equals(nodeType)){
            //需要将当前节点下面的子节点
            LambdaQueryWrapper<PmGcNode> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(PmGcNode::getParentId,pId).eq(PmGcNode::getProjectId,projectId);
            queryWrapper.eq(PmGcNode::getDelFlag,ConstantsUtil.NOT_DELETED);//查询为删除的子节点
            List<PmGcNode> pmGcNodeList=baseMapper.selectList(queryWrapper);
            if(pmGcNodeList!=null && pmGcNodeList.size()>0){
                pmGcNodeList.forEach(t->{
                    deleteNode(t.getPid(),t.getNodeType(),projectId);
                });
            }

        }else{

        }
        return result;
    }

}
