package com.wcp.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.text.PmAesUtil;
import com.wcp.db.data.DmConfiguration;
import com.wcp.db.enums.DatasourceTypes;
import com.wcp.db.util.JdbcProvider;
import com.wcp.db.util.JdbcUrlUtils;
import com.wcp.domain.PmSpDataSource;
import com.wcp.mapper.PmSpDataSourceMapper;
import com.wcp.service.PmSpDataSourceService;
import io.dataease.plugins.common.base.domain.Datasource;
import io.dataease.plugins.common.dto.datasource.TableDesc;
import io.dataease.plugins.common.request.datasource.DatasourceRequest;
import io.dataease.plugins.datasource.entity.MysqlConfiguration;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description 数据源配置
 * 2. <AUTHOR>
 * 3. @Date 2025/4/16 11:25
 */
@Service
public class PmSpDataSourceServiceImpl extends ServiceImpl<PmSpDataSourceMapper, PmSpDataSource> implements PmSpDataSourceService {
    /**
     * 根据类型查询数据源
     * @param type
     * @param projectId
     * @return
     */
    public List<PmSpDataSource> dataSourceList(String type,String projectId){
        LambdaQueryWrapper<PmSpDataSource> wrapper=new LambdaQueryWrapper<>();
        if("oracle".equals(type)){
            PmSpDataSource p=new PmSpDataSource();
            p.setProjectId("9ba2450dba0d47368947da448fc3cf74");
            p.setService("oracle");
            p.setAlias("oracle数据库");
            List<PmSpDataSource> ls=new ArrayList<>();
            ls.add(p);
            return ls;
        }
        wrapper.select(PmSpDataSource::getService, PmSpDataSource::getAlias,PmSpDataSource::getProjectId)
                .eq(PmSpDataSource::getDbType, type)
                .eq(PmSpDataSource::getProjectId,projectId);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 查询数据库下的表
     * @param service
     * @param projectId
     * @return
     */
    public List<TableDesc> queryTables(String service,String projectId){
        List<TableDesc> tables=null;
        try {
            LambdaQueryWrapper<PmSpDataSource> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PmSpDataSource::getService, service)
                    .eq(PmSpDataSource::getProjectId, projectId);
            PmSpDataSource pmSpDataSource = this.baseMapper.selectOne(wrapper);
            if (pmSpDataSource != null) {
                String dbType = pmSpDataSource.getDbType();
                Map<String, String> datasourceDetail = JdbcUrlUtils.parseJdbcUrl(pmSpDataSource.getUrl());
                String password = PmAesUtil.decrypt(pmSpDataSource.getUserPwd());
                DatasourceRequest datasourceRequest = new DatasourceRequest();
                Datasource datasource = new Datasource();
                JdbcProvider provider = new JdbcProvider();
                switch (dbType) {
                    case "mysql":
                        MysqlConfiguration mysqlConfiguration = new MysqlConfiguration();
                        mysqlConfiguration.setHost(datasourceDetail.get("host"));
                        mysqlConfiguration.setPort(Integer.parseInt(datasourceDetail.get("port")));
                        mysqlConfiguration.setDataBase(pmSpDataSource.getDbName());
                        mysqlConfiguration.setUsername(pmSpDataSource.getUserName());
                        mysqlConfiguration.setPassword(password);
                        mysqlConfiguration.setDataSourceType(DatasourceTypes.mysql.getType());
                        datasource.setConfiguration(JSONObject.toJSONString(mysqlConfiguration));
                        datasource.setType(DatasourceTypes.mysql.getType());
                        datasource.setId(projectId+"-"+service);
                        datasourceRequest = new DatasourceRequest();
                        datasourceRequest.setDatasource(datasource);
                        tables = provider.getTables(datasourceRequest);
                        System.out.println(tables);
                        break;
                    case "dm":
                        DmConfiguration dmConfiguration = new DmConfiguration();
                        dmConfiguration.setHost(datasourceDetail.get("host"));
                        dmConfiguration.setPort(Integer.parseInt(datasourceDetail.get("port")));
                        dmConfiguration.setDataBase(pmSpDataSource.getDbName());
                        dmConfiguration.setUsername(pmSpDataSource.getUserName());
                        dmConfiguration.setPassword(password);
                        dmConfiguration.setDataBase(pmSpDataSource.getDbName());
                        dmConfiguration.setSchema(pmSpDataSource.getDbName());
                        dmConfiguration.setDataSourceType(DatasourceTypes.dm.getType());
                        datasource.setConfiguration(JSONObject.toJSONString(dmConfiguration));
                        datasource.setType(DatasourceTypes.dm.getType());
                        datasource.setId(projectId+"-"+service);
                        datasourceRequest.setDatasource(datasource);
                        tables = provider.getTables(datasourceRequest);
                        break;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return tables;
    }

    /**
     * 预览表数据
     * @param service
     * @param table
     * @param projectId
     * @return
     */
    public List<Map<String,Object>> queryTableData(String service,String table,String projectId){
        List<Map<String,Object>> dataList=null;
        try {
            LambdaQueryWrapper<PmSpDataSource> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PmSpDataSource::getService, service)
                    .eq(PmSpDataSource::getProjectId, projectId);
            PmSpDataSource pmSpDataSource = this.baseMapper.selectOne(wrapper);
            if (pmSpDataSource != null) {
                String dbType = pmSpDataSource.getDbType();
                Map<String, String> datasourceDetail = JdbcUrlUtils.parseJdbcUrl(pmSpDataSource.getUrl());
                String password = PmAesUtil.decrypt(pmSpDataSource.getUserPwd());
                DatasourceRequest datasourceRequest = new DatasourceRequest();
                Datasource datasource = new Datasource();
                JdbcProvider provider = new JdbcProvider();
                switch (dbType) {
                    case "mysql":
                        MysqlConfiguration mysqlConfiguration = new MysqlConfiguration();
                        mysqlConfiguration.setHost(datasourceDetail.get("host"));
                        mysqlConfiguration.setPort(Integer.parseInt(datasourceDetail.get("port")));
                        mysqlConfiguration.setDataBase(pmSpDataSource.getDbName());
                        mysqlConfiguration.setUsername(pmSpDataSource.getUserName());
                        mysqlConfiguration.setPassword(password);
                        mysqlConfiguration.setDataSourceType(DatasourceTypes.mysql.getType());
                        datasource.setConfiguration(JSONObject.toJSONString(mysqlConfiguration));
                        datasource.setType(DatasourceTypes.mysql.getType());
                        datasource.setId(projectId+"-"+service);
                        datasourceRequest.setDatasource(datasource);
                        datasourceRequest.setQuery("SELECT * FROM "+table+" LIMIT 20");
                        dataList=provider.getDataList(datasourceRequest);
                        break;
                    case "dm":
                        DmConfiguration dmConfiguration=new DmConfiguration();
                        dmConfiguration.setHost(datasourceDetail.get("host"));
                        dmConfiguration.setUsername(pmSpDataSource.getUserName());
                        dmConfiguration.setPassword(password);
                        dmConfiguration.setPort(Integer.parseInt(datasourceDetail.get("port")));
                        dmConfiguration.setDataBase(pmSpDataSource.getDbName());
                        dmConfiguration.setSchema(pmSpDataSource.getDbName());
                        dmConfiguration.setDataSourceType(DatasourceTypes.dm.getType());
                        datasource.setConfiguration(JSONObject.toJSONString(dmConfiguration));
                        datasource.setType(DatasourceTypes.dm.getType());
                        datasource.setId(projectId+"-"+service);
                        datasourceRequest.setDatasource(datasource);
                        if (Character.isDigit(table.charAt(0))) {
                            table = "\"" + table + "\"";
                        }
                        datasourceRequest.setQuery("SELECT * FROM "+table+" LIMIT 20");
                        dataList=provider.getDataList(datasourceRequest);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return dataList;
    }
}
