package com.wcp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.text.PmIdUtils;
import com.wcp.domain.PmSpTeamTree;
import com.wcp.mapper.PmSpTeamTreeMapper;
import com.wcp.service.PmSpTeamTreeService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 1. @Description 团队文件树
 * 2. <AUTHOR>
 * 3. @Date 2025/3/17 14:55
 */
@Service
public class PmSpTeamTreeServiceImple extends ServiceImpl<PmSpTeamTreeMapper, PmSpTeamTree> implements PmSpTeamTreeService {

    /**
     * 新增分组
     * @param pmSpTeamTree
     * @return
     */
    @Override
    public Boolean addGroup(PmSpTeamTree pmSpTeamTree) {
        // 设置创建时间和更新时间
        String id = PmIdUtils.fastSimpleUUID();
        pmSpTeamTree.setId(id);
        pmSpTeamTree.setCreateTime(new Date());
        pmSpTeamTree.setUpdateTime(new Date());
        pmSpTeamTree.setIsDelete(0);//默认正常
        // 插入数据
        return this.save(pmSpTeamTree);
    }

    /**
     * 修改分组
     * @param pmSpTeamTree
     * @return
     */
    @Override
    public Boolean updateGroup(PmSpTeamTree pmSpTeamTree) {
        // 设置创建时间和更新时间
        pmSpTeamTree.setUpdateTime(new Date());
        // 插入数据
        return this.updateById(pmSpTeamTree);
    }
    /**
     * 删除分组
     * @param id
     * @return
     */
    @Override
    public Boolean deleteGroup(String id) {
        return false;
    }
}
