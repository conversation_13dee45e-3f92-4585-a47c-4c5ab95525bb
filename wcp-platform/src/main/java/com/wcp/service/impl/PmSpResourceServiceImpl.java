package com.wcp.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.text.PmUUID;
import com.wcp.domain.PmSpResource;
import com.wcp.execption.ServiceException;
import com.wcp.mapper.PmSpResourceMapper;
import com.wcp.minio.MinioService;
import com.wcp.service.PmSpResourceService;
import com.wcp.utils.ResourceTree;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 1. @Description 平台资源管理器
 * 2. <AUTHOR>
 * 3. @Date 2025/2/26 9:59
 */
@Service
public class PmSpResourceServiceImpl extends ServiceImpl<PmSpResourceMapper, PmSpResource> implements PmSpResourceService {

    /**
     * 新增文件夹树
     * @param pmSpResource
     * @return
     */
    public boolean addResource(PmSpResource pmSpResource){
        pmSpResource.setIsFolder(1);//表示文件夹
        pmSpResource.setResourceType("文件夹");
        pmSpResource.setId(PmUUID.fastUUID().toString(true));
        pmSpResource.setCreateTime(new Date());
        return this.save(pmSpResource);
    }

    /**
     * 修改文件夹名称
     * @param pmSpResource
     * @return
     */
    public int updateResource(PmSpResource pmSpResource){
        return baseMapper.updateById(pmSpResource);
    }

    /**
     * 移动
     * @param jsonArray
     * @param parentId
     * @return
     */
    public boolean moveResource(JSONArray jsonArray,String parentId){
        List<PmSpResource> list=new ArrayList<>();
        if(jsonArray!=null && jsonArray.size()>0){
            jsonArray.forEach(x->{
                PmSpResource pmSpResource=new PmSpResource();
                pmSpResource.setParentId(parentId);
                pmSpResource.setId((String) x);
                list.add(pmSpResource);
            });
            if(list.size()>0){
                return this.updateBatchById(list)?true:false;
            }else{{
                return  false;
            }
            }
        }else{
            throw new PmServiceException("请选择需要移动的文件");
        }

    }


    /**
     * 删除资源
     * @param array
     * @return
     */
    public boolean deleteResource(String projectId, List<String> array) {
        if (array == null || array.isEmpty()) {
            throw new PmServiceException("删除文件ID不能为空");
        }
        for (String id : array) {
            PmSpResource pmSpResource = this.getById(id);
            if (pmSpResource == null) {
                throw new ServiceException("资源不存在");
            }

            // 如果是文件夹，递归删除所有子文件和文件夹
            if (pmSpResource.getIsFolder() == 1) {
                deleteFolderAndChildren(projectId, id); // 递归删除文件夹及其子内容
            } else {
                // 如果是文件，直接删除
                deleteFile(projectId, pmSpResource);
            }
        }

        return true;
    }

    /**
     * 递归删除文件夹及其所有子文件和文件夹
     */
    private void deleteFolderAndChildren(String projectId, String folderId) {
        // 查询所有子文件和文件夹
        QueryWrapper<PmSpResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("PARENT_ID", folderId); // 查询所有 parent_id 等于当前文件夹 ID 的资源
        List<PmSpResource> children = this.list(queryWrapper);
        // 递归删除子文件和文件夹
        if (children != null && !children.isEmpty()) {
            for (PmSpResource child : children) {
                if (child.getIsFolder() == 1) {
                    // 如果是子文件夹，递归删除
                    deleteFolderAndChildren(projectId, child.getId());
                } else {
                    // 如果是子文件，直接删除
                    deleteFile(projectId, child);
                }
            }
        }

        // 删除当前文件夹
        this.removeById(folderId);
    }

    /**
     * 删除文件
     */
    private void deleteFile(String projectId, PmSpResource pmSpResource) {
        String url = "ebi/project/" + projectId + "/" + pmSpResource.getFileUrl();
        try {
            MinioService.removeFile(url); // 删除 MinIO 中的文件
        } catch (Exception e) {
            throw new RuntimeException("删除文件失败: " + url, e);
        }
        this.removeById(pmSpResource.getId()); // 删除数据库记录
    }

    /**
     * 资源管理器左侧文件夹树
     * @return
     */
    public JSONObject getAllFolders(String projectId) {
        QueryWrapper<PmSpResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_FOLDER", 1);
        queryWrapper.eq("PROJECT_ID", projectId);
        List<PmSpResource> list=this.list(queryWrapper);
        List<ResourceTree>  treeNodeList=convertToTreeNodeList(list);
        QueryWrapper<PmSpResource> wrapper = new QueryWrapper<>();
        wrapper.eq("IS_FOLDER", 0) // age > 18
                    .eq("PROJECT_ID", projectId);
        Long fileCount =baseMapper.selectCount(wrapper);
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("treeData",treeNodeList);
        jsonObject.put("folderCount",list.size());
        jsonObject.put("fileCount",fileCount);
        return jsonObject;
    }

    /**
     * 上传文件
     * @param files
     * @param projectId
     * @param folderId
     * @return
     */
    public boolean upload(MultipartFile[] files, String projectId, String folderId) {
        if(files!=null && files.length>0){
            for (MultipartFile file : files) {
                // 获取原始文件名
                String originalFilename = file.getOriginalFilename();
                // 获取文件扩展名
                String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                // 去掉扩展名的纯文件名
                String fileNameWithoutExtension = originalFilename.substring(0, originalFilename.lastIndexOf("."));

                // 查询数据库中是否已存在同名文件
                QueryWrapper<PmSpResource> wrapper = new QueryWrapper<>();
                wrapper.eq("RESOURCE_NAME", originalFilename)
//                        .eq("PARENT_ID", folderId)
                        .eq("PROJECT_ID", projectId);
                Long count = baseMapper.selectCount(wrapper);
                // 如果文件名重复，追加 (1)、(2) 等后缀
                String uniqueFileName = originalFilename;
                if (count > 0) {
                    int suffix = 1;
                    while (true) {
                        // 构造新文件名：原文件名 + (suffix) + 扩展名
                        uniqueFileName = fileNameWithoutExtension + "(" + suffix + ")" + fileExtension;
                        // 检查新文件名是否重复
                        wrapper.clear(); // 清空之前的条件
                        wrapper.eq("RESOURCE_NAME", uniqueFileName)
                                .eq("PARENT_ID", folderId)
                                .eq("PROJECT_ID", projectId);
                        Long newCount = baseMapper.selectCount(wrapper);
                        if (newCount == 0) {
                            break; // 如果新文件名不重复，退出循环
                        }
                        suffix++; // 否则继续递增后缀
                    }
                }
                // 生成唯一文件名（用于存储）
                String storageFileName = uniqueFileName;
                // 构造资源对象
                PmSpResource pmSpResource = new PmSpResource();
                pmSpResource.setId(PmUUID.fastUUID().toString(true));
                pmSpResource.setParentId(folderId);
                pmSpResource.setProjectId(projectId);
                pmSpResource.setResourceName(uniqueFileName); // 使用唯一的文件名
                pmSpResource.setResourceType(fileExtension);
                pmSpResource.setIsFolder(0);
                pmSpResource.setFileSize(String.valueOf(file.getSize()));
                pmSpResource.setCreateTime(new Date());
                String url = "ebi/project/" + projectId + "/" + storageFileName;
                pmSpResource.setFileUrl(storageFileName);
                // 上传文件到 MinIO
                MinioService.uploadFile(url, file);
                // 保存资源信息到数据库
                this.save(pmSpResource);
            }
            return true;
        }else{
            throw new PmServiceException("上传的文件不能为空");
        }
    }
    /**
     * 获取文件夹子集
     * @param projectId
     * @param folderId
     * @return
     */
    public List<PmSpResource> getChildrenFile(String projectId,String folderId,String sortColumn,String sortOrder){
        QueryWrapper<PmSpResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("PROJECT_ID", projectId);
        queryWrapper.eq("PARENT_ID", folderId);
        // 设置排序规则
        if (StringUtils.isBlank(sortColumn) || StringUtils.isBlank(sortOrder)) {
            // 如果 sortColumn 或 sortOrder 为空，默认按 CREATE_TIME 倒序
            queryWrapper.orderByDesc("CREATE_TIME");
        } else {
            // 否则按照传入的参数排序
            boolean isAsc = "ASC".equalsIgnoreCase(sortOrder);
            queryWrapper.orderBy(true, isAsc, sortColumn);
        }
        List<PmSpResource> list=baseMapper.selectList(queryWrapper);
        if(list!=null){
            list.forEach(t->{
                if(t.getIsFolder()==0){
                    t.setFileSize(formatSize(Long.parseLong(t.getFileSize())));
                }

            });
        }
        return list;
    }

    public String formatSize(long size) {
        if (size < 1024) {
            return size + " bytes";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 树形结构数据转换
     * @param pmSpResourceList
     * @return
     */
    public  List<ResourceTree> convertToTreeNodeList(List<PmSpResource> pmSpResourceList) {
        List<ResourceTree> treeNodeList = new ArrayList<>();
        for (PmSpResource pmSpResource : pmSpResourceList) {
            if (pmSpResource.getParentId() == null || "".equals(pmSpResource.getParentId())) {
                ResourceTree treeNode = convertToTreeNode(pmSpResource, pmSpResourceList);
                treeNodeList.add(treeNode);
            }
        }
        return treeNodeList;
    }

    /**
     * 递归设置属性文件夹的层级
     * @param pmGcNodeList
     * @return
     */
    public static ResourceTree convertToTreeNode(PmSpResource pmSpResource, List<PmSpResource> pmGcNodeList) {
        ResourceTree treeNode = new ResourceTree();
        treeNode.setId(pmSpResource.getId());
        treeNode.setNodeName(pmSpResource.getResourceName());
     //   treeNode.setNodeType(pmSpResource.getResourceType());
        //格式化时间
        treeNode.setParentId(pmSpResource.getParentId());
        List<ResourceTree> children = new ArrayList<>();
        for (PmSpResource childNode : pmGcNodeList) {
            //如果父节点不为空，递归调用
            if (childNode.getParentId() != null && childNode.getParentId().equals(pmSpResource.getId())) {
                ResourceTree childTreeNode = convertToTreeNode(childNode, pmGcNodeList);
                children.add(childTreeNode);
            }
        }
        treeNode.setChildren(children);
        return treeNode;
    }
    /**
     * 复制文件夹
     */
    private boolean copyFolder(String projectId, PmSpResource folder) {
        // 创建新文件夹
        PmSpResource newFolder = createCopyResource(folder);
        newFolder.setParentId(folder.getParentId()); // 设置父文件夹 ID
        this.save(newFolder);

        // 查询文件夹下的所有子文件和子文件夹
        QueryWrapper<PmSpResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("PARENT_ID", folder.getId());
        queryWrapper.eq("PROJECT_ID", projectId);
        List<PmSpResource> children = this.list(queryWrapper);

        // 递归复制子文件和子文件夹
        for (PmSpResource child : children) {
            if (child.getIsFolder() == 1) {
                copyFolder(projectId, child); // 递归复制子文件夹
            } else {
                copyFile(projectId, child); // 复制子文件
            }
        }

        return true;
    }

    /**
     * 复制文件
     */
    private boolean copyFile(String projectId, PmSpResource file) {
        // 生成新的文件名（避免冲突）
        String newFileName = generateUniqueFileName(projectId, file.getResourceName(), file.getParentId());

        // 创建新文件记录
        PmSpResource newFile = createCopyResource(file);
        newFile.setResourceName(newFileName); // 设置新文件名
        newFile.setFileUrl(generateUniqueFileUrl(projectId, file.getFileUrl())); // 设置新文件路径
        this.save(newFile);

        // 在 MinIO 中复制文件
        String sourcePath = "ebi/project/" + projectId + "/" + file.getFileUrl();
        String targetPath = "ebi/project/" + projectId + "/" + newFile.getFileUrl();
        MinioService.copyObject(sourcePath, targetPath);

        return true;
    }
    /**
     * 生成唯一的文件名
     */
    private String generateUniqueFileName(String projectId, String originalFileName, String parentId) {
        String baseName = originalFileName.replaceFirst("[.][^.]+$", ""); // 去掉扩展名
        String extension = originalFileName.substring(originalFileName.lastIndexOf(".")); // 获取扩展名

        int suffix = 1;
        String newFileName = originalFileName;
        while (true) {
            // 检查文件名是否已存在
            QueryWrapper<PmSpResource> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("PROJECT_ID", projectId);
        //    queryWrapper.eq("PARENT_ID", parentId);
            queryWrapper.eq("RESOURCE_NAME", newFileName);
            Long count = this.baseMapper.selectCount(queryWrapper);

            if (count == 0) {
                break; // 文件名唯一，退出循环
            }

            // 生成新的文件名：原文件名 + "-副本(suffix)" + 扩展名
            newFileName = baseName + "-副本(" + suffix + ")" + extension;
            suffix++;
        }

        return newFileName;
    }
    /**
     * 生成唯一的文件路径
     */
    private String generateUniqueFileUrl(String projectId, String originalFileUrl) {
        String baseName = originalFileUrl.replaceFirst("[.][^.]+$", ""); // 去掉扩展名
        String extension = originalFileUrl.substring(originalFileUrl.lastIndexOf(".")); // 获取扩展名

        int suffix = 1;
        String newFileUrl = originalFileUrl;
        while (true) {
            // 检查文件路径是否已存在
            QueryWrapper<PmSpResource> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("PROJECT_ID", projectId);
            queryWrapper.eq("FILE_URL", newFileUrl);
            Long count = this.baseMapper.selectCount(queryWrapper);

            if (count == 0) {
                break; // 文件路径唯一，退出循环
            }

            // 生成新的文件路径：原路径 + "-副本(suffix)" + 扩展名
            newFileUrl = baseName + "-副本(" + suffix + ")" + extension;
            suffix++;
        }

        return newFileUrl;
    }
    /**
     * 创建复制资源的副本
     */
    private PmSpResource createCopyResource(PmSpResource resource) {
        PmSpResource newResource = new PmSpResource();
        newResource.setId(PmUUID.fastUUID().toString(true)); // 生成新的 ID
        newResource.setProjectId(resource.getProjectId());
        newResource.setParentId(resource.getParentId());
        newResource.setResourceName(resource.getResourceName() + " - 副本");
        newResource.setResourceType(resource.getResourceType());
        newResource.setIsFolder(resource.getIsFolder());
        newResource.setFileSize(resource.getFileSize());
        newResource.setCreateTime(new Date());
        newResource.setFileUrl(resource.getFileUrl() + "-copy"); // 新文件的路径
        return newResource;
    }

    /**
     * 复制资源
     * @param projectId
     * @param id
     * @return
     */
    public boolean copyResource(String projectId,String id){
        QueryWrapper<PmSpResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", id);
        queryWrapper.eq("PROJECT_ID", projectId);
        PmSpResource pmSpResource=this.getOne(queryWrapper);
        //如果是文件夹
        if(pmSpResource!=null && pmSpResource.getIsFolder()==1){
            return copyFolder(projectId, pmSpResource);
        }else{
            //复制文件
            // 如果是文件
            return copyFile(projectId, pmSpResource);
        }
    }
}
