package com.wcp.service.impl;
import com.wcp.domain.PlmRole;
import com.wcp.mapper.PlatfromRoleMapper;
import com.wcp.service.IPlatfromRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Description: 平台角色业务
 * Author: qianchao
 * Date: 2024/1/10 11:59
 */
@Service
public class PlatfromRoleServiceImpl implements IPlatfromRoleService {
    @Autowired
    private PlatfromRoleMapper platfromRoleMapper;

    /**
     * 查询用户角色
     * @param userId
     * @return
     */
    public Set<PlmRole> selectRolesByUserId(String userId) {
        // 执行查询
        Set<PlmRole> perms = platfromRoleMapper.selectRolePermissionByUserId(userId);
        return perms;
    }
}
