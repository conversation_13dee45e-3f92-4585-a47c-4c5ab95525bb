package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.text.PmStringUtils;
import com.wcp.core.text.PmUUID;
import com.wcp.domain.PmSpProjectFile;
import com.wcp.domain.dto.PmSpProjectFileDto;
import com.wcp.mapper.PmSpProjectFileMapper;
import com.wcp.minio.MinioService;
import com.wcp.service.PmSpProjectFileService;
import com.wcp.utils.GzipCompress;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 1. @Description 项目资源管理文件
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 13:43
 */
@Service
public class PmSpProjectFileServiceImpl extends ServiceImpl<PmSpProjectFileMapper, PmSpProjectFile> implements PmSpProjectFileService {

    /**
     * 修改项目资源
     * @param file
     * @param projectId
     * @param pmSpProjectFile
     * @return
     */
    @Override
    public int updateFile(MultipartFile file,String projectId, PmSpProjectFile pmSpProjectFile) {
        try {
            LambdaQueryWrapper<PmSpProjectFile> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(PmSpProjectFile::getId, pmSpProjectFile.getId());
            PmSpProjectFile pf=baseMapper.selectOne(queryWrapper);
            // 如果 file 不为空，则进行文件替换
            if (file != null && !file.isEmpty()) {
                if(pf!=null){
                    //缩略图地址
                    String thumbnailPath =pf.getThumbnailPath();
                    //删除minio缩略图地址
                    MinioService.removeFile(thumbnailPath);
                }
                // 获取原始文件名
                String originalFilename = file.getOriginalFilename();
                // 获取文件扩展名
                String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                // 生成唯一文件名
                String uniqueFileName = PmUUID.fastUUID().toString(true)+ fileExtension;
                String url="ebi/project/"+projectId+"/"+uniqueFileName;
                MinioService.uploadFile(url,file);
                pmSpProjectFile.setThumbnailPath(url);
            }
            pmSpProjectFile.setUserAccount(SecurityUtils.getLoginUser().getUser().getAccount());
            if(PmStringUtils.isNotEmpty(pmSpProjectFile.getGraphData())){
                //图元内容进行压缩
                String graphData=pmSpProjectFile.graphData.toJSONString();
                String graphCell= GzipCompress.compress(graphData,"UTF-8");
                // 将字符串转换为字节数组，使用 UTF-8 编码
                byte[] bytes = graphCell.getBytes(StandardCharsets.UTF_8);
                pmSpProjectFile.setGraphCell(bytes);
            }
            // 保存到数据库
            return baseMapper.updateById(pmSpProjectFile);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 保存项目资源
     * @param file
     * @param projectId
     * @param pmSpProjectFile
     * @return
     */
    @Override
    public int uploadFile(MultipartFile file,String projectId, PmSpProjectFile pmSpProjectFile) {
        try {
            String originalFilename = file.getOriginalFilename();
            String url="ebi/project/"+projectId+"/"+originalFilename;
            MinioService.uploadFile(url,file);
            pmSpProjectFile.setId(PmUUID.fastUUID().toString(true)); // 生成唯一 ID
            pmSpProjectFile.setCreateTime(new Date());
            pmSpProjectFile.setThumbnailPath(url);
            pmSpProjectFile.setUserAccount(SecurityUtils.getLoginUser().getUser().getAccount());
            if(PmStringUtils.isNotEmpty(pmSpProjectFile.getGraphData())){
                //图元内容进行压缩
                String graphData=pmSpProjectFile.graphData.toJSONString();
                String graphCell= GzipCompress.compress(graphData,"UTF-8");
                // 将字符串转换为字节数组，使用 UTF-8 编码
                byte[] bytes = graphCell.getBytes(StandardCharsets.UTF_8);
                pmSpProjectFile.setGraphCell(bytes);
            }
            // 保存到数据库
            return baseMapper.insert(pmSpProjectFile);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 查询资源列表
     * @param id
     * @return
     */
    public List<PmSpProjectFileDto> queryFileList(String id){
        List<PmSpProjectFileDto> list=baseMapper.selectFileList(id);
        return list;
    }

    /**
     * 删除单个资源文件
     * @param id
     * @return
     */
    public int deleteFile(String id) {
        PmSpProjectFile pmSpProjectFile = baseMapper.selectOne(
                new LambdaQueryWrapper<PmSpProjectFile>()
                        .eq(PmSpProjectFile::getId, id)
        );
        if(pmSpProjectFile!=null && PmStringUtils.isNotEmpty(pmSpProjectFile.getThumbnailPath())){
            try {
                MinioService.removeFile(pmSpProjectFile.getThumbnailPath());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
          return   baseMapper.deleteById(pmSpProjectFile);
        }
        return 0;
    }

    /**
     * 查询图元详情
     * @param id
     * @param encoding
     * @return
     * @throws IOException
     */
    public Object queryDetail(String id,String encoding)throws IOException {
        PmSpProjectFile pmSpProjectFile = baseMapper.selectOne(
                new LambdaQueryWrapper<PmSpProjectFile>()
                        .eq(PmSpProjectFile::getId, id)
        );
        if(pmSpProjectFile!=null){
            byte[] graphCell=pmSpProjectFile.getGraphCell();
            String graphData = new String(graphCell, StandardCharsets.UTF_8);
            //如果encodng是gzip就解压，就返回压缩的，如果是json就是需要解压
            return  "gzip".equals(encoding)?graphData: GzipCompress.uncompress(graphData);
        }
        return null;
    }
}
