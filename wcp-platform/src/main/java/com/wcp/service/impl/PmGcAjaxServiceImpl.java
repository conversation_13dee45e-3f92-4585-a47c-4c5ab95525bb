package com.wcp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.domain.PmGcRequest;
import com.wcp.mapper.PmGcRequestMapper;
import com.wcp.service.PmGcAjaxService;
import com.wcp.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 1. @Description 数据接口service
 * 2. <AUTHOR>
 * 3. @Date 2025/2/14 10:52
 */
@Service
public class PmGcAjaxServiceImpl  extends ServiceImpl<PmGcRequestMapper, PmGcRequest>  implements PmGcAjaxService {
    /**
     * 接口列表查询
     * @param projectId
     * @return
     */
    @Override
    public List<JSONObject> ajaxList(String projectId) {

        LambdaQueryWrapper<PmGcRequest> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(PmGcRequest::getProjectId, projectId).select(PmGcRequest::getSetting, PmGcRequest::getId);
        List<PmGcRequest> ajaxList=baseMapper.selectList(wrapper);
        List<JSONObject> list=new ArrayList<>();
        for (PmGcRequest pm:ajaxList) {
            JSONObject tmp=JSONObject.parseObject(pm.getSetting());
            tmp.put("id",pm.getId());
            list.add(tmp);
        }
        return list;
    }

    /**
     * 报错接口
     * @param pmGcAjax
     * @return
     */
    @Override
    public int ajaxSave(PmGcRequest pmGcAjax) {
        if (StrUtil.isEmpty(pmGcAjax.getId())) {
            pmGcAjax.setId(StringUtil.generateUUID());
        }
        pmGcAjax.setCreateTime(new Date());
        baseMapper.deleteById(pmGcAjax.getId());
        return baseMapper.insert(pmGcAjax);
    }

    /**
     * 接口删除
     * @param pmGcAjax
     * @return
     */
    @Override
    public int ajaxDelete(PmGcRequest pmGcAjax) {
        return baseMapper.deleteById(pmGcAjax.getId());
    }
}
