package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.text.PmUUID;
import com.wcp.domain.PmSpProjectFile;
import com.wcp.domain.PmSpProjectResource;
import com.wcp.domain.dto.PmSpProjectResourceTreeDto;
import com.wcp.mapper.PmSpProjectFileMapper;
import com.wcp.mapper.PmSpProjectResourceMapper;
import com.wcp.minio.MinioService;
import com.wcp.service.PmSpProjectResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 1. @Description 项目资源管理器
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 13:41
 */
@Service
public class PmSpProjectResourceImpl extends ServiceImpl<PmSpProjectResourceMapper, PmSpProjectResource> implements PmSpProjectResourceService {

    @Autowired
    private PmSpProjectFileMapper pmSpProjectFileMapper;
    /**
     * 新增文件夹
     * @param resource
     * @return
     */
    @Override
    public int addResource(PmSpProjectResource resource,String projectId) {
        // 设置创建时间
        resource.setId(PmUUID.fastUUID().toString(true));
        resource.setCreateTime(new Date());
        resource.setProjectId(projectId);
        // 插入资源
        return baseMapper.insert(resource);
    }
    @Override
    public int updateResource(PmSpProjectResource resource) {
        // 更新资源
        return baseMapper.updateById(resource);
    }
    @Override
    public int deleteResource(String id) {
        // 递归删除子资源
        deleteChildResources(id);
        // 删除当前资源
        return baseMapper.deleteById(id);
    }

    /**
     * 构建文件夹树
     * @param projectId
     * @return
     */
    @Override
    public List<PmSpProjectResourceTreeDto> getResourceTree(String projectId) {
        // 查询项目下的所有资源
        List<PmSpProjectResource> resources =baseMapper.selectList(
                new LambdaQueryWrapper<PmSpProjectResource>()
                        .eq(PmSpProjectResource::getProjectId, projectId)
        );
        //如果没有文件夹，就插入一条插入的数据
        if(resources==null || resources.size()==0){
            PmSpProjectResource pmSpProjectResource=new PmSpProjectResource();
            pmSpProjectResource.setId(PmUUID.fastUUID().toString(true));
            pmSpProjectResource.setCreateTime(new Date());
            pmSpProjectResource.setResourceName("默认");
            pmSpProjectResource.setProjectId(projectId);
            baseMapper.insert(pmSpProjectResource);
            resources.add(pmSpProjectResource);
        }
        // 将实体列表转换为 DTO 列表
        List<PmSpProjectResourceTreeDto> dtos = resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        // 构建文件树
        Map<String, PmSpProjectResourceTreeDto> dtoMap = new HashMap<>();
        List<PmSpProjectResourceTreeDto> rootResources = new ArrayList<>();
        // 将所有 DTO 放入 Map
        for (PmSpProjectResourceTreeDto dto : dtos) {
            dtoMap.put(dto.getId(), dto);
        }
        // 构建树结构
        for (PmSpProjectResourceTreeDto dto : dtos) {
            if (dto.getParentId() == null) {
                rootResources.add(dto); // 根节点
            } else {
                PmSpProjectResourceTreeDto parent = dtoMap.get(dto.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(dto); // 添加到父节点的子节点列表
                }
            }
        }
        return rootResources;
    }



    /**
     * 将实体类转换为 DTO
     */
    private PmSpProjectResourceTreeDto convertToDTO(PmSpProjectResource resource) {
        PmSpProjectResourceTreeDto dto = new PmSpProjectResourceTreeDto();
        dto.setId(resource.getId());
        dto.setResourceName(resource.getResourceName());
        dto.setParentId(resource.getParentId());
        dto.setCreateTime(resource.getCreateTime());
        return dto;
    }

    /**
     * 递归删除子资源
     */
    @Transactional
    public void deleteChildResources(String parentId) {
        List<PmSpProjectResource> children = baseMapper.selectList(
                new LambdaQueryWrapper<PmSpProjectResource>()
                        .eq(PmSpProjectResource::getParentId, parentId)
        );
        if(children!=null && children.size()>0){
            // 遍历子资源
            for (PmSpProjectResource child : children) {
                // 删除当前资源
                baseMapper.deleteById(child.getId());
                //删除文件资源
                LambdaQueryWrapper<PmSpProjectFile> queryWrapper=new LambdaQueryWrapper<>();
                queryWrapper.eq(PmSpProjectFile::getParentId, child.getId());
                List<PmSpProjectFile> pmSpProjectFiles=pmSpProjectFileMapper.selectList(queryWrapper);
                //如果不为空，需要遍历删除minio上的文件
                if(pmSpProjectFiles!=null && pmSpProjectFiles.size()>0){
                    //分片删除
                    pmSpProjectFiles.parallelStream().forEach(t->{
                        try {
                            MinioService.removeFile(t.getThumbnailPath());
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                    //删除完成再删除数据库数据
                    pmSpProjectFileMapper.delete(queryWrapper);
                }
                //再递归一次,如果查询不到数据就结束
                deleteChildResources(child.getId());
            }
        }
    }
}
