package com.wcp.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.domain.PmGcTopology;
import com.wcp.domain.PmSpProjectPrefix;
import com.wcp.mapper.PmGcTopologyMapper;
import com.wcp.service.PmGcTopologyService;
import com.wcp.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 1. @Description 拓扑service
 * 2. <AUTHOR>
 * 3. @Date 2025/6/23 14:05
 */
@Service
public class PmGcTopologyServiceImpl extends ServiceImpl<PmGcTopologyMapper, PmGcTopology> implements PmGcTopologyService {

    /**
     * 查询该项目下的拓扑图(不包含data)
     *
     * @param projectId
     * @return
     */
    @Override
    public List<PmGcTopology> queryTopologyIdxs(String projectId) {
        LambdaQueryWrapper<PmGcTopology> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmGcTopology::getProjectId, projectId)
                .select(PmGcTopology::getProjectId, PmGcTopology::getId, PmGcTopology::getName)
                .orderByAsc(PmGcTopology::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 查询该项目下的拓扑图
     *
     * @param projectId
     * @return
     */
    @Override
    public List<PmGcTopology> queryTopologyList(String projectId) {
        LambdaQueryWrapper<PmGcTopology> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmGcTopology::getProjectId, projectId)
                .select(PmGcTopology::getProjectId, PmGcTopology::getId, PmGcTopology::getName,
                        PmGcTopology::getData, PmGcTopology::getCreateTime, PmGcTopology::getAccount)
                .orderByAsc(PmGcTopology::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 根据项目id和拓扑id查询拓扑图
     *
     * @param projectId
     * @param idArr
     * @return
     */
    @Override
    public List<PmGcTopology> queryTopologyById(String projectId, String[] idArr) {
        LambdaQueryWrapper<PmGcTopology> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmGcTopology::getProjectId, projectId)
                .in(PmGcTopology::getId, idArr)
                .select(PmGcTopology::getProjectId, PmGcTopology::getId, PmGcTopology::getName,
                        PmGcTopology::getData, PmGcTopology::getCreateTime, PmGcTopology::getAccount)
                .orderByAsc(PmGcTopology::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 拓扑图保存
     * @param pmGcTopology
     * @return
     */
    @Override
    public int saveTopology(PmGcTopology pmGcTopology) {
        pmGcTopology.setCreateTime(new Date());
        if (StrUtil.isEmpty(pmGcTopology.getId())){
            pmGcTopology.setId(StringUtil.generateUUID());
        }

        // 根据ID查询数据库
        PmGcTopology existingRecord = baseMapper.selectById(pmGcTopology.getId());

        // 判断记录是否存在，存在则更新，否则插入
        if (existingRecord != null) {
            // 执行更新操作
            return baseMapper.updateById(pmGcTopology);
        } else {
            // 执行插入操作
            return baseMapper.insert(pmGcTopology);
        }
    }

    /**
     * 拓扑图删除
     * @param projectId
     * @param ids
     * @return
     */
    @Override
    public int deleteTopologyByIds(String projectId, List<String> ids) {
        QueryWrapper<PmGcTopology> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids)
                .eq("project_id", projectId);

        return baseMapper.delete(queryWrapper);
    }
}
