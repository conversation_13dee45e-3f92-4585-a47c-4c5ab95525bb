package com.wcp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.execption.PmServiceException;
import com.wcp.domain.PmTeamUser;
import com.wcp.domain.dto.ProjectUserDto;
import com.wcp.enums.PermissionEnum;
import com.wcp.enums.UserRoleEnum;
import com.wcp.mapper.PmTeamUserMapper;
import com.wcp.service.PmTeamUserService;
import com.wcp.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PmTeamUserServiceImpl extends ServiceImpl<PmTeamUserMapper, PmTeamUser> implements PmTeamUserService {

    @Autowired
    private PmTeamUserMapper teamUserMapper;

    public List<ProjectUserDto> queryTeamUserById(String projectId) throws PmServiceException {
        if (StringUtil.isEmpty(projectId)) {
            throw new PmServiceException("项目ID不能为空！请重新输入。");
        }
        List<ProjectUserDto> projectUserDtoList=teamUserMapper.queryWorkTeamById(projectId);
        if(projectUserDtoList!=null && projectUserDtoList.size()>0){
            projectUserDtoList.forEach(t->{
                t.setUserRole(UserRoleEnum.fromCode(Integer.parseInt(t.getUserRole())).getName());
                t.setPermission(PermissionEnum.fromCode(Integer.parseInt(t.getPermission())).getName());
            });
        }
        return projectUserDtoList;
    }


    /**
     * 当前用户退出团队
     * @param pmTeamUser
     * @return
     */
    public boolean exit(PmTeamUser pmTeamUser) {
        int result = teamUserMapper.exitByUserIdAndTeamId(pmTeamUser.getUserId(), pmTeamUser.getTeamId());
        return result > 0;
    }
}
