package com.wcp.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.text.PmIdUtils;
import com.wcp.domain.*;
import com.wcp.execption.ServiceException;
import com.wcp.mapper.*;
import com.wcp.minio.MinioService;
import com.wcp.service.ProjectExporterService;
import com.wcp.utils.DbUtils;
import com.wcp.utils.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.web.multipart.MultipartFile;

/**
 * 1. @Description 项目导出
 * 2. <AUTHOR>
 * 3. @Date 2025/3/24 17:36
 */
@Service
public class ProjectExporterServiceImpl implements ProjectExporterService {

    @Autowired
    private PmTeamProjectMapper pmTeamProjectMapper;
    @Autowired
    private PmGcNodeMapper pmGcNodeMapper;

    @Autowired
    private PmGcDataMapper pmGcDataMapper;

    @Autowired
    private PmGcPageMapper pmGcPageMapper;

    @Autowired
    private PmGcRequestMapper pmGcRequestMapper;

    @Autowired
    private PmSpResourceMapper pmSpResourceMapper;

    @Autowired
    private PmIconGroupMapper pmIconGroupMapper;

    @Autowired
    private PmSpIconMapper pmSpIconMapper;
    @Value("${wcp.project-path}")
    private  String projectPath;

    private static final ObjectMapper objectMapper = new ObjectMapper();
    public  Path exportProject(String projectId) {
        try {
            // 创建导出目录
            Files.createDirectories(Path.of(projectPath+"/"+projectId));

            // 查询项目信息
            PmTeamProject pmTeamProject = pmTeamProjectMapper.selectById(projectId);
            //查询图元树
            QueryWrapper<PmGcNode> pmGcNodeWrapper=new QueryWrapper<>();
            pmGcNodeWrapper.eq("PROJECT_ID",projectId);
            List<PmGcNode> pmGcNodeList = pmGcNodeMapper.selectList(pmGcNodeWrapper);

            //查询图元内容
            QueryWrapper<PmGcData> pmGcDataWrapper=new QueryWrapper<>();
            pmGcDataWrapper.eq("PROJECT_ID",projectId);
            List<PmGcData> pmGcDataList = pmGcDataMapper.selectList(pmGcDataWrapper);

            //查询图元路由
            QueryWrapper<PmGcPage> pmGcPagerapper=new QueryWrapper<>();
            pmGcPagerapper.eq("PROJECT_ID",projectId);
            List<PmGcPage> pmGcPageList = pmGcPageMapper.selectList(pmGcPagerapper);

            //查询图元接口
            QueryWrapper<PmGcRequest> pmGcRequestwrapper=new QueryWrapper<>();
            pmGcRequestwrapper.eq("PROJECT_ID",projectId);
            List<PmGcRequest> pmGcRequestList = pmGcRequestMapper.selectList(pmGcRequestwrapper);

            QueryWrapper<PmSpResource> pmSpResourceWrapper=new QueryWrapper<>();
            pmSpResourceWrapper.eq("PROJECT_ID",projectId);
            List<PmSpResource> PmSpResourceList =pmSpResourceMapper.selectList(pmSpResourceWrapper);

            //图标分组
            QueryWrapper<PmSpIconGroup> pmSpIconGroupWrapper=new QueryWrapper<>();
            List<PmSpIconGroup> pmIconGroupList =pmIconGroupMapper.selectList(pmSpIconGroupWrapper);
            //查询图标
            QueryWrapper<PmSpIcon> pmSpIconWrapper=new QueryWrapper<>();
            List<PmSpIcon> PmSpIconList =pmSpIconMapper.selectList(pmSpIconWrapper);

            // 2. 序列化数据为 JSON
            File projectFile = new File(projectPath+"/"+projectId, "项目信息.json");
            objectMapper.writeValue(projectFile, pmTeamProject);

            // 2. 序列化数据为 JSON
            File pmGcNodeFile = new File(projectPath+"/"+projectId, "图元树.json");
            objectMapper.writeValue(pmGcNodeFile, pmGcNodeList);

            File pmGcDataFile = new File(projectPath+"/"+projectId, "图元.json");
            objectMapper.writeValue(pmGcDataFile, pmGcDataList);

            File pmGcPageFile = new File(projectPath+"/"+projectId, "路由.json");
            objectMapper.writeValue(pmGcPageFile, pmGcPageList);

            File pmGcRequestFile = new File(projectPath+"/"+projectId, "接口.json");
            objectMapper.writeValue(pmGcRequestFile, pmGcRequestList);

            File pmSpResourceFile = new File(projectPath+"/"+projectId, "资源管理器.json");
            objectMapper.writeValue(pmSpResourceFile, PmSpResourceList);

            File pmIconGroupFile = new File(projectPath+"/"+projectId, "图标分组.json");
            objectMapper.writeValue(pmIconGroupFile, pmIconGroupList);
            File pmIconFile = new File(projectPath+"/"+projectId, "图标.json");
            objectMapper.writeValue(pmIconFile, PmSpIconList);

            //下载资源管理器
            String folderPath = projectPath+"/"+projectId+"/resource";
            JSONArray jsonArray =MinioService.queryFiles("ebi/project/"+projectId);
            MinioService.downloadFiles(jsonArray,folderPath);
            //压缩
            Path exportDir = Path.of(projectPath, projectId);
            Path zipPath = Path.of(projectPath, pmTeamProject.getProjectName() + ".zip");
            zipDirectory(exportDir,zipPath);
            //压缩完成后删除压缩前的文件夹
            deleteDirectory(exportDir);
            return zipPath;
        } catch (Exception e) {
            e.printStackTrace();
        }
       return null;
    }
    //删除文件夹
    public static void deleteDirectory(Path path) throws IOException {
        if (Files.exists(path)) {
            Files.walk(path)                             // 递归遍历目录
                    .sorted(Comparator.reverseOrder())     // 先删子文件，再删父目录
                    .map(Path::toFile)
                    .forEach(file -> {
                        if (!file.delete()) {
                            System.err.println("警告：删除失败 " + file.getAbsolutePath());
                        }
                    });
        }
    }
    // 打包导出目录为 zip 文件
    public static void zipDirectory(Path sourceDirPath, Path zipFilePath) throws IOException {
        try (ZipOutputStream zs = new ZipOutputStream(Files.newOutputStream(zipFilePath))) {
            Files.walk(sourceDirPath)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        ZipEntry zipEntry = new ZipEntry(sourceDirPath.relativize(path).toString());
                        try {
                            zs.putNextEntry(zipEntry);
                            Files.copy(path, zs);
                            zs.closeEntry();
                        } catch (IOException e) {
                            throw new RuntimeException("压缩失败: " + path, e);
                        }
                    });
        }
    }

    //解压
    public static void unzip(Path zipFilePath, Path destDir) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(zipFilePath))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                Path newPath = resolveZipEntry(destDir, entry);
                if (entry.isDirectory()) {
                    Files.createDirectories(newPath);
                } else {
                    Files.createDirectories(newPath.getParent()); // 确保父目录存在
                    Files.copy(zis, newPath, StandardCopyOption.REPLACE_EXISTING);
                }
                zis.closeEntry();
            }
        }
        // 解压完成后删除 zip 文件
        try {
            Files.deleteIfExists(zipFilePath);
        } catch (IOException e) {
            System.err.println("警告：无法删除 ZIP 文件：" + zipFilePath);
            e.printStackTrace();
        }
    }
    private static Path resolveZipEntry(Path destDir, ZipEntry entry) throws IOException {
        Path resolvedPath = destDir.resolve(entry.getName()).normalize();
        if (!resolvedPath.startsWith(destDir)) {
            throw new IOException("不安全的ZIP路径: " + entry.getName());
        }
        return resolvedPath;
    }
    /**
     * 导入项目
     * @param multipartFile
     */
    public String importProject(MultipartFile multipartFile){
        try {
            //解压文件
            // 获取原始文件名
            String originalFilename = multipartFile.getOriginalFilename();
            // 保存文件路径
            Path savePath = Path.of(projectPath, originalFilename);
            // 保存文件
            multipartFile.transferTo(savePath.toFile());
            //解压文件
            String baseName = originalFilename != null && originalFilename.contains(".")
                    ? originalFilename.substring(0, originalFilename.lastIndexOf('.'))
                    : originalFilename;
            Path extractDir = Path.of(projectPath, baseName);
            unzip(savePath,extractDir);
            String path = projectPath + "/" + baseName + "/";
            String projectId="";
            PmTeamProject pmTeamProject = objectMapper.readValue(new File(path + "项目信息.json"), new TypeReference<>() {});
            if(pmTeamProject!=null){
                projectId=pmTeamProject.getProjectId();
                List<PmTeamProject> list=new ArrayList<>();
                list.add(pmTeamProject);
                DbUtils.insertOrUpdateBatch(list,pmTeamProjectMapper, Arrays.asList("projectId"));
            }
            List<PmGcNode> nodes = objectMapper.readValue(new File(path + "图元树.json"), new TypeReference<>() {});
            if(nodes!=null && nodes.size()>0){
              /*  for (PmGcNode node : nodes) {
                    node.setProjectId(projectId);
                }*/
                DbUtils.insertOrUpdateBatch(nodes,pmGcNodeMapper, Arrays.asList("projectId", "pid"));
            }

            List<PmGcData> datas = objectMapper.readValue(new File(path + "图元.json"), new TypeReference<>() {});
            if(datas!=null && datas.size()>0){
               /* for (PmGcData data : datas) {
                    data.setProjectId(projectId);
                }*/
                DbUtils.insertOrUpdateBatch(datas,pmGcDataMapper, Arrays.asList("graphId"));
            }

            List<PmGcPage> pages = objectMapper.readValue(new File(path + "路由.json"), new TypeReference<>() {});
            if(pages!=null && pages.size()>0){
              /*  for (PmGcPage page : pages) {
                    page.setProjectId(projectId);
                }*/
                DbUtils.insertOrUpdateBatch(pages,pmGcPageMapper, Arrays.asList("projectId", "id"));
            }

            List<PmGcRequest> requests = objectMapper.readValue(new File(path + "接口.json"), new TypeReference<>() {});
            if(requests!=null && requests.size()>0){
               /* for (PmGcRequest request : requests) {
                    request.setProjectId(projectId);
                }*/
                DbUtils.insertOrUpdateBatch(requests,pmGcRequestMapper, Arrays.asList("projectId", "id"));
            }

            List<PmSpResource> resources = objectMapper.readValue(new File(path + "资源管理器.json"), new TypeReference<>() {});
            if(resources!=null && resources.size()>0){
               /* for (PmSpResource resource : resources) {
                    resource.setProjectId(projectId);
                }*/
                DbUtils.insertOrUpdateBatch(resources,pmSpResourceMapper, Arrays.asList("id"));
            }

            List<PmSpIconGroup> iconGroups = objectMapper.readValue(new File(path + "图标分组.json"), new TypeReference<>() {});
            if(iconGroups!=null && iconGroups.size()>0){
                DbUtils.insertOrUpdateBatch(iconGroups,pmIconGroupMapper, Arrays.asList("groupId", "groupName"));
            }
            List<PmSpIcon> icons = objectMapper.readValue(new File(path + "图标.json"), new TypeReference<>() {});
            if(icons!=null && icons.size()>0){
                DbUtils.insertOrUpdateBatch(icons,pmSpIconMapper, Arrays.asList("groupId", "iconName"));
            }
            // 上传资源文件
            File resourceDir = new File(path + "resource");
            for (File file : Objects.requireNonNull(resourceDir.listFiles())) {
                try (InputStream inputStream = new FileInputStream(file)) {
                    String objectName = "ebi/project/" + projectId + "/" + file.getName();
                    MinioService.uploadFile(inputStream, objectName);
                    System.out.println("上传完成:"+objectName);
                } catch (IOException e) {
                    throw new PmServiceException("导入图片失败:"+e.getMessage());
                }
            }
            //导入完成之后删除临时文件
            deleteDirectory(extractDir);
            return pmTeamProject.getProjectName()+"导入完成";
        } catch (Exception e) {
           throw new PmServiceException("导入失败:"+e.getMessage());
        }

    }

    /**
     * 拷贝项目
     * @param projectId
     * @return
     */
    public  void copyProject(String projectId) {
        try {
            // 查询项目信息
            PmTeamProject pmTeamProject = pmTeamProjectMapper.selectById(projectId);
            String loginUserId = SecurityUtils.getUserId();
            String pid = PmIdUtils.fastSimpleUUID();
            pmTeamProject.setProjectId(pid);
            pmTeamProject.setProjectName(pmTeamProject.getProjectName()+"(copy)");
            pmTeamProject.setCreateUser(loginUserId);
            pmTeamProject.setCreateTime(new Date());
            pmTeamProject.setUpdateTime(new Date());
            pmTeamProject.setIsDelete(0);//默认正常状态
            pmTeamProject.setPrefix(IdGenerator.generateUniqueId());
            int result=pmTeamProjectMapper.insert(pmTeamProject);
            //查询图元树
            QueryWrapper<PmGcNode> pmGcNodeWrapper = new QueryWrapper<>();
            pmGcNodeWrapper.eq("PROJECT_ID", projectId);
            List<PmGcNode> pmGcNodeList = pmGcNodeMapper.selectList(pmGcNodeWrapper);
            if(pmGcNodeList!=null && pmGcNodeList.size()>0){
                   for (PmGcNode node : pmGcNodeList) {
                        node.setProjectId(pid);
                }
                DbUtils.insertOrUpdateBatch(pmGcNodeList,pmGcNodeMapper, Arrays.asList("projectId", "pid"));
            }
            //查询图元内容
            QueryWrapper<PmGcData> pmGcDataWrapper = new QueryWrapper<>();
            pmGcDataWrapper.eq("PROJECT_ID", projectId);
            List<PmGcData> pmGcDataList = pmGcDataMapper.selectList(pmGcDataWrapper);
            if(pmGcDataList!=null && pmGcDataList.size()>0){
                for (PmGcData pmGcData : pmGcDataList) {
                    pmGcData.setProjectId(pid);
                }
                DbUtils.insertOrUpdateBatch(pmGcDataList,pmGcDataMapper, Arrays.asList("graphId"));
            }
            //查询图元路由
            QueryWrapper<PmGcPage> pmGcPagerapper = new QueryWrapper<>();
            pmGcPagerapper.eq("PROJECT_ID", projectId);
            List<PmGcPage> pmGcPageList = pmGcPageMapper.selectList(pmGcPagerapper);
            if(pmGcPageList!=null && pmGcPageList.size()>0){
                for (PmGcPage pmGcPage : pmGcPageList) {
                    pmGcPage.setProjectId(pid);
                }
                DbUtils.insertOrUpdateBatch(pmGcPageList,pmGcPageMapper, Arrays.asList("projectId", "id"));
            }
            //查询图元接口
            QueryWrapper<PmGcRequest> pmGcRequestwrapper = new QueryWrapper<>();
            pmGcRequestwrapper.eq("PROJECT_ID", projectId);
            List<PmGcRequest> pmGcRequestList = pmGcRequestMapper.selectList(pmGcRequestwrapper);
            if(pmGcRequestList!=null && pmGcRequestList.size()>0){
                for (PmGcRequest pmGcRequest : pmGcRequestList) {
                    pmGcRequest.setProjectId(pid);
                }
                DbUtils.insertOrUpdateBatch(pmGcRequestList,pmGcRequestMapper, Arrays.asList("projectId", "id"));
            }
            QueryWrapper<PmSpResource> pmSpResourceWrapper = new QueryWrapper<>();
            pmSpResourceWrapper.eq("PROJECT_ID", projectId);
            List<PmSpResource> PmSpResourceList = pmSpResourceMapper.selectList(pmSpResourceWrapper);
            if(pmGcRequestList!=null && PmSpResourceList.size()>0){
                for (PmSpResource pmSpResource : PmSpResourceList) {
                    pmSpResource.setProjectId(pid);
                }
                DbUtils.insertOrUpdateBatch(PmSpResourceList,pmSpResourceMapper, Arrays.asList("id"));
            }
            //从源项目将文件夹拷贝一份到新的里面
            MinioService.copyFolder("ebi/project/"+projectId,"ebi/project/"+pid);

        }catch (Exception e){
            throw new PmServiceException("复制失败:"+e.getMessage());
        }
    }
}
