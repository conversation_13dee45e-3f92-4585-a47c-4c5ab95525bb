package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.wcp.core.config.SecurityUtils;
import com.wcp.core.text.PmStringUtils;
import com.wcp.core.text.PmUUID;
import com.wcp.domain.PmSpDataVersion;
import com.wcp.domain.dto.PmSpDataVersionDto;
import com.wcp.mapper.PmSpDataVersionMapper;
import com.wcp.service.PmSpDataVersionService;
import com.wcp.utils.GzipCompress;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 1. @Description 图元版本业务接口
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 10:04
 */
@Service
public class PmSpDataVersionServiceImpl extends ServiceImpl<PmSpDataVersionMapper, PmSpDataVersion> implements PmSpDataVersionService {


    /**
     * 新增版本
     * @param pmSpDataVersion
     * @return
     */
    public int add(PmSpDataVersion pmSpDataVersion){
        String id= PmUUID.fastUUID().toString(true);
        pmSpDataVersion.setVersionId(id);
        pmSpDataVersion.setParentGraphId(pmSpDataVersion.getGraphId());
        pmSpDataVersion.setCreateTime(new Date());
        pmSpDataVersion.setUserAccount(SecurityUtils.getLoginUser().getUser().getAccount());
        if(PmStringUtils.isNotEmpty(pmSpDataVersion.getGraphData())){
            //图元内容进行压缩
            String graphData=pmSpDataVersion.graphData.toJSONString();
            String graphCell=GzipCompress.compress(graphData,"UTF-8");
            // 将字符串转换为字节数组，使用 UTF-8 编码
            byte[] bytes = graphCell.getBytes(StandardCharsets.UTF_8);
            pmSpDataVersion.setGraphCell(bytes);
        }
        return this.baseMapper.insert(pmSpDataVersion);
    }

    /**
     * 修改
     * @param pmSpDataVersion
     * @return
     */
    public int update(PmSpDataVersion pmSpDataVersion){
        pmSpDataVersion.setUserAccount(SecurityUtils.getLoginUser().getUser().getAccount());
        return this.baseMapper.updateById(pmSpDataVersion);
    }

    /**
     * 删除
     * @param versionId
     * @return
     */
    public int delete(String  versionId){
        return this.baseMapper.deleteById(versionId);
    }

    /**
     * 查询列表
     * @param graphId
     * @return
     */
    public List<PmSpDataVersionDto>  list(String graphId){
        List<PmSpDataVersionDto> versions = baseMapper.selectVersionList(graphId);
        return versions;
    }

    /**
     * 查询历史图元
     * @param versionId
     * @param encoding
     * @return
     * @throws IOException
     */
    public Object getHistGraph(String versionId,String encoding) throws IOException {
        LambdaQueryWrapper<PmSpDataVersion> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(PmSpDataVersion::getVersionId, versionId);
        PmSpDataVersion pmSpDataVersion=this.baseMapper.selectOne(queryWrapper);
        if(pmSpDataVersion!=null){
            byte[] graphCell=pmSpDataVersion.getGraphCell();
            String graphData = new String(graphCell, StandardCharsets.UTF_8);
            //如果encodng是gzip就解压，就返回压缩的，如果是json就是需要解压
            return  "gzip".equals(encoding)?graphData: GzipCompress.uncompress(graphData);
        }
        return null;
    }
}
