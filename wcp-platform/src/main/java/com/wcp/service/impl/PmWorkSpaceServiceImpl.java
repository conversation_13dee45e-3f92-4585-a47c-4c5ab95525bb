package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.text.PmIdUtils;
import com.wcp.domain.PmWorkSpace;
import com.wcp.domain.PmWorkSpaceUser;
import com.wcp.domain.dto.PmWorkSpaceDto;
import com.wcp.enums.PermissionEnum;
import com.wcp.enums.UserRoleEnum;
import com.wcp.mapper.PmWorkSpaceMapper;
import com.wcp.mapper.PmWorkSpaceUserMapper;
import com.wcp.minio.MinioService;
import com.wcp.service.PmWorkSpaceService;
import com.wcp.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@Service
public class PmWorkSpaceServiceImpl extends ServiceImpl<PmWorkSpaceMapper, PmWorkSpace> implements PmWorkSpaceService{
    @Autowired
    private PmWorkSpaceMapper workSpaceMapper;
    @Autowired
    private PmWorkSpaceUserMapper pmWorkSpaceUserMapper;

    /**
     * 查询登录用户所有的工作空间
     * @return
     * @throws PmServiceException
     */
    @Override
    public List<PmWorkSpaceDto> queryWorkSpaceByLoginUser() throws PmServiceException{
        String userId = SecurityUtils.getLoginUser().getUser().getUserId();
        List<PmWorkSpaceDto> list=workSpaceMapper.queryWorkSpaceByLoginUser(userId);
        if(list!=null && list.size()>0){
            list.forEach(t->{
                t.setType("免费版");
            });
        }
        return list;
    }

    /**
     * 创建空间
     * @param file
     * @param pmWorkSpace
     * @return
     * @throws PmServiceException
     */
    @Transactional
    @Override
    public Boolean createWorkSpace(MultipartFile file,PmWorkSpace pmWorkSpace) throws PmServiceException{
        if (StringUtil.isEmpty(pmWorkSpace.getSpaceName())) {
            throw new PmServiceException("空间名称不能为空！请重新输入。");
        }
        if (file != null && !file.isEmpty()) {
            String originalFilename = file.getOriginalFilename();
            String url="ebi/platform/"+originalFilename;
            MinioService.uploadFile(url,file);
            pmWorkSpace.setImgUrl(url);
        }
        String userId = SecurityUtils.getLoginUser().getUser().getUserId();
        String id = PmIdUtils.fastSimpleUUID();
        pmWorkSpace.setSpaceId(id);
        pmWorkSpace.setCreateUser(userId);
        pmWorkSpace.setCreateTime(new Date());
        int index=workSpaceMapper.insert(pmWorkSpace);
        if(index>0){
            PmWorkSpaceUser pmWorkSpaceUser=new PmWorkSpaceUser();
            pmWorkSpaceUser.setId(PmIdUtils.fastSimpleUUID());
            pmWorkSpaceUser.setSpaceId(id);//空间ID
            pmWorkSpaceUser.setUserId(userId);
            pmWorkSpaceUser.setUserRole(UserRoleEnum.CREATE.getCode());//管理员角色
            pmWorkSpaceUser.setPermission(PermissionEnum.EDIT.getCode());//拥有编辑权限
            pmWorkSpaceUser.setCreateUser(userId);
            pmWorkSpaceUser.setCreateTime(new Date());
           return pmWorkSpaceUserMapper.insert(pmWorkSpaceUser)>0;
        }
        return false;
    }

    /**
     * 修改空间
     * @param file
     * @param pmWorkSpace
     * @return
     * @throws PmServiceException
     */
    @Override
    public Boolean updateWorkSpace(MultipartFile file,PmWorkSpace pmWorkSpace) throws PmServiceException{
        try {
            if (StringUtil.isEmpty(pmWorkSpace.getSpaceName())) {
                throw new PmServiceException("空间名称不能为空！请重新输入。");
            }
            String userId = SecurityUtils.getLoginUser().getUser().getUserId();
            // 如果 file 不为空，则进行文件替换
            if (file != null && !file.isEmpty()) {
                LambdaQueryWrapper<PmWorkSpace> wrapper = Wrappers.<PmWorkSpace>lambdaQuery()
                        .eq(PmWorkSpace::getSpaceId, pmWorkSpace.getSpaceId())
                        .eq(PmWorkSpace::getCreateUser, userId);
                PmWorkSpace workSpace = workSpaceMapper.selectOne(wrapper);
                if (workSpace != null && StringUtil.isNotEmpty(workSpace.getImgUrl())) {
                    //删除minio缩略图地址
                    MinioService.removeFile(workSpace.getImgUrl());
                }
                String originalFilename = file.getOriginalFilename();
                String url = "ebi/platform/" + originalFilename;
                MinioService.uploadFile(url, file);
                pmWorkSpace.setImgUrl(url);
            }
            pmWorkSpace.setSpaceName(pmWorkSpace.getSpaceName());
            pmWorkSpace.setDescr(pmWorkSpace.getDescr());//描述
            pmWorkSpace.setUpdateUser(userId);
            pmWorkSpace.setUpdateTime(new Date());
            return workSpaceMapper.updateById(pmWorkSpace) > 0;
        }catch (Exception e){
            throw new PmServiceException("修改失败");
        }
    }

    /**
     * 删除空间
     * @param spaceId
     * @return
     * @throws PmServiceException
     */
    @Override
    public Boolean deleteWorkSpace(String spaceId) throws PmServiceException{
        if (StringUtil.isEmpty(spaceId)) {
            throw new PmServiceException("空间ID不能为空！请重新输入。");
        }
        String loginUserId = SecurityUtils.getUserId();
        LambdaQueryWrapper<PmWorkSpace> wrapper = Wrappers.<PmWorkSpace>lambdaQuery()
                .eq(PmWorkSpace::getSpaceId, spaceId)
                .eq(PmWorkSpace::getCreateUser, loginUserId);
        return workSpaceMapper.delete(wrapper) > 0;
    }
}
