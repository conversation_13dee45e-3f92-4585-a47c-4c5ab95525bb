package com.wcp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.domain.PmSpProjectPrefix;
import com.wcp.mapper.PmSpProjectPrefixMapper;
import com.wcp.service.PmSpProjectPrefixService;
import com.wcp.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 1. @Description 项目前缀service
 * 2. <AUTHOR>
 * 3. @Date 2025/6/13 10:52
 */
@Service
public class PmSpProjectPrefixServiceImpl extends ServiceImpl<PmSpProjectPrefixMapper, PmSpProjectPrefix>  implements PmSpProjectPrefixService {


    /**
     * 项目前缀列表查询
     * @param projectId
     * @return
     */
    @Override
    public List<JSONObject> prefixList(String projectId) {
        LambdaQueryWrapper<PmSpProjectPrefix> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(PmSpProjectPrefix::getProjectId, projectId).select(PmSpProjectPrefix::getName,PmSpProjectPrefix::getPrefix,PmSpProjectPrefix::getStatus, PmSpProjectPrefix::getId);
        List<PmSpProjectPrefix> prefixList=baseMapper.selectList(wrapper);
        List<JSONObject> list=new ArrayList<>();
        for (PmSpProjectPrefix pm : prefixList) {
            JSONObject tmp = new JSONObject();
            tmp.put("prefix",pm.getPrefix());
            tmp.put("id",pm.getId());
            tmp.put("name",pm.getName());
            tmp.put("status",pm.getStatus());
            list.add(tmp);
        }
        return list;
    }

    /**
     * 项目前缀保存/修改
     * @param prefixList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int prefixSaveOrUpdates(List<PmSpProjectPrefix> prefixList) {
        if (CollectionUtils.isEmpty(prefixList)) {
            // 空列表直接返回，避免无效操作
            return 0;
        }

        int count = 0;
        for (PmSpProjectPrefix prefix : prefixList) {
            if (StrUtil.isEmpty(prefix.getId())) {
                // 无 ID，执行新增逻辑
                prefix.setId(StringUtil.generateUUID());
                prefix.setCreateTime(new Date());
                count += baseMapper.insert(prefix);
            } else {
                // 有 ID，执行更新逻辑
                prefix.setUpdateTime(new Date());
                count += baseMapper.updateById(prefix);
            }
        }
        return count;
    }

    /**
     * 项目前缀删除
     * @param pmSpProjectPrefix
     * @return
     */
    @Override
    public int prefixDelete(PmSpProjectPrefix pmSpProjectPrefix) {
        return baseMapper.deleteById(pmSpProjectPrefix.getId());
    }


}
