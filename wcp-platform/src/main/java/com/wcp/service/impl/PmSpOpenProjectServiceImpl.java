package com.wcp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.text.PmIdUtils;
import com.wcp.domain.PlmUser;
import com.wcp.domain.PmSpOpenProject;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;
import com.wcp.enums.PermissionEnum;
import com.wcp.enums.UserRoleEnum;
import com.wcp.mapper.PmSpOpenProjectMapper;
import com.wcp.minio.MinioService;
import com.wcp.service.PmSpOpenProjectService;
import com.wcp.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 1. @Description 最近打开的项目
 * 2. <AUTHOR>
 * 3. @Date 2025/3/17 11:12
 */
@Service
public class PmSpOpenProjectServiceImpl extends ServiceImpl<PmSpOpenProjectMapper, PmSpOpenProject> implements PmSpOpenProjectService {

    /**
     * 记录最近打开的项目
     * @param projectOpen
     * @return
     */
    public int recordProjectOpen(PmSpOpenProject projectOpen){
        String userId=SecurityUtils.getUserId();
        projectOpen.setUserId(userId);
        // 设置打开时间为当前时间
        projectOpen.setCreateTime(new Date());
        projectOpen.setId(PmIdUtils.fastSimpleUUID());
        // 检查是否已存在记录
        QueryWrapper<PmSpOpenProject> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("PROJECT_ID", projectOpen.getProjectId())
                .eq("USER_ID", projectOpen.getUserId());
        PmSpOpenProject existingRecord = baseMapper.selectOne(queryWrapper);

        if (existingRecord != null) {
            // 如果记录已存在，更新打开时间
            existingRecord.setCreateTime(projectOpen.getCreateTime());
            return baseMapper.updateById(existingRecord);

        } else {
            // 如果记录不存在，插入新记录
            return baseMapper.insert(projectOpen);
        }
    }

    /**
     * 获取最近打开的项目记录
     * @return
     */
    @Override
    public List<PmSpTeamGroupProjectDto> getRecentProjectOpens() {
        // 清理过期记录
        // 计算 30 天前的时间
        String userId=SecurityUtils.getUserId();
        PlmUser plmUser =SecurityUtils.getLoginUser().getUser();
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(30);

        // 删除超过 7 天的记录
        QueryWrapper<PmSpOpenProject> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lt("CREATE_TIME", sevenDaysAgo);

        List<PmSpTeamGroupProjectDto> list=baseMapper.queryOpenProject(userId);
        if(list!=null){
            list.forEach(t->{
                try {
                    t.setImgUrl(StringUtil.isNotEmpty(t.getImgUrl())? MinioService.getPresignedObjectUrl(t.getImgUrl()):null);
                    t.setPermission(Integer.parseInt(t.getPermission())== PermissionEnum.VIEW.getCode()?PermissionEnum.VIEW.getName():PermissionEnum.EDIT.getName());
                    t.setUserRole(t.getCreateUser().equals(plmUser.getAccount())? UserRoleEnum.CREATE.getName() :  UserRoleEnum.fromCode(Integer.parseInt(t.getUserRole())).getName());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        // 查询记录
        return list;
    }
}
