package com.wcp.service.impl;

import com.wcp.core.config.PMPermissionService;
import com.wcp.core.enums.PlmUserStatus;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PlmRole;
import com.wcp.domain.PmLoginUser;
import com.wcp.domain.PlmUser;
import com.wcp.service.IUserInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    @Lazy
    public IUserInfoService userInfoService;

    @Autowired
    public PMPermissionService pmPermissionService;

    @Override
    public UserDetails loadUserByUsername(String account) throws UsernameNotFoundException {
        PlmUser user = userInfoService.selectUserByUserName(account);
        if (PmStringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", account);
            throw new PmServiceException("登录用户：" + account + " 不存在");
        } else if (PlmUserStatus.DISABLE.getCode().equals(user.getUserStatus())) {
            log.info("登录用户：{} 已被停用.", account);
            throw new PmServiceException("对不起，您的账号：" + account + " 已停用");
        }

        return createLoginUser(user);
    }

    public UserDetails createLoginUser(PlmUser user) {
        //List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(pmPermissionService.getRolePermission(user).toArray(new String[0]));
        Set<PlmRole> roles= pmPermissionService.getRoles(user);
        user.setRoles(roles);
        return new PmLoginUser(user.getUserId(),  user);
       // return new PmLoginUser(user.getUserId(),  user, authorities);
    }
}
