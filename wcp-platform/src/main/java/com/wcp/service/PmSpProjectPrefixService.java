package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.domain.PmSpProjectPrefix;

import java.util.List;

/**
 * 1. @Description 项目前缀定义service
 * 2. <AUTHOR>
 * 3. @Date 2025/6/13 9:05
 */
public interface PmSpProjectPrefixService {

    /**
     * 获取系统配置的项目前缀列表信息
     * @param projectId
     * @return
     */
    public List<JSONObject> prefixList(String projectId);

    /**
     * 项目前缀信息保存
     * @param prefixList
     * @return
     */
    public int prefixSaveOrUpdates(List<PmSpProjectPrefix> prefixList);

    /**
     * 项目前缀删除
     * @param pmSpProjectPrefix
     * @return
     */
    public int prefixDelete(PmSpProjectPrefix pmSpProjectPrefix);


}
