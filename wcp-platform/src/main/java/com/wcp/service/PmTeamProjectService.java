package com.wcp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wcp.core.execption.PmServiceException;
import com.wcp.domain.PmTeamProject;
import com.wcp.domain.dto.PlmUserDto;
import com.wcp.domain.dto.PmTeamProjectSerachDto;
import com.wcp.domain.vo.PmProjectUserVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface PmTeamProjectService extends IService<PmTeamProject> {
    /**
     * 查询当前团队下用户可以看到的项目
     * @param spaceId
     * @param teamId
     * @return
     */
    List<PmTeamProject> queryTeamProjectById(String spaceId,String teamId);

    /**
     * 创建项目
     * @param file
     * @param project
     * @return
     */
    Boolean createTeamProject(MultipartFile file,PmTeamProject project);
    /**
     * 修改项目
     * @param file
     * @param project
     * @return
     * @throws PmServiceException
     */
     Boolean updateTeamProject(MultipartFile file,PmTeamProject project);

    Boolean deleteTeamProject(PmTeamProject teamProject);
    /**
     * 要用项目成员加入
     * @param projectUsers
     * @return
     */
    Boolean  addProjectUser(List<PmProjectUserVo> projectUsers);
    /**
     * 用户搜索
     * @param content
     * @return
     */
    List<PlmUserDto> searchuser(String projectId,String content);
    List<PmTeamProject> queryTeamProject(String teamId,String groupId) throws PmServiceException;

    /**
     * 项目搜索
     * @param content
     * @return
     */
    JSONArray searchTeamList(String content);
}
