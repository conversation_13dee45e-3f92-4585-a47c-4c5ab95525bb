package com.wcp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wcp.core.execption.PmServiceException;
import com.wcp.domain.PmWorkSpace;
import com.wcp.domain.dto.PmWorkSpaceDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface PmWorkSpaceService extends IService<PmWorkSpace> {

    List<PmWorkSpaceDto> queryWorkSpaceByLoginUser();

    Boolean createWorkSpace(MultipartFile file, PmWorkSpace pmWorkSpace);

    Boolean deleteWorkSpace(String spaceId);
    Boolean updateWorkSpace(MultipartFile file,PmWorkSpace pmWorkSpace) throws PmServiceException;
}
