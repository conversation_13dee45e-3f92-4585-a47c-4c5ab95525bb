package com.wcp.service;

import com.wcp.domain.PmSpProjectResource;
import com.wcp.domain.dto.PmSpProjectResourceTreeDto;

import java.util.List;

/**
 * 1. @Description 项目资源管理器
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 13:41
 */
public interface PmSpProjectResourceService {
    List<PmSpProjectResourceTreeDto> getResourceTree(String projectId); // 查询文件树

    int addResource(PmSpProjectResource resource, String projectId); // 新增资源
    int updateResource(PmSpProjectResource resource); // 修改资源
    int deleteResource(String id); // 删除资
    void deleteChildResources(String parentId);//删除文件夹
}
