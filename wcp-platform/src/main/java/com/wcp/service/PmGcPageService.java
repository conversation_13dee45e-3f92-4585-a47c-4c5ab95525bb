package com.wcp.service;

import com.wcp.domain.PmGcPage;

/**
 * 1. @Description 图元路由
 * 2. <AUTHOR>
 * 3. @Date 2025/2/12 17:22
 */

public interface PmGcPageService {
    /**
     * 图元路由发布
     * @param pmGcPage
     */
    public int publishGraphPage(PmGcPage pmGcPage);

    /**
     * 删除图元路由
     * @param id
     * @return
     */
    public int deleteRoute(String projectId,String id);

    /**
     * 获取图元路由信息
     * @param projectId
     * @param pmGcPage
     * @return
     */
    public PmGcPage getRouteInfo(String projectId,PmGcPage pmGcPage);
}
