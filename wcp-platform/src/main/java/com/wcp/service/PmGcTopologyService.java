package com.wcp.service;

import com.wcp.domain.PmGcTopology;

import java.util.List;

/**
 * 1. @Description 拓扑管理定义service
 * 2. <AUTHOR>
 * 3. @Date 2025/6/23 14:05
 */
public interface PmGcTopologyService {


    /**
     * 查询该项目下的拓扑图（不包含data）
     *
     * @param projectId
     * @return
     */
    List<PmGcTopology> queryTopologyIdxs(String projectId);

    /**
     * 查询该项目下的拓扑图
     *
     * @param projectId
     * @return
     */
    List<PmGcTopology> queryTopologyList(String projectId);

    /**
     * 根据项目id和拓扑id查询拓扑图
     *
     * @param projectId
     * @param idArr
     * @return
     */
    List<PmGcTopology> queryTopologyById(String projectId, String[] idArr);

    /**
     * 拓扑图保存
     * @param pmGcTopology
     * @return
     */
    int saveTopology(PmGcTopology pmGcTopology);

    /**
     * 拓扑图删除
     * @param projectId
     * @param ids
     * @return
     */
    int deleteTopologyByIds(String projectId, List<String> ids);
}
