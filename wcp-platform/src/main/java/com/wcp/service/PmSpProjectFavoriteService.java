package com.wcp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wcp.domain.PmSpProjectFavorite;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;

import java.util.List;

/**
 * 1. @Description 收藏业务接口
 * 2. <AUTHOR>
 * 3. @Date 2025/3/24 10:08
 */
public interface PmSpProjectFavoriteService extends IService<PmSpProjectFavorite> {

    /**
     * 项目收藏
     * @param
     * @return
     */
    List<PmSpTeamGroupProjectDto> queryFavoriteProject(String orderBy);

    /**
     * 添加收藏
     * @param favorite
     * @return
     */
    boolean add(PmSpProjectFavorite favorite);


    /**
     * 取消项目收藏
     * @param favorite
     * @return
     */
    int delete(PmSpProjectFavorite favorite);
}
