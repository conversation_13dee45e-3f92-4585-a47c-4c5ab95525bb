package com.wcp.service;

import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;

/**
 * 1. @Description 导入导出项目资源
 * 2. <AUTHOR>
 * 3. @Date 2025/3/25 8:59
 */

public interface ProjectExporterService {
    /**
     * 导出项目资源
     * @param projectId
     */
    Path exportProject(String projectId);
    /**
     * 导入项目
     * @param multipartFile
     */
    String importProject(MultipartFile multipartFile);

    /**
     * 拷贝项目
     * @param projectId
     * @return
     */
    void copyProject(String projectId);
}
