package com.wcp.service;

import com.wcp.domain.PmSpDataVersion;
import com.wcp.domain.dto.PmSpDataVersionDto;

import java.io.IOException;
import java.util.List;

/**
 * 1. @Description 图元版本业务接口
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 10:04
 */

public interface PmSpDataVersionService {
    /**
     * 新增历史版本
     * @param pmSpDataVersion
     * @return
     */
    public int add(PmSpDataVersion pmSpDataVersion);

    /**
     * 查询列表
     * @param graphId
     * @return
     */
    public List<PmSpDataVersionDto> list(String graphId);

    public int update(PmSpDataVersion pmSpDataVersion);

    public int delete(String  versionId);
    /**
     * 查询历史图元
     * @param graphId
     * @param rootId
     * @param encoding
     * @return
     * @throws IOException
     */
    public Object getHistGraph(String versionId,String encoding) throws IOException;
}
