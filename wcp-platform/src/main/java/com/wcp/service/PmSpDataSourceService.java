package com.wcp.service;

import com.wcp.domain.PmSpDataSource;
import io.dataease.plugins.common.dto.datasource.TableDesc;

import java.util.List;
import java.util.Map;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/4/16 11:25
 */
public interface PmSpDataSourceService {
    /**
     * 根据类型查询数据源
     * @param type
     * @param projectId
     * @return
     */
    List<PmSpDataSource> dataSourceList(String type, String projectId);

    /**
     * 查询链接信息
     * @param service
     * @param projectId
     * @return
     */
    List<TableDesc> queryTables(String service, String projectId);

    /**
     * 预览表数据
     * @param service
     * @param table
     * @param projectId
     * @return
     */
    List<Map<String,Object>> queryTableData(String service, String table, String projectId);
}
