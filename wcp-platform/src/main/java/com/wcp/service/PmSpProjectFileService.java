package com.wcp.service;

import com.wcp.domain.PmSpProjectFile;
import com.wcp.domain.dto.PmSpProjectFileDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 1. @Description 项目资源文件
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 13:41
 */
public interface PmSpProjectFileService {
    /**
     * 保存项目资源
     * @param file
     * @param projectId
     * @param pmSpProjectFile
     * @return
     */
    public int uploadFile(MultipartFile file, String projectId, PmSpProjectFile pmSpProjectFile);
    /**
     * 修改资源文件
     * @param file
     * @param projectId
     * @param pmSpProjectFile
     * @return
     */
    public int updateFile(MultipartFile file,String projectId, PmSpProjectFile pmSpProjectFile);

    /**
     * 查询资源列表
     * @param id
     * @return
     */
    public List<PmSpProjectFileDto> queryFileList(String id);
    /**
     * 删除单个资源文件
     * @param id
     * @return
     */
    public int deleteFile(String id);

    /**
     * 查询图元详情
     * @param id
     * @param encoding
     * @return
     * @throws IOException
     */
    public Object queryDetail(String id,String encoding)throws IOException;
}
