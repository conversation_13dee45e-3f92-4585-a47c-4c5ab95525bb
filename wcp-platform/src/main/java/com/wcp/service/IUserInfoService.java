package com.wcp.service;

import com.wcp.core.http.PmResult;
import com.wcp.domain.PlmUser;
import com.wcp.domain.PmLoginUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 用户接口服务类
 */
public interface IUserInfoService {
    List<PlmUser> getInfo(String userName);
    Map<String,Object> login(String username, String password);
    /**
     * 账号查询用户信息
     * @param account
     * @return
     */
     PlmUser selectUserByUserName(String account);
    /**
     * 用户ID获取用户信息
     * @param userId
     * @return
     */
    public PlmUser queryMe(String userId);
    /**
     * 注册
     */
    public String register(PlmUser plmUser);

    /**
     * 微信登录获取用户信息
     * @param code
     * @return
     */
    public PmLoginUser saveWeChatUser(String code);

    /**
     * 通过账号、手机号或邮箱查找对应的账号
     * @param identifier 手机号或邮箱
     * @return 账号，未找到返回null
     */
    PlmUser findUserByIdentifier(String identifier);

    /**
     * 验证用户密码
     * @param account 用户名
     * @param password 密码
     */
    void validatePassword(String account, String password);

    /**
     * 修改用户密码
     * @param userId 用户ID
     * @param newPassword 新密码
     */
    void changePassword(String userId, String newPassword);

    /**
     * 注销用户账户
     * @param userId 用户ID
     */
    void cancelAccount(String userId);

    /**
     * 更新用户信息
     * @param plmUser 更新参数
     * @param currentUser 当前用户信息
     * @return
     */
    PmResult updateUserInfo(PlmUser plmUser, PlmUser currentUser);

    /**
     * 更新用户头像
     * @param headImage 头像file
     * @param loginUser 登录信息
     * @return
     */
    PmResult updateAvatar(MultipartFile headImage, PmLoginUser loginUser);
}
