package com.wcp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.domain.PmSpResource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 1. @Description 资源管理器业务类
 * 2. <AUTHOR>
 * 3. @Date 2025/2/26 9:58
 */
public interface PmSpResourceService{
    /**
     * 新增文件夹树
     * @param pmSpResource
     * @return
     */
    boolean addResource(PmSpResource pmSpResource);
    /**
     * 删除资源
     * @param array
     * @return
     */
    boolean deleteResource(String parentId,List<String> array);

    /**
     * 文件夹树形目录
     * @return
     */

    JSONObject getAllFolders(String projectId);

    /**
     * 修改文件夹名称
     * @param pmSpResource
     * @return
     */
    int updateResource(PmSpResource pmSpResource);

    /**
     * 上传文件
     * @param files
     * @param projectId
     * @param folderId
     * @return
     */
    boolean upload(MultipartFile[] files, String projectId, String folderId);
    /**
     * 获取文件夹子集
     * @param projectId
     * @param folderId
     * @return
     */
    List<PmSpResource> getChildrenFile(String projectId,String folderId,String sortColumn,String sortOrder);
    /**
     * 移动
     * @param jsonArray
     * @param parentId
     * @return
     */
    boolean moveResource(JSONArray jsonArray, String parentId);

    /**
     * 复制资源
     * @param projectId
     * @param id
     * @return
     */
    boolean copyResource(String projectId,String id);
}
