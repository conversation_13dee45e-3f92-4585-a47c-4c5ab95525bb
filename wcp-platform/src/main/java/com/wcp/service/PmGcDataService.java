package com.wcp.service;

import com.wcp.domain.PmGcData;

import java.io.IOException;

/**
 * Description: 图元业务类
 * Author: qianchao
 * Date: 2024/3/13 16:00
 */
public interface PmGcDataService {
    /**
     * 保存图元
     * @param pmGcData
     * @return
     */
    public int saveOrUpdateGraph(PmGcData pmGcData);
    /**
     * 通过图元点号获取图元内容
     * @param graphId
     * @return
     */
    public Object getProejctGraph(String graphId,String rootId,String encoding,String projectid) throws IOException;


}
