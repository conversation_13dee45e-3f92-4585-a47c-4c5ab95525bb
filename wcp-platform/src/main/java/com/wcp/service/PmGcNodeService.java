package com.wcp.service;

import com.wcp.domain.PmGcNode;
import com.wcp.utils.TreeNode;

import java.util.List;

/**
 * Description: 图元树形service
 * Author: qianchao
 * Date: 2024/3/4 15:31
 */
public interface PmGcNodeService {

    /**
     * 添加节点
     * @param pmGcNode
     */
    boolean saveNode(PmGcNode pmGcNode);

    /**
     * 查询属性菜单
     * @param projectId
     * @return
     */
     List<TreeNode> queryNodes(String projectId);

    /**
     * 删除节点
     * @param pId
     * @param nodeType
     * @return
     */
    int deleteNode(String pId,String nodeType,String projectId);

    /**
     * 修改树节点
     * @param pmGcNode
     * @return
     */
     boolean updateNode(PmGcNode pmGcNode);
}
