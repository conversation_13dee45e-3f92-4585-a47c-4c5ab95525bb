package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmWorkTeam;
import com.wcp.domain.dto.PlmUserDto;
import com.wcp.domain.dto.PmSpTeamRecycleProjectDto;
import com.wcp.domain.dto.PmWorkTeamDto;
import com.wcp.domain.dto.TeamUserDto;
import com.wcp.domain.vo.PmTeamUserVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface PmWorkTeamService extends IService<PmWorkTeam> {

    List<PmWorkTeamDto> queryWorkTeamById(String userId);

    Boolean createWorkTeam(MultipartFile file, PmWorkTeam pmWorkTeam);

    Boolean deleteWorkTeam(String teamId);
    Boolean updateWorkTeam(PmWorkTeam team);
    Boolean  addTeamUser(List<PmTeamUserVo> teamUsers);
    /**
     * 用户搜索
     * @param content
     * @return
     */
     List<PlmUserDto> searchuser(String temId, String content);
    /**
     * 查看团队中的成员
     * @param teamId
     * @return
     * @throws PmServiceException
     */
     List<TeamUserDto> queryTeamUserById(String teamId) throws PmServiceException;
     List<PmWorkTeamDto> queryWorkTeamById() throws PmServiceException;
    /**
     * 根据团队ID按照分组查看项目
     * @param teamId
     */
    List<Map<String, Object>>  queryTeamProjectByGroup(String teamId);

    /**
     * 查询团队下面的回收站中的项目
     * @param teamId
     * @return
     */
    List<PmSpTeamRecycleProjectDto> queryTeamRecycle(String teamId);
    /**
     * 恢复团队下的回收站
     * @param teamId
     * @return
     */
    int recycleRestore(String teamId,String projectId);
    /**
     * 清空回收站
     * @param teamId
     * @param projectId
     * @return
     */
    int recycleClear(String teamId,String projectId);
    /**
     *修改权限
     * @return
     */
    int updatePermission(TeamUserDto teamUserDto);
    /**
     * 修改项目图标
     * @param profileImage
     * @param teamId
     * @return
     */
     PmResult uploadTeamProfile(MultipartFile profileImage, String teamId);

    /**
     * 查询用户信息
     * @param teamId
     * @return
     */
    PmWorkTeamDto queryDetail(String teamId);

    /**
     * 团队交接
     * @param  jsonObject
     * @return
     */
    Boolean teamTransfer(JSONObject jsonObject);
}
