package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.domain.PmGcRequest;

import java.util.List;

/**
 * 1. @Description 数据接口定义service
 * 2. <AUTHOR>
 * 3. @Date 2025/2/14 10:46
 */
public interface PmGcAjaxService {

    /**
     * 获取系统配置的接口列表信息
     * @param projectId
     * @return
     */
    public List<JSONObject> ajaxList(String projectId);

    /**
     * 接口信息保存
     * @param pmGcAjax
     * @return
     */
    public int ajaxSave(PmGcRequest pmGcAjax);

    /**
     * 接口删除
     * @param pmGcAjax
     * @return
     */
    public int ajaxDelete(PmGcRequest pmGcAjax);
}
