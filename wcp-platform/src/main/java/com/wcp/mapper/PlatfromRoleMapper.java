package com.wcp.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PlmRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Set;

/**
 * 平台角色
 */
@Mapper
public interface PlatfromRoleMapper extends BaseMapper<PlmRole> {
    @Select("SELECT WSR.ROLE_ID ,WSR.ROLE_NAME ,WSR.ROLE_KEY  FROM WCP_SP_USER U \n" +
            "        LEFT JOIN WCP_SP_USER_ROLE UR ON U.USER_ID=UR.USER_ID \n" +
            "        LEFT JOIN WCP_SP_ROLE WSR  ON UR.ROLE_ID =WSR.ROLE_ID  " +
            " WHERE U.USER_STATUS=1 AND UR.USER_ID = #{userId} ")
    Set<PlmRole> selectRolePermissionByUserId(String userId);
}
