package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmSpProjectFavorite;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 1. @Description TODO 收藏Mapper
 * 2. <AUTHOR>
 * 3. @Date 2025/3/24 10:06
 */
public interface PmSpProjectFavoriteMapper extends BaseMapper<PmSpProjectFavorite> {
    @Select("SELECT A.ID AS COLLECT_ID,A.PROJECT_ID,B.PROJECT_NAME,B.IMG_URL,B.UPDATE_TIME,A.CREATED_TIME,B.HOME_ROUTER,C.USER_ROLE,C.PERMISSION,D.ACCOUNT AS CREATE_USER,D.USER_NAME  FROM WCP_SP_PROJECT_FAVORITE A  " +
            " JOIN WCP_SP_PROJECT B ON A.PROJECT_ID=B.PROJECT_ID AND B.IS_DELETE=0   " +
            " JOIN WCP_SP_TEAM_USER C ON B.TEAM_ID=C.TEAM_ID AND A.USER_ID=C.USER_ID  "+
            " JOIN WCP_SP_USER D ON D.USER_ID=B.CREATE_USER   "+
            " WHERE A.USER_ID=#{userId} ORDER BY A.CREATED_TIME ${orderBy}")
    List<PmSpTeamGroupProjectDto> queryOpenProject(String userId,String orderBy);
}
