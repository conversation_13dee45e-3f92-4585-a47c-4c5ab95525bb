package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmTeamUser;
import com.wcp.domain.dto.PlmUserDto;
import com.wcp.domain.dto.ProjectUserDto;
import com.wcp.domain.dto.TeamUserDto;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface PmTeamUserMapper extends BaseMapper<PmTeamUser> {

    @Select("SELECT B.USER_ID,B.ACCOUNT,B.USER_NAME,A.USER_ROLE,A.PERMISSION FROM WCP_SP_PROJECT_USER A \n " +
            "  JOIN WCP_SP_USER B ON A.USER_ID=B.USER_ID AND B.USER_STATUS=1 \n" +
            "  WHERE A.PROJECT_ID=#{projectId} AND A.PERMISSION!=2")
    List<ProjectUserDto> queryWorkTeamById(String projectId);

    /**
     * 搜索用户并且判断是否在团队中 邀请中过滤掉创建人的搜索
     * @param temId
     * @param account
     * @return
     */
    @Select("<script>" +
            "SELECT A.USER_ID, A.ACCOUNT, A.USER_NAME, B.PERMISSION FROM WCP_SP_USER A " +
            "LEFT JOIN WCP_SP_TEAM_USER B ON A.USER_ID = B.USER_ID AND B.TEAM_ID = #{temId}   " +
            "WHERE  (B.USER_ID IS NULL OR B.USER_ROLE != 2)   " +
            "<if test='account != null'> AND  A.ACCOUNT LIKE #{account}  </if> " +
            "<if test='phoneNumber != null'> AND A.PHONE_NUMBER = #{phoneNumber} </if> " +
            "<if test='email != null'> AND A.EMAIL = #{email} </if> " +
            "</script>")
    List<PlmUserDto> selectTeamByUser(String temId, String account, String phoneNumber, String email);

    /**
     * 获取已加入团队的成员
     * @param teamId
     * @return
     */
    @Select("SELECT A.ID,B.USER_ID,B.ACCOUNT,B.USER_NAME,A.USER_ROLE,A.PERMISSION FROM WCP_SP_TEAM_USER A \n " +
            "  JOIN WCP_SP_USER B ON A.USER_ID=B.USER_ID AND B.USER_STATUS=1 \n" +
            "  WHERE A.TEAM_ID=#{teamId} AND A.PERMISSION!=2")
    List<TeamUserDto> queryWorkTeamByUser(String teamId);

    /**
     * 当前用户退出该团队
     * @param userId
     * @param teamId
     * @return
     */
    @Delete("DELETE FROM WCP_SP_TEAM_USER WHERE USER_ID = #{userId} AND TEAM_ID = #{teamId}")
    int exitByUserIdAndTeamId(String userId,String teamId);

}
