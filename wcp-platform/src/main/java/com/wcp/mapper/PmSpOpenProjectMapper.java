package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmSpOpenProject;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 最近打开项目dao
 */
@Mapper
public interface PmSpOpenProjectMapper extends BaseMapper<PmSpOpenProject> {


    @Select("SELECT A.PROJECT_ID,B.PROJECT_NAME,B.IMG_URL,B.UPDATE_TIME,A.CREATE_TIME,B.HOME_ROUTER,C.USER_ROLE,C.PERMISSION,D.ACCOUNT AS CREATE_USER,D.USER_NAME,B.PREFIX  FROM WCP_SP_PROJECT_OPEN A  " +
            " JOIN WCP_SP_PROJECT B ON A.PROJECT_ID=B.PROJECT_ID AND B.IS_DELETE=0   " +
            " JOIN WCP_SP_TEAM_USER C ON B.TEAM_ID=C.TEAM_ID AND A.USER_ID=C.USER_ID  "+
            " JOIN WCP_SP_USER D ON D.USER_ID=B.CREATE_USER   "+
            " WHERE A.USER_ID=#{userId} ORDER BY A.CREATE_TIME DESC")
    List<PmSpTeamGroupProjectDto> queryOpenProject(String userId);
}
