package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmSpIconGroup;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Description: 图标分组dao
 * Author: qianchao
 * Date: 2024/3/5 11:08
 */
@Mapper
public interface PmIconGroupMapper extends BaseMapper<PmSpIconGroup> {
    @Select("select wsi.GROUP_ID ,wsi.GROUP_NAME,wsi2.ICON_NAME ,wsi2.ICON ,wsi2.ICON_TYPE ,wsi2.IS_FREE  from WCP_SP_ICONGROUP wsi left join WCP_SP_ICON wsi2 on wsi.GROUP_ID=wsi2.GROUP_ID ")
    @Results({
            @Result(column = "GROUP_ID", property = "groupId"),
            @Result(column = "GROUP_NAME", property = "groupName"),
            @Result(
                    property = "icons",
                    column = "GROUP_ID",
                    javaType = List.class,
                    many = @Many(select = "com.wcp.domain.PmSpIcon")
            )
    })
    List<PmSpIconGroup> selectIconGroupWithIcons();
}
