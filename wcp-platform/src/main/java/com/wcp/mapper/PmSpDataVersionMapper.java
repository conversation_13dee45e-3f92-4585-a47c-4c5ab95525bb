package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmSpDataVersion;
import com.wcp.domain.dto.PmSpDataVersionDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 1. @Description 图元版本
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 10:03
 */
@Mapper
public interface PmSpDataVersionMapper  extends BaseMapper<PmSpDataVersion> {
    /**
     * 查询 WCP_SP_DATA_VERSON 表并关联 user_account 表获取创建者用户名
     */
    @Select("SELECT v.VERSION_ID, v.CREATE_TIME, v.VERSION_NAME, v.REMARK, u.USER_NAME " +
            "FROM WCP_SP_DATA_VERSON v " +
            "LEFT JOIN WCP_SP_USER u ON v.USER_ACCOUNT = u.ACCOUNT  WHERE PARENT_GRAPH_ID=#{graphId} ORDER BY  v.CREATE_TIME DESC")
    List<PmSpDataVersionDto> selectVersionList(String graphId);
}
