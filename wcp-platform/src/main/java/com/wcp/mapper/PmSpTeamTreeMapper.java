package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmSpTeamTree;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PmSpTeamTreeMapper extends BaseMapper<PmSpTeamTree> {

    @Select("SELECT A.ID,A.GROUP_NAME,B.PROJECT_ID,B.PROJECT_NAME,B.IMG_URL,B.IS_PUBLIC,B.CREATE_USER,B.CREATE_TIME,B.CREATE_USER,B.DESCR,B.GROUP_ID,B.HOME_ROUTER,B.PREFIX  FROM WCP_SP_TEAM_TREE A  \n" +
            " LEFT JOIN WCP_SP_PROJECT B ON A.ID=B.GROUP_ID  AND A.TEAM_ID=B.TEAM_ID AND B.IS_DELETE=0  \n" +
            " WHERE A.TEAM_ID=#{teamId} AND A.IS_DELETE=0")
    List<PmSpTeamGroupProjectDto>  queryTeamPtojectByGroup(String teamId);
}
