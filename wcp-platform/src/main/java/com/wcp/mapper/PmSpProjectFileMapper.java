package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmSpProjectFile;
import com.wcp.domain.dto.PmSpProjectFileDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 1. @Description 项目资源文件表Mapper
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 13:40
 */
@Mapper
public interface PmSpProjectFileMapper extends BaseMapper<PmSpProjectFile> {

    /**
     * 查询 WCP_SP_PROJECT_FILE 表并关联 user_account 表获取创建者用户名
     */
    @Select("SELECT V.ID, V.CREATE_TIME,V.PARENT_ID, V.RESOURCE_NAME,V.THUMBNAIL_PATH,U.USER_NAME " +
            "FROM WCP_SP_PROJECT_FILE V " +
            "LEFT JOIN WCP_SP_USER U ON V.USER_ACCOUNT = U.ACCOUNT  WHERE PARENT_ID=#{ID}  ORDER BY  V.CREATE_TIME DESC")
    List<PmSpProjectFileDto> selectFileList(String id);
}
