package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmWorkSpace;
import com.wcp.domain.dto.PmWorkSpaceDto;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PmWorkSpaceMapper extends BaseMapper<PmWorkSpace> {
    @Select("SELECT WSI2.SPACE_ID,WSI2.SPACE_NAME,WSI2.IMG_URL  FROM WCP_SP_SPACE_USER WSI " +
            "   JOIN WCP_SP_SPACE WSI2 ON WSI.SPACE_ID=WSI2.SPACE_ID AND WSI.USER_ID=#{userId}")
    List<PmWorkSpaceDto> queryWorkSpaceByLoginUser(String userId);
}
