package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmWorkTeam;
import com.wcp.domain.dto.PmSpTeamRecycleProjectDto;
import com.wcp.domain.dto.PmWorkTeamDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PmWorkTeamMapper extends BaseMapper<PmWorkTeam> {

    @Select("<script>" +
            "SELECT WSI2.TEAM_ID, WSI2.TEAM_NAME, WSI2.SPACE_ID, WSI2.CREATE_USER, WSI.USER_ID,WSI2.IMG_URL,WSI2.DESCR     " +
            "FROM WCP_SP_TEAM_USER WSI " +
            "  JOIN WCP_SP_TEAM WSI2 ON WSI.TEAM_ID = WSI2.TEAM_ID  AND WSI2.IS_DELETE=0" +
            "<if test='spaceId != null'> WSI2.SPACE_ID = #{spaceId} </if>" +
            "<if test='userId != null'> AND WSI.USER_ID = #{userId} </if>" +
            "<if test='permission != null and permission.size() > 0'> " +
            "  AND WSI.PERMISSION IN " +
            "  <foreach collection='permission' item='item' open='(' separator=',' close=')'> " +
            "    #{item} " +
            "  </foreach> " +
            "</if>" +
            "</script>")
    List<PmWorkTeamDto> queryWorkTeamById(@Param("spaceId") String spaceId, @Param("userId") String userId,@Param("permission") List<Integer> permission);


    @Select("SELECT A.PROJECT_ID,A.PROJECT_NAME,A.IMG_URL,A.UPDATE_TIME,A.CREATE_TIME,A.HOME_ROUTER,C.USER_ROLE,C.PERMISSION,D.ACCOUNT AS CREATE_USER,D.USER_NAME,B.ID AS GROUP_ID,B.GROUP_NAME,A.DELETE_TIME  FROM WCP_SP_PROJECT A  \n" +
            " LEFT JOIN WCP_SP_TEAM_USER C ON A.TEAM_ID=C.TEAM_ID   \n" +
            " LEFT JOIN  WCP_SP_TEAM_TREE B ON B.ID=A.GROUP_ID AND B.IS_DELETE=0   \n" +
            " LEFT JOIN WCP_SP_USER D ON D.USER_ID=A.CREATE_USER   \n" +
            " WHERE A.TEAM_ID=#{teamId} AND C.USER_ID=#{userId} AND A.IS_DELETE=1 ORDER BY A.CREATE_TIME DESC")
    List<PmSpTeamRecycleProjectDto> queryTeamRecycle(String userId,String teamId);

}
