package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmTeamProject;
import com.wcp.domain.dto.PmTeamProjectDto;
import com.wcp.domain.dto.PmTeamProjectSerachDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PmTeamProjectMapper extends BaseMapper<PmTeamProject> {
    @Select("SELECT WSI2.PROJECT_ID,WSI2.PROJECT_NAME,WSI2.TEAM_ID,WSI2.IS_PUBLIC,WSI2.IMG_URL,WSI2.CREATE_USER,WSI.PERMISSION  FROM WCP_SP_PROJECT_USER WSI " +
            "  LEFT JOIN WCP_SP_PROJECT WSI2 ON WSI.TEAM_ID=WSI2.TEAM_ID AND WSI2.IS_DELETE=0 AND WSI1.TEAM_ID=#{temId} AND WSI.USER_ID=#{userId}")
    List<PmTeamProjectDto> queryTeamProjectById(String temId, String userId);

    @Select("SELECT A.PROJECT_ID,A.PROJECT_NAME,A.TEAM_ID,A.DESCR,A.CREATE_USER,A.CREATE_TIME,A.UPDATE_USER,A.UPDATE_TIME,A.IS_DELETE,A.IMG_URL,A.IS_PUBLIC,A.GROUP_ID,A.HOME_ROUTER,A.DELETE_TIME,A.PREFIX,B.USER_NAME,F.ID AS COLLECT_ID FROM WCP_SP_PROJECT A \n" +
            "LEFT JOIN WCP_SP_USER B ON A.CREATE_USER=B.USER_ID  " +
            "LEFT JOIN WCP_SP_PROJECT_FAVORITE F ON A.PROJECT_ID=F.PROJECT_ID  "+
            "  WHERE A.TEAM_ID=#{teamId} AND A.GROUP_ID=#{groupId} AND A.IS_DELETE=0 ORDER BY A.CREATE_TIME DESC ")
    List<PmTeamProject> selectProjectList(String teamId,String groupId);

    @Select("SELECT A.TEAM_ID,A.TEAM_NAME,A.CREATE_TIME,A.IMG_URL,C.USER_NAME FROM WCP_SP_TEAM A   " +
            "LEFT JOIN WCP_SP_TEAM_USER B ON A.TEAM_ID=B.TEAM_ID  " +
            "LEFT JOIN WCP_SP_USER C ON B.USER_ID=C.USER_ID  " +
            "WHERE A.IS_DELETE=0 AND B.USER_ID=#{userId}  AND A.TEAM_NAME LIKE #{content} " +
            "GROUP BY A.TEAM_ID,A.TEAM_NAME,A.CREATE_TIME,A.IMG_URL,C.USER_NAME ")
    List<PmTeamProjectSerachDto> searchTeamList(String content,String userId);

    @Select("SELECT a.PROJECT_ID,a.PROJECT_NAME,a.CREATE_TIME,a.IMG_URL,d.USER_NAME FROM wcp_sp_project a  " +
            "left join wcp_sp_team b on a.TEAM_ID=b.TEAM_ID and b.IS_DELETE=0  " +
            "left join wcp_sp_team_user c on a.TEAM_ID=c.TEAM_ID  " +
            "left join wcp_sp_user d on d.USER_ID=c.USER_ID  " +
            "where a.IS_DELETE=0 and c.USER_ID=#{userId}  AND A.PROJECT_NAME like #{content}  " +
            "group by a.PROJECT_ID,a.PROJECT_NAME,a.CREATE_TIME,a.IMG_URL,d.USER_NAME")
    List<PmTeamProjectSerachDto> searchProjectList(String content,String userId);
}
