package com.wcp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wcp.domain.PmProjectUser;
import com.wcp.domain.dto.PlmUserDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PmTeamProjectUserMapper extends BaseMapper<PmProjectUser> {

    /**
     * 搜索用户并且判断是否在项目中 邀请中过滤掉创建人的搜索
     * @param projectId
     * @param account
     * @return
     */
    @Select("<script>" +
            "SELECT A.USER_ID, A.ACCOUNT, A.USER_NAME, B.PERMISSION FROM WCP_SP_USER A " +
            "LEFT JOIN WCP_SP_PROJECT_USER B ON A.USER_ID = B.USER_ID AND B.PROJECT_ID = #{projectId}   " +
            "WHERE  (B.USER_ROLE IS NULL OR B.USER_ROLE != 2)   " +
            "<if test='account != null'> AND  A.ACCOUNT LIKE #{account}  </if> " +
            "<if test='phoneNumber != null'> AND A.PHONE_NUMBER = #{phoneNumber} </if> " +
            "<if test='email != null'> AND A.EMAIL = #{email} </if> " +
            "</script>")
    List<PlmUserDto> selectProjectByUser(String projectId, String account,String phoneNumber,String email);
}
