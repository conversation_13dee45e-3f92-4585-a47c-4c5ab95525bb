package com.wcp.domain;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 项目资源文件表实体类
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 11:59
 */
@Data
@TableName("WCP_SP_PROJECT_FILE")
public class PmSpProjectFile {
    @TableId("ID")
    @ApiModelProperty(value = "资源ID",hidden = true)
    private String id; // 资源的唯一标识符，自增主键
    @TableField("PARENT_ID")
    @ApiModelProperty(value = "关联文件夹ID",hidden = true)
    private String parentId; // WCP_SP_PROJECT_RESOURCE 的 ID

    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "资源上传时间",hidden = true)
    private Date createTime; // 资源上传时间

    @TableField("RESOURCE_NAME")
    @ApiModelProperty(value = "文件名称",required = true)
    private String resourceName; // 文件名称

    @TableField("GRAPH_CELL")
    @ApiModelProperty(value = "图元信息",hidden = true)
    private byte[] graphCell; // 图元信息，存一个二进制
    @TableField(exist = false)
    @ApiModelProperty(value = "图元数据")
    public JSONArray graphData;
    @TableField("ROOTID")
    @ApiModelProperty(value = "ROOTID",hidden = true)
    private String rootId; // ROOTID

    @TableField("USER_ACCOUNT")
    @ApiModelProperty(value = "创建人",hidden = true)
    private String userAccount; // 创建人

    @TableField("THUMBNAIL_PATH")
    @ApiModelProperty(value = "缩略图",hidden = true)
    private String thumbnailPath; // 缩略图
}
