package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

import java.util.Date;

@Data
@TableName("WCP_SP_SPACE")
@ApiModel("工作空间")
public class PmWorkSpace {

    @TableId(type = IdType.INPUT)
    @TableField("SPACE_ID")
    @ApiModelProperty(value = "空间ID",required = true)
    private String spaceId;

    @TableField("SPACE_NAME")
    @ApiModelProperty(value = "空间名称",required = true)
    private String spaceName;

    @TableField("DESCR")
    @ApiModelProperty(value = "空间描述")
    private String descr;

    @TableField("CREATE_USER")
    @ApiModelProperty(value = "空间创建者",required = true)
    private String createUser;

    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "空间创建时间",hidden = true)
    private Date createTime;

    @TableField("UPDATE_USER")
    @ApiModelProperty(value = "最后修改人",required = true)
    private String updateUser;

    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "空间创建时间",hidden = true)
    private Date updateTime;

    @TableField("IMG_URL")
    @ApiModelProperty(value = "空间logo缩略图",required = true)
    private String imgUrl;
}
