package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 1. @Description 团队下的文件夹
 * 2. <AUTHOR>
 * 3. @Date 2025/3/17 14:49
 */
@Data
@ApiModel("团队文件夹树")
@TableName("WCP_SP_TEAM_TREE")
public class PmSpTeamTree {
    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "文件夹ID", required = true, hidden = true)
    private String id;

    @TableField("TEAM_ID")
    @ApiModelProperty(value = "所属团队ID", required = true, hidden = true)
    private String teamId;

    @TableField("GROUP_NAME")
    @ApiModelProperty(value = "文件夹名称", required = true)
    private String groupName;



    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("IS_DELETE")
    private Integer isDelete;

    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "更新时间", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonIgnore
    private Date updateTime;


}
