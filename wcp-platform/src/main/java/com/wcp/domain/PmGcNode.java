package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.Date;

/**
 * Description: 图元树形结构
 * Author: qianchao
 * Date: 2024/3/4 15:15
 */
@Data
@TableName("WCP_SP_NODE")
@ApiModel("图元树形结构")
public class PmGcNode {
    /**
     * 图元ID
     */
    @TableId("PID")
    @ApiModelProperty(hidden = true)
    private String pid;

    @TableField("NODE_NAME")
    @ApiModelProperty(value = "节点名称",required = true)
    private String nodeName;

    @TableField("PARENT_ID")
    @ApiModelProperty(value = "父级节点ID")
    private String parentId;

    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目ID",hidden = true)
    private String projectId;

    @TableField("NODE_TYPE")
    @ApiModelProperty(value = "节点类型，folder:文件夹  graph：图元",required = true)
    private  String nodeType;

    @TableField("ACCOUNT")
    @ApiModelProperty(value = "用户账号",hidden = true)
    private String account;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createTime;

    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "修改时间",hidden = true)
    private Date updateTIme;

    @TableField("DEL_FLAG")
    @ApiModelProperty(value = "是否删除 0：正常   1:删除",hidden = true)
    private Integer delFlag;

    @TableField("PUB_STATUS")
    @ApiModelProperty(value = "发布状态",hidden = true)
    private Integer pubStatus;
}
