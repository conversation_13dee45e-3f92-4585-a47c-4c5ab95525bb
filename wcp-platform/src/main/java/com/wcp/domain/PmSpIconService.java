package com.wcp.domain;

import java.util.List;

/**
 * Description: 图标相关业务类
 * Author: qianchao
 * Date: 2024/3/5 11:05
 */
public interface PmSpIconService {
     /**
      * 查询图标集合
      * @return
      */
     List<PmSpIconGroup> queryIcons();
     /**
      * 查询图标分组
      * @return
      */
     public List<PmSpIconGroup> queryGroups();
     /**
      * 上传平台图标信息
      * @param pmSpIcon
      * @return
      */
     public int saveIcon(PmSpIcon pmSpIcon);

     public int saveGroup(PmSpIconGroup pmSpIconGroup);
     public int deleteGroup(String  groupId);
     /**
      * 删除指定分组图标
      * @param groupId
      * @param groupName
      * @return
      */
     public int deleteIcon(String groupId,String groupName);
     /**
      * 修改分组
      * @param pmSpIconGroup
      * @return
      */
     public int updateGroup(PmSpIconGroup pmSpIconGroup);
}
