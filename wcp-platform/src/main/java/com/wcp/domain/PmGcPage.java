package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 平台路由发布信息
 * 2. <AUTHOR>
 * 3. @Date 2025/2/12 16:48
 */
@Data
@TableName("WCP_SP_PAGE")
@ApiModel("平台图元路由信息")
public class PmGcPage {
    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "图元Id",required = true)
    private String id;

    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目ID",hidden = true)
    private String projectId;
    @TableField("ROUTER")
    @ApiModelProperty(value = "路由地址",required = true)
    private String router;

    @TableField("TITLE")
    @ApiModelProperty(value = "名称",required = true)
    private String title;
    @TableField("ICON")
    @ApiModelProperty(value = "图标",required = false)
    private String icon;
    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("ACCOUNT")
    @ApiModelProperty(value = "发布人",hidden = true)
    private String account;

    @TableField("HPOS")
    @ApiModelProperty(value = "水平位置",required = false)
    private String hpos;
    @TableField("VPOS")
    @ApiModelProperty(value = "垂直位置",required = false)
    private String vpos;

    @TableField("ADAPOS")
    @ApiModelProperty(value = "自适应属性",required = false)
    private String adapos;

    @TableField("ISVERIFY")
    @ApiModelProperty(value = "是否验证登录",required = false)
    private Boolean isVerify;
    @TableField("REROUTER")
    @ApiModelProperty(value = "未通过跳转录音",required = false)
    private String reRouter;

    @TableField("ROOTID")
    @ApiModelProperty(value = "rootId",required = false)
    private String rootId;

    @TableField(exist = false)
    private Boolean isHomeRouter;
}
