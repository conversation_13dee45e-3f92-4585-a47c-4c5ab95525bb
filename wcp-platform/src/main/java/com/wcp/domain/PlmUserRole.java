package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description: 用户和角色关联 wcp_sp_user_role
 * Author: qianchao
 * Date: 2024/1/10 16:24
 */
@Data
@TableName("WCP_SP_USER_ROLE")
@ApiModel("用户和角色关联")
public class PlmUserRole {
    /**
     * 用户ID
     */
    @TableId("USER_ID")
    @ApiModelProperty("用户ID")
    private String userId;

    /**
     * 角色ID
     */
    @TableField("ROLE_ID")
    @ApiModelProperty("角色ID")
    private Integer roleId;
}