package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 拓扑
 * 2. <AUTHOR>
 * 3. @Date 2025/6/23 14:05
 */
@Data
@TableName("WCP_GC_TOPOLOGY")
@ApiModel("拓扑")
public class PmGcTopology {
    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "'拓扑点号唯一标识 '",hidden = true)
    private String id;
    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "''项目ID' '",hidden = true)
    private String projectId;
    @TableField("NAME")
    @ApiModelProperty(value = "拓扑名称",required = false)
    private String  name;
    @TableField("SETTING")
    @ApiModelProperty(value = "拓扑data数据",required = false)
    private String  data;
    @TableField("ACCOUNT")
    @ApiModelProperty(value = "创建的用户账号",required = false)
    private String account;

    @TableField("CT")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;



}
