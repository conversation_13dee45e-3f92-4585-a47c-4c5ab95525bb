package com.wcp.domain;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.List;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
@Data
@ApiModel("登录用户身份权限")
public class PmLoginUser implements UserDetails {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String userId;


    /**
     * 用户唯一标识
     */
    @ApiModelProperty("用户唯一标识")
    private String token;

    /**
     * 登录时间
     */
    @ApiModelProperty("登录时间")
    private Long loginTime;

    /**
     * 过期时间
     */
    @ApiModelProperty("过期时间")
    private Long expireTime;

    /**
     * 登录IP地址
     */
    @ApiModelProperty("登录IP地址")
    private String ipaddr;
    /**
     * 权限列表
     */
    @ApiModelProperty(value = "权限列表",hidden = true)
    private Set<String> permissions;

    /**
     * 用户信息
     */
    @ApiModelProperty(value = "用户信息",dataType = "com.wcp.domain.PlmUser")
    private PlmUser user;

    @ApiModelProperty(hidden = true)
    private List<GrantedAuthority> authorities;
    /**
     * 头像
     */
    @ApiModelProperty(value = "头像",hidden = true)
    private String headImg;

    public PmLoginUser() {
    }

    public PmLoginUser(PlmUser user, Set<String> permissions) {
        this.user = user;
        this.permissions = permissions;
    }
    public PmLoginUser(String userId, PlmUser user) {
        this.user = user;
        this.userId = userId;
    }


    /*  public PmLoginUser(String userId, PlmUser user, Set<String> permissions) {
        this.userId = userId;
        this.user = user;
        this.permissions = permissions;
    }*/
    /*public PmLoginUser(String userId, PlmUser user, List<GrantedAuthority> authorities) {
        this.userId = userId;
        this.user = user;
        this.authorities=authorities;
    }*/
    @JSONField(serialize = false)
    @Override
    public String getPassword() {
        return user.getPwd();
    }

    @Override
    public String getUsername() {
        return user.getUserName();
    }

    /**
     * 账户是否未过期,过期无法验证
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isEnabled() {
        return true;
    }

}
