package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("WCP_SP_TEAM")
@ApiModel("工作团队")
public class PmWorkTeam {
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "团队ID",required = true)
    @TableField("TEAM_ID")
    private String teamId;

    @TableField("TEAM_NAME")
    @ApiModelProperty(value = "团队名称",required = true)
    private String teamName;

    @TableField("SPACE_ID")
    @ApiModelProperty(value = "空间ID",required = true)
    private String spaceId;

    @TableField("CREATE_USER")
    @ApiModelProperty(value = "创建者",required = true)
    private String createUser;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("UPDATE_USER")
    @ApiModelProperty(value = "最后修改人",required = true)
    private String updateUser;

    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改时间",hidden = true)
    private Date updateTime;
    @TableField("IMG_URL")
    @ApiModelProperty(value = "空间logo缩略图",required = true)
    private String imgUrl;

    @TableField("DESCR")
    @ApiModelProperty(value = "团队描述描述")
    private String descr;

    @TableField("IS_DELETE")
    private Integer isDelete;

}
