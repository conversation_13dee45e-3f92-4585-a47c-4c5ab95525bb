package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("项目信息")
@TableName("WCP_SP_PROJECT")
public class PmTeamProject {
    @TableId(type = IdType.INPUT)
    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目ID",required = true,hidden = true)
    private String projectId;

    @TableField("PROJECT_NAME")
    @ApiModelProperty(value = "项目名称",required = true)
    private String projectName;

    @TableField("TEAM_ID")
    @ApiModelProperty(value = "团队ID",required = true,hidden = true)
    private String teamId;

    @TableField("DESCR")
    @ApiModelProperty(value = "项目描述")
    private String descr;

    @TableField("CREATE_USER")
    @ApiModelProperty(value = "项目创建人",required = true,hidden = true)
    private String createUser;
    @TableField(exist = false) // 表示该字段不是数据库表中的字段
    @ApiModelProperty(value = "用户名称", hidden = true)
    private String userName;
    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "项目创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("UPDATE_USER")
    @ApiModelProperty(value = "项目修改人",hidden = true)
    @JsonIgnore
    private String updateUser;

    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "项目修改时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("IS_DELETE")
    @ApiModelProperty(value = "是否删除",hidden = true)
    @JsonIgnore
    private int isDelete;

    @TableField("IMG_URL")
    @ApiModelProperty(value = "项目logo缩略图",required = true)
    private String imgUrl;

    @TableField("IS_PUBLIC")
    @ApiModelProperty(value = "是否公开",hidden = true)
    private int isPublic;

    @TableField(exist = false)
    private String collectId;

    //返回权限，是浏览还是编辑
    @TableField(exist = false)
    private String permission;

    @TableField(exist = false)
    private String userRole;

    @TableField("GROUP_ID")
    private String groupId;

    @TableField("HOME_ROUTER")
    private String homeRouter;//首页路由

    @TableField("DELETE_TIME")
    private Date deleteTime; //删除时间


    /**
     * 项目前缀
     */
    @TableField("PREFIX")
    private String prefix;
}
