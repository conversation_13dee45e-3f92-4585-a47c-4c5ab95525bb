package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 1. @Description 数据源配置表
 * 2. <AUTHOR>
 * 3. @Date 2025/4/16 11:15
 */

@Data
@ApiModel("图元版本")
@TableName("WCP_SP_DB")
public class PmSpDataSource {
    @TableField("PROJECT")
    private String projectId;
    @TableField("SERVICE")
    private String service;
    @TableField("DB_TYPE")
    private String dbType;
    @TableField("DB_NAME")
    private String dbName;
    @TableField("ALIAS")
    private String alias;
    @TableField("DRIVER")
    private String driver;
    @TableField("USER_NAME")
    private String userName;
    @TableField("USER_PWD")
    private String userPwd;
    @TableField("URL")
    private String url;
    @TableField("MAX_CLIENTS")
    private int maxClients;
    @TableField("DESCR")
    private String descr;
}
