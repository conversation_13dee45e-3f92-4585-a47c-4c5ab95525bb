package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Description: 静态数据源 csv,excel
 * Author: qianchao
 * Date: 2024/3/19 14:26
 */
@Data
@ApiModel("静态数据源")
@TableName("WCP_SP_STATIC_DB")
public class PmStaticDb {
    @TableId(type = IdType.INPUT)
    @TableField("PID")
    @ApiModelProperty(value = "表名Id",required = true)
    private String tabId;

    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目Id",required = true)
    private String projectId;

    @TableField("TABLE_NAME")
    @ApiModelProperty(value = "表名称",required = true)
    private String tableName;

    @TableField("REMARK")
    @ApiModelProperty(value = "表名备注",required = true)
    private String remark;

    @TableField("LOCAL_PATH")
    @ApiModelProperty(value = "文件地址",required = true)
    private String localPath;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",required = true)
    private Date createTime;

    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "修改时间",required = true)
    private Date updateTime;
}
