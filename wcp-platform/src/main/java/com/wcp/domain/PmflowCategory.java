package com.wcp.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 1. @Description 工作流分类
 * 2. <AUTHOR>
 * 3. @Date 2025/5/19 13:39
 */
@Data
@TableName("WCP_FLOW_CATEGORY")
@ApiModel("工作流分类")
public class PmflowCategory {
    @TableId(value = "CATEGORY_ID")
    private String categoryId;
    @TableField("CATEGORY_CODE")
    private String categoryCode;
    @TableField("CATEGORY_NAME")
    private String categoryName;
    @TableField("SORT_INDEX")
    private Integer sortIndex;
    @TableField("PROJECT_ID")
    private String projectId;
}
