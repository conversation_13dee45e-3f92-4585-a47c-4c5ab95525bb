package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 平台项目收藏
 * 2. <AUTHOR>
 * 3. @Date 2025/3/24 10:03
 */
@TableName("WCP_SP_PROJECT_FAVORITE")
@Data
public class PmSpProjectFavorite {
    @TableId("ID")
    private String id;

    @TableField("USER_ID")
    private String userId;

    @TableField("PROJECT_ID")
    private String projectId;



    @TableField("CREATED_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;
}
