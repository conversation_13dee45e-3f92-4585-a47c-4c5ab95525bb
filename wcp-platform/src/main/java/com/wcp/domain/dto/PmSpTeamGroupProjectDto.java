package com.wcp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 查询团队分组项目
 * 2. <AUTHOR>
 * 3. @Date 2025/3/17 16:06
 */
@Data
public class PmSpTeamGroupProjectDto {
    private String id;
    private String groupName;
    private String projectId;

    private String projectName;

    private String imgUrl;
    private int isPublic;

    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String descr;
    private String groupId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String userName;

    private String userRole;

    private String permission;

    private String teamId;

    private String homeRouter;//首页路由

    private String prefix;

    private String collectId; //项目收藏ID
}
