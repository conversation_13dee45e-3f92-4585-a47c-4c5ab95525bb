package com.wcp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 资源文件列表
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 15:42
 */
@Data
public class PmSpProjectFileDto {
    private String id; // 资源的唯一标识符
    private String resourceName; // 资源名称

    private String userName; // 父资源 ID

    private String thumbnailPath;//缩略图地址

    private String parentId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime; // 创建时间
}
