package com.wcp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 最近打开的项目
 * 2. <AUTHOR>
 * 3. @Date 2025/3/18 10:59
 */
@Data
public class PmSpOpenProjectDto {
    private String projectId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date openTIme;

    private String projectName;

    private String imgUrl;
}
