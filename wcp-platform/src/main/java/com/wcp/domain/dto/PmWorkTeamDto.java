package com.wcp.domain.dto;

import com.wcp.domain.PmSpTeamTree;
import lombok.Data;

import java.util.List;

/**
 * 1. @Description 团队dto
 * 2. <AUTHOR>
 * 3. @Date 2025/3/13 14:41
 */
@Data
public class PmWorkTeamDto {
    private String teamId;
    private String teamName;
    private String spaceId;
    private String createUser;
    private int isPublic;
    private String userId;
    private String imgUrl;

    private String descr;
    private List<PmSpTeamTree> children;

    private Boolean isCreator;
}
