package com.wcp.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 1. @Description 图元dto
 * 2. <AUTHOR>
 * 3. @Date 2025/2/6 10:52
 */
@Data
public class PmGcDataDto {

    @ApiModelProperty(value = "图元ID")
    private String graphId;

    @ApiModelProperty(value = "数据返回是否压缩,值为gzip或者空")
    public String encoding;
    @ApiModelProperty(value = "组件ID")
    public String rootId;
}
