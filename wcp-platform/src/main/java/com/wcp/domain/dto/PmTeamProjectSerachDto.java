package com.wcp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description TODO 项目搜索dto
 * 2. <AUTHOR>
 * 3. @Date 2025/7/1 15:05
 */
@Data
public class PmTeamProjectSerachDto {
    private String teamId;
    private String teamName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String imgUrl;
    private String userName;

    private String projectId;

    private String projectName;
}
