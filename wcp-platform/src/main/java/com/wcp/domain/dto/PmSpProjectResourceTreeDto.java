package com.wcp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 13:54
 */
@Data
public class PmSpProjectResourceTreeDto {
    private String id; // 资源的唯一标识符
    private String resourceName; // 资源名称

    private String parentId; // 父资源 ID

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime; // 创建时间

    private List<PmSpProjectResourceTreeDto> children; // 子资源
}
