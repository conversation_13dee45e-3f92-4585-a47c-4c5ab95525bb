package com.wcp.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("项目成员")
public class ProjectUserDto {

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("用户Id")
    private String userId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("用户角色")
    private String userRole;

    @ApiModelProperty("权限")
    private String permission;


}
