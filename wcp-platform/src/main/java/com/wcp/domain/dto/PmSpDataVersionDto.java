package com.wcp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 图元历史版本Dto
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 10:17
 */
@Data
public class PmSpDataVersionDto {
    private String versionId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String versionName;
    private String remark;
    private String userName;

}
