package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("WCP_SP_SPACE_USER")
@ApiModel("空间成员")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PmWorkSpaceUser {

    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "主键",required = true)
    private String id;

    @TableField("SPACE_ID")
    @ApiModelProperty(value = "空间ID",required = true)
    private String spaceId;

    @TableField("USER_ID")
    @ApiModelProperty(value = "用户ID",required = true)
    private String userId;

    @TableField("USER_ROLE")
    @ApiModelProperty(value = "用户角色",required = true,example = "4")
    private int userRole;

    @TableField("CREATE_USER")
    @ApiModelProperty(value = "团队创建者",required = true)
    private String createUser;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @TableField("PERMISSION")
    @ApiModelProperty(value = "权限标识 VIEW或EDIT",required = true)
    private int permission;
}
