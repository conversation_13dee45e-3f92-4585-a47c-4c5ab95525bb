package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description: 平台图标
 * Author: qianchao
 * Date: 2024/3/5 10:55
 */
@Data
@TableName("WCP_SP_ICON")
@ApiModel("图标数据")
public class PmSpIcon {
    @TableField("GROUP_ID")
    @ApiModelProperty(value = "分组ID",required = true)
    private String groupId;
    @TableField("ICON_NAME")
    @ApiModelProperty(value = "图标名称",required = true)
    private String iconName;
    @TableField("ICON")
    @ApiModelProperty(value = "图标内容",required = true)
    private String icon;
    @TableField("IS_FREE")
    @ApiModelProperty(value = "是否免费",required = true)
    private int isFree;
    @TableField("ICON_TYPE")
    @ApiModelProperty(value = "图标类型",required = true)
    private String iconType;
}
