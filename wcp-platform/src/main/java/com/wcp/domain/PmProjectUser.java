package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("WCP_SP_PROJECT_USER")
@ApiModel("项目成员")
public class PmProjectUser {

    @TableField("ID")
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键",required = true)
    private String id;
    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目ID",required = true)
    private String projectId;

    @TableField("USER_ID")
    @ApiModelProperty(value = "用户ID",required = true)
    private String userId;

    @TableField("USER_ROLE")
    @ApiModelProperty(value = "用户角色",required = true,example = "4")
    private int userRole;

    @TableField("CREATE_USER")
    @ApiModelProperty(value = "创建者",required = true)
    private String createUser;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("PERMISSION")
    @ApiModelProperty(value = "权限标识 VIEW或EDIT",required = true)
    private int permission;

}
