package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 项目资源表实体类
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 11:57
 */

@Data
@ApiModel("项目资源管理器实体")
@TableName("WCP_SP_PROJECT_RESOURCE")
public class PmSpProjectResource {
    @TableId("ID")
    @ApiModelProperty(value = "版本ID",hidden = true)
    private String id; // 资源的唯一标识符，自增主键

    @TableField("RESOURCE_NAME")
    @ApiModelProperty(value = "文件夹",required = true)
    private String resourceName; // 资源名称（文件或文件夹的名称）

    @TableField("PARENT_ID")
    @ApiModelProperty(value = "上级文件夹ID",required = true)
    private String parentId; // 父资源的 ID

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime; // 资源上传时间

    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目ID",hidden = true)
    private String projectId; // 项目ID
}
