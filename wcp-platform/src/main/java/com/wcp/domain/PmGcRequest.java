package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 接口定义
 * 2. <AUTHOR>
 * 3. @Date 2025/2/14 10:21
 */
@Data
@TableName("WCP_SP_REQUEST")
@ApiModel("平台项目接口定义")
public class PmGcRequest {
    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "'唯一标识 '",hidden = true)
    private String id;
    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "''项目ID' '",hidden = true)
    private String projectId;
    @TableField("AJAX_NAME")
    @ApiModelProperty(value = "接口名称",required = true)
    private String  name;
    @TableField("REQUEST_PATH")
    @ApiModelProperty(value = "接口路径",required = true)
    private String  path;

    @TableField("SETTING")
    @ApiModelProperty(value = "接口配置",required = false)
    private String   setting;

    @TableField("ORDERINDEX")
    @ApiModelProperty(value = "排序",required = false)
    private Integer   orderIndex;
    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
