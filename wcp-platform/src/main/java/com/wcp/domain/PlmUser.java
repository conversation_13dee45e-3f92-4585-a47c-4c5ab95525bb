package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 平台用户信息表
 */
@Data
@TableName("WCP_SP_USER")
@ApiModel("平台用户")
public class PlmUser implements Serializable {
    @TableId("USER_ID")
    @ApiModelProperty(hidden = true)
    private String userId;

    @TableField("ACCOUNT")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    @ApiModelProperty(value = "用户账号",required = true)
    private String account;

    @TableField("PWD")
    @ApiModelProperty(value = "用户密码",required = true)
    private String pwd;

    @TableField("EMAIL")
    @ApiModelProperty(value = "用户密码",required = false)
    private String email;

    @TableField("GENDER")
    @ApiModelProperty(value = "性别",required = false)
    private String gender;

    @TableField("USER_NAME")
    @Size(min = 0, max = 30, message = "用户名称长度不能超过30个字符")
    @ApiModelProperty(value = "用户名称",required = true)
    private String userName;

    @TableField("PHONE_NUMBER")
    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    @ApiModelProperty(value = "手机号码",required = true)
    private String phone;

    @TableField("OPEN_ID")
    @ApiModelProperty(hidden = true)
    private String openId;
    @TableField("USER_STATUS")
    @ApiModelProperty(hidden = true)
    private int userStatus;
    @TableField("CREATE_TIME")
    @ApiModelProperty(hidden = true)
    private Date createTime;
    @TableField("COMPANY")
    @ApiModelProperty(value = "公司名称",required = true)
    private String company;

    @TableField("HEAD_IMAGE")
    private String headImage;
    /**
     * 平台角色对象
     * 表示排除，不会出现在查询语句字段中
     */
    @TableField(exist = false)
    @ApiModelProperty(hidden = true)
    private Set<PlmRole> roles;

    @ApiModelProperty(hidden = true)
    public boolean isAdmin() {
        return isAdmin(this.userId);
    }

    @ApiModelProperty(hidden = true)
    public static boolean isAdmin(String userId) {
        return userId != null && "admin".equals(userId);
    }
}
