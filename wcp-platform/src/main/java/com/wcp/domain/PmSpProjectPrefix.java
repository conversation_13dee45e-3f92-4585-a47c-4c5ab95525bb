package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 项目前缀
 * 2. <AUTHOR>
 * 3. @Date 2025/6/13 10:21
 */
@Data
@TableName("WCP_SP_PROJECT_PREFIX")
@ApiModel("平台项目前缀")
public class PmSpProjectPrefix {
    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "'唯一标识 '",hidden = true)
    private String id;
    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "''项目ID' '",hidden = true)
    private String projectId;
    @TableField("PREFIX_NAME")
    @ApiModelProperty(value = "前缀名称",required = true)
    private String  name;
    @TableField("PREFIX")
    @ApiModelProperty(value = "项目前缀",required = true)
    private String  prefix;

    @TableField("STATUS")
    @ApiModelProperty(value = "状态",required = false)
    private String   status;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
