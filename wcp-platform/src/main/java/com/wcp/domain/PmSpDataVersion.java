package com.wcp.domain;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 平台图元版本类
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 9:58
 */
@Data
@ApiModel("图元版本")
@TableName("WCP_SP_DATA_VERSON")
public class PmSpDataVersion {
    /**
     * 图元id
     */
    @TableId("VERSION_ID")
    @ApiModelProperty(value = "版本ID",hidden = true)
    private String versionId;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 图元信息,存一个二进制
     */
    @TableField("GRAPH_CELL")
    private byte[] graphCell;

    @TableField(exist = false)
    @ApiModelProperty(value = "图元数据")
    public JSONArray graphData;

    /**
     * ROOTID
     */
    @TableField("ROOTID")
    @ApiModelProperty(value = "ROOTID",required = true)
    private String rootId;

    /**
     * 版本名称
     */
    @TableField("VERSION_NAME")
    @ApiModelProperty(value = "版本名称",required = true)
    private String versionName;

    /**
     * 备注
     */
    @TableField("REMARK")
    @ApiModelProperty(value = "备注",required = true)
    private String remark;

    @TableField(exist = false)
    private String graphId;
    /**
     * 父级图元ID,关联的是wcp_gc_data表ID
     */
    @TableField("PARENT_GRAPH_ID")
    @ApiModelProperty(value = "父级图元ID",hidden = true)
    private String parentGraphId;

    @TableField("USER_ACCOUNT")
    @ApiModelProperty(value = "创建人",hidden = true)
    private String userAccount;

}
