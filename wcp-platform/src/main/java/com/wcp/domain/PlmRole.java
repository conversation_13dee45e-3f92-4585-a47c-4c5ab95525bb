package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description: 平台角色
 * Author: qianchao
 * Date: 2024/1/8 17:47
 */
@Data
@TableName("WCP_SP_ROLE")
@ApiModel("平台角色")
public class PlmRole implements Serializable {
    /**
     * 角色ID
     */
    @TableId("ROLE_ID")
    @ApiModelProperty(value = "角色ID",example = "1")
    private Integer roleId;

    /**
     * 角色名称
     */
    @TableField("ROLE_NAME")
    @ApiModelProperty("角色名称")
    private String roleName;

    /**
     * 角色权限
     */
    @TableField("ROLE_KEY")
    @ApiModelProperty("角色权限")
    private String roleKey;

}
