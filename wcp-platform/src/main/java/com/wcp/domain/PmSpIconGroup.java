package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Description: 图标分组
 * Author: qianchao
 * Date: 2024/3/5 10:57
 */
@Data
@TableName("WCP_SP_ICONGROUP")
@ApiModel("图标分组")
public class PmSpIconGroup {
    @TableId("GROUP_ID")
    @ApiModelProperty(value = "分组标志",hidden = true)
    private String groupId;
    @TableField("GROUP_NAME")
    @ApiModelProperty(value = "分组名称",required = true)
    private String groupName;
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PmSpIcon> icons;
}
