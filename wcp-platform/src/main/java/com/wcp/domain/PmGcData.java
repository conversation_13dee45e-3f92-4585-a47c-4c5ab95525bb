package com.wcp.domain;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Description: 图元操作类
 * Author: qianchao
 * Date: 2024/3/13 15:51
 */
@Data
@TableName("WCP_SP_DATA")
@ApiModel("平台图元实体")
public class PmGcData {
    @TableId("GRAPH_ID")
    @ApiModelProperty(value = "图元ID")
    private String graphId;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createTime;

    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "修改时间",hidden = true)
    private Date updateTIme;

    @TableField("GRAPH_CELL")
    @ApiModelProperty(value = "图元类容",hidden = true)
    private byte[] graphCell;
    //接收前段图元数据
    @TableField(exist = false)
    @ApiModelProperty(value = "图元数据")
    public JSONArray graphData;

    @TableField("ROOTID")
    @ApiModelProperty(value = "rootId",required = false)
    private String rootId;

    @TableField("PROJECT_ID")
    private String projectId;

}
