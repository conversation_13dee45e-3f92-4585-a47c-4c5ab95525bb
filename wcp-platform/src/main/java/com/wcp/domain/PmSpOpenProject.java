package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 最近打开的项目
 * 2. <AUTHOR>
 * 3. @Date 2025/3/14 16:07
 */
@Data
@TableName("WCP_SP_PROJECT_OPEN")
public class PmSpOpenProject {
    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "主键",required = true,hidden = true)
    private String id;

    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目ID",required = true,hidden = true)
    private String projectId;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "打开时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @TableField("USER_ID")
    @ApiModelProperty(value = "用户ID",required = true)
    private String userId;

}
