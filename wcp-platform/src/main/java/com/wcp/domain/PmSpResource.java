package com.wcp.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 1. @Description 平台资源管理器
 * 2. <AUTHOR>
 * 3. @Date 2025/2/26 9:23
 */
@Data
@TableName("WCP_SP_RESOURCE")
@ApiModel("平台资源管理器")
public class PmSpResource {
    @TableId(type = IdType.INPUT)
    @TableField("ID")
    @ApiModelProperty(value = "'唯一标识 '",hidden = true)
    private String id; // 资源的唯一标识符
    @TableField("RESOURCE_NAME")
    @ApiModelProperty(value = "资源名称",required = false)
    private String resourceName; // 资源名称（文件或文件夹的名称）

    @TableField("RESOURCE_TYPE")
    @ApiModelProperty(value = "资源类型",required = false)
    private String resourceType; // 资源类型（如 JPEG、PNG、文件夹等）

    @TableField("FILE_SIZE")
    @ApiModelProperty(value = "大小",hidden = true)
    private String fileSize; // 资源大小（以字节为单位），文件夹时为空
    @TableField("FILE_URL")
    @ApiModelProperty(value = "资源路径",hidden = true)
    private String fileUrl;//资源路径

    @TableField("PARENT_ID")
    @ApiModelProperty(value = "父资源ID")
    private String parentId; // 父资源的 ID（如果是文件夹内的文件或子文件夹），根资源为 NULL

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "资源上传时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime; // 资源上传时间

    @TableField("IS_FOLDER")
    @ApiModelProperty(value = "是否为文件夹",hidden = true)
    private Integer isFolder; // 是否为文件夹（1 表示是文件夹，0 表示是文件）

    @TableField("PROJECT_ID")
    @ApiModelProperty(value = "项目ID",hidden = true)
    private String projectId; // 项目ID
}
