package com.wcp.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 1. @Description 微信用户
 * 2. <AUTHOR>
 * 3. @Date 2024/10/23 14:04
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeChatUser {
    /**
     * 用户唯一标识
     */
    private String openid;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     *  用户性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    private String sex;

    /**
     * 用户所在国家
     */
    private String country;

    /**
     * 用户所在城市
     */
    private String city;

    /**
     * 用户所在省份
     */
    private String province;

    /**
     * 用户头像
     */
    private String headimgurl;

    /**
     * 用户特权信息，json 数组
     */
    private String privilege;

    private String access_token;

}
