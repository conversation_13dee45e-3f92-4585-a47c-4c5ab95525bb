package com.wcp.utils;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Description: 图元树形节点转换
 * Author: qianchao
 * Date: 2024/3/4 16:50
 */
@Data
public class TreeNode {
    //节点ID
    private String key;
    //节点名称
    private String nodeName;
    //上级节点
    private String parentId;
    //节点类型
    private String nodeType;
    //创建时间
    private String createTime;
    private List<TreeNode> children;
    //发布状态
    private Integer pubStatus;
}
