package com.wcp.utils;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 1. @Description 筛选minio文件
 * 2. <AUTHOR>
 * 3. @Date 2025/2/25 15:08
 */

public class FIleUtil {
    // 解析 JSONArray
    public static List<JSONObject> parseFolderStructure(JSONArray jsonArray) {
        JSONArray outerChildren = getOuterChildren(jsonArray);
        List<JSONObject> folders = new ArrayList<>();
        for (int i = 0; i < outerChildren.size(); i++) {
            JSONObject item = outerChildren.getJSONObject(i);
            JSONObject folder = parseFolderNode(item);
            if (folder != null) {
                folders.add(folder);
            }
        }
        return folders;
    }

    // 递归解析文件夹节点
    private static JSONObject parseFolderNode(JSONObject node) {
        if (node == null) {
            return null;
        }

        // 如果是文件夹节点（包含 "children" 字段）
        if (node.containsKey("children")) {
            JSONObject folder = new JSONObject();
            folder.put("name", node.getString("name"));
            folder.put("url", node.getString("url"));

            JSONArray children = node.getJSONArray("children");
            JSONArray folderChildren = new JSONArray();

            for (int i = 0; i < children.size(); i++) {
                JSONObject child = children.getJSONObject(i);
                JSONObject childFolder = parseFolderNode(child);
                if (childFolder != null) {
                    folderChildren.add(childFolder);
                }
            }

            folder.put("children", folderChildren);
            return folder;
        }

        // 如果是文件节点，返回 null
        return null;
    }

    // 获取最外层的 children
    public static JSONArray getOuterChildren(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) {
            return new JSONArray();
        }

        // 获取最外层的第一个节点（假设只有一个外层节点）
        JSONObject outerNode = jsonArray.getJSONObject(0);
        if (outerNode.containsKey("children")) {
            return outerNode.getJSONArray("children");
        }

        return new JSONArray();
    }


}
