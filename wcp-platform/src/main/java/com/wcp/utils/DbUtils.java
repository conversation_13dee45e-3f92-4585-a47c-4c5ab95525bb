package com.wcp.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.beans.PropertyDescriptor;
import java.util.List;

/**
 * 1. @Description 联合主键saveOrUpdate
 * 2. <AUTHOR>
 * 3. @Date 2025/6/26 10:50
 */

public class DbUtils {
    /**
     * 通用 insertOrUpdate 方法，支持联合主键。
     *
     * @param <T> 实体类型
     * @param list 数据列表
     * @param mapper 对应的 BaseMapper
     * @param keyFields 联合主键字段名（如：["projectId", "pid"]）
     */
    public static <T> void insertOrUpdateBatch(List<T> list, BaseMapper<T> mapper, List<String> keyFields) {
        if (list == null || list.isEmpty()) return;

        for (T item : list) {
            QueryWrapper<T> wrapper = new QueryWrapper<>();

            for (String field : keyFields) {
                Object value = getFieldValue(item, field);
                if (value != null) {
                    wrapper.eq(camelToUnderscore(field), value); // 字段转为数据库列名（驼峰转下划线）
                }
            }

            T existing = mapper.selectOne(wrapper);
            if (existing != null) {
                mapper.update(item, wrapper);
            } else {
                mapper.insert(item);
            }
        }
    }

    // 利用反射获取字段值
    private static Object getFieldValue(Object obj, String fieldName) {
        try {
            PropertyDescriptor pd = new PropertyDescriptor(fieldName, obj.getClass());
            return pd.getReadMethod().invoke(obj);
        } catch (Exception e) {
            throw new RuntimeException("无法获取字段值: " + fieldName, e);
        }
    }

    // 驼峰转下划线（projectId -> project_id）
    private static String camelToUnderscore(String str) {
        return str.replaceAll("([a-z])([A-Z]+)", "$1_$2").toLowerCase();
    }
}
