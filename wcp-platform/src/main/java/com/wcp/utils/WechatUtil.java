package com.wcp.utils;

import com.wcp.core.config.WeChatConfig;
import com.wcp.domain.WeChatUser;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import com.alibaba.fastjson.JSON;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2024/10/23 14:05
 */
@Configuration
public class WechatUtil {
    private  TokenInfo tokenInfo;
    @Autowired
    private WeChatConfig weChatConfig;
    public  WeChatUser getUserInfo(String code) throws Exception{
        //构造http请求客户端
        CloseableHttpClient httpclient = HttpClients.createDefault();
        //用code交换token，code为扫码后微信服务器响应来的值
        String smsUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?" +
                "appid=" + weChatConfig.getOpenAppid() +
                "&SECRET=" + weChatConfig.getOpenAppsecret() +
                "&code=" + code +
                "&grant_type=authorization_code";
        //发送请求
        HttpGet httpGet = new HttpGet(smsUrl);
        String responseResult = "";
        //接收返回的数据，转换成utf-8格式
        HttpResponse response = httpclient.execute(httpGet);
        if(response.getStatusLine().getStatusCode() == 200){
            responseResult = EntityUtils.toString(response.getEntity(), "utf-8");
        }
        System.out.println("获取accessToken返回结果：{}" + responseResult);
        //将结果封装到TokenInfo对象中
        JSON.parseObject(responseResult, TokenInfo.class);
        //将结果封装到TokenInfo对象中
        tokenInfo = JSON.parseObject(responseResult, TokenInfo.class);
        //用accessToken获取扫码人的个人信息
        String userInfoUrl = "https://api.weixin.qq.com/sns/userinfo?" +
                "access_token=" + tokenInfo.getAccess_token() +
                "&openid=" + tokenInfo.getOpenid() +
                "&lang=zh_CN";
        //构造http请求客户端
        HttpGet httpGet1 = new HttpGet(userInfoUrl);

        //接收数据
        HttpResponse response1 = httpclient.execute(httpGet1);
        if(response1.getStatusLine().getStatusCode() == 200){
            responseResult = EntityUtils.toString(response1.getEntity(), "utf-8");
        }
        System.out.println("获取个人信息返回：{}" + responseResult);
        //将收到的用户信息转化为WeChatUser对象
        WeChatUser weChatUser = JSON.parseObject(responseResult, WeChatUser.class);
        weChatUser.setAccess_token(tokenInfo.getAccess_token());
        return weChatUser;

    }

}
