package com.wcp.utils;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Description: 图元树形节点转换
 * Author: qianchao
 * Date: 2024/3/4 16:50
 */
@Data
public class ResourceTree {
    //节点ID
    private String id;
    //节点名称
    private String nodeName;
    //上级节点
    private String parentId;

    //节点类型
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String nodeType;
    //创建时间
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private List<ResourceTree> children;

}
