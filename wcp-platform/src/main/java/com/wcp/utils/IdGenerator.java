package com.wcp.utils;
import java.security.SecureRandom;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/4/30 15:10
 */

public class IdGenerator {
    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom random = new SecureRandom();

    public static String generateId(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHAR_POOL.charAt(random.nextInt(CHAR_POOL.length())));
        }
        return sb.toString();
    }
    public static String generateUniqueId() {
        String prefix = "bi";
        String randomPart = generateId(6); // 剩余 6 位
        return (prefix + randomPart).toLowerCase();        // 示例：ID83KL2QZ
    }
    public static void main(String[] args) {
        System.out.println(generateUniqueId().toLowerCase());  // 示例：G7X2B9LQ
    }
}
