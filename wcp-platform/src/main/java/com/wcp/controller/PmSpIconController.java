package com.wcp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.core.controller.BaseController;
import com.wcp.core.http.PmResult;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PmSpIcon;
import com.wcp.domain.PmSpIconGroup;
import com.wcp.minio.MinioService;
import com.wcp.domain.PmSpIconService;
import com.wcp.utils.FIleUtil;
import io.minio.errors.MinioException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * Description: 图标控制类
 * Author: qianchao
 * Date: 2024/3/5 11:14
 */
@RestController
@RequestMapping("/icon")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"图标/图片管理"})
public class PmSpIconController extends BaseController {

    @Autowired
    private PmSpIconService pmSpIconService;
    /**
     * 查询图标集合
     * @return
     */
    @GetMapping("/query-icons")
    @ApiOperation("查询图标数据")
    public PmResult queryIcons(){
        return PmResult.success(pmSpIconService.queryIcons());
    }

    @GetMapping("/query-group")
    @ApiOperation("查询图标分组")
    public PmResult queryGroup(){
        return PmResult.success(pmSpIconService.queryGroups());
    }

    @PostMapping("/save-icons")
    @ApiOperation("上传图标")
    public PmResult saveIcons(@RequestBody PmSpIcon pmSpIcon){
        int result=pmSpIconService.saveIcon(pmSpIcon);
        if(result>0){
            return PmResult.success("上传成功");
        }else{
            return PmResult.error("上传失败");
        }
    }
    /**
     * 查询项目的图片树
     * @return
     */
    @GetMapping("/query-file-topo")
    @ApiOperation(value = "查询项目下的文件树形结构",notes = "根据项目ID查询minio上的文件树形结构")
    public PmResult queryFileTopo(){
        JSONArray jsonArray =MinioService.listFiles(getProjectId());
        List<JSONObject> fileList=FIleUtil.parseFolderStructure(jsonArray);
        return PmResult.success(fileList);
    }

    @PostMapping("/query-file")
    @ApiOperation(value = "查询项目下的子集",notes = "查询项目下的子集")
    public PmResult queryFiles(@RequestBody JSONObject jsonObject) throws MinioException, IOException, NoSuchAlgorithmException, InvalidKeyException {
      /*  String url=jsonObject.getString("url");
        List<Map<String, Object>> jsonArray =MinioService.buildTreeStructure(url);*/
        return PmResult.success();
    }

    /**
     * 创建图片资源目录
     * @param jsonObject
     * @return
     * @throws Exception
     */
    @PostMapping("/add-resource-folder")
    @ApiOperation(value = "图片资源管理器新增文件夹",notes = "图片资源管理器新增文件夹")
    public PmResult addResourceFolder(@RequestBody JSONObject jsonObject) throws Exception{
        String folderPath=jsonObject.getString("folderPath"); //当前选中的文件夹的路径
        String folderName=jsonObject.getString("folderName");//需要新增的文件夹名称
        String path=folderPath+"/"+folderName+"/";
        if(MinioService.isFolderExist(path)){
            return PmResult.error("该文件夹已存在!");
        }
        return PmStringUtils.isNotEmpty(MinioService.createFolder(folderPath+"/"+folderName+"/"))?PmResult.success():PmResult.error();
    }

    /**
     * 文件夹重命名
     * @param jsonObject
     * @return
     * @throws Exception
     */
    @PostMapping("/rename-folder")
    @ApiOperation(value = "图片资源管理器文件夹文件夹重命名",notes = "图片资源管理器文件夹文件夹重命名")
    public PmResult renameFolder(@RequestBody JSONObject jsonObject) throws Exception{
        String oldFolderName=jsonObject.getString("oldFolderName"); //当前选中的文件夹的路径
        String newFileName=jsonObject.getString("newFileName");//需要新增的文件夹名称
        MinioService.renameFolder(oldFolderName,newFileName);
        return PmResult.success();
    }

    @PostMapping("/delete-group")
    @ApiOperation(value = "删除分组",notes = "删除分组")
    public PmResult deleteGroup(@RequestBody PmSpIconGroup pmSpIconGroup){
        int result=pmSpIconService.deleteGroup(pmSpIconGroup.getGroupId());
        return PmResult.success(result>0?"删除成功":"删除失败");
    }

    @PostMapping("/save-group")
    @ApiOperation(value = "新增分组",notes = "新增分组")
    public PmResult saveGroup(@RequestBody PmSpIconGroup pmSpIconGroup){
        int result=pmSpIconService.saveGroup(pmSpIconGroup);
        return PmResult.success(result>0?"新增成功":"新增失败");
    }
    @PostMapping("/delete-icon")
    @ApiOperation(value = "删除图标",notes = "删除图标")
    public PmResult deleteIcon(@RequestBody PmSpIcon pmSpIcon){
        String groupId=pmSpIcon.getGroupId();
        String iconName=pmSpIcon.getIconName();
        int result=pmSpIconService.deleteIcon(groupId,iconName);
        return PmResult.success(result>0?"删除成功":"删除失败");
    }

    @PostMapping("/update-group")
    @ApiOperation(value = "修改分组",notes = "修改分组")
    public PmResult updateGroup(@RequestBody PmSpIconGroup pmSpIconGroup){
        int result=pmSpIconService.updateGroup(pmSpIconGroup);
        return PmResult.success(result>0?"修改成功":"修改失败");
    }
}
