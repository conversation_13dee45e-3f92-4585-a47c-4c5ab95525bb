package com.wcp.controller;

import com.wcp.core.http.PmResult;
import com.wcp.service.ProjectExporterService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;

/**
 * 1. @Description 项目导入导出
 * 2. <AUTHOR>
 * 3. @Date 2025/3/25 9:00
 */
@RestController
@RequestMapping("/project/resource")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
public class ProjectExporterController {
    @Autowired
    private ProjectExporterService projectExporterService;
    @ApiOperation("导出项目")
    @GetMapping("/export")
    public void export(String projectId, HttpServletResponse response){
        Path zipPath=null;
        File zipFile = null;
        try {
            //step1：导出文件并且压缩
             zipPath = projectExporterService.exportProject(projectId);
            // Step 2: 构造 zip 文件路径
            zipFile = zipPath.toFile();
            if (!zipFile.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("导出失败，压缩文件未生成");
                return;
            }
            // Step 3: 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(zipFile.getName(), StandardCharsets.UTF_8));
            response.setContentLengthLong(zipFile.length());

            // Step 4: 文件写入响应流
            try (InputStream is = new FileInputStream(zipFile);
                 OutputStream os = response.getOutputStream()) {
                is.transferTo(os);
                os.flush();
            }
        }catch (Exception e){
            e.printStackTrace();
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("导出失败：" + e.getMessage());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }finally {
            // Step 4: 下载完成后删除临时文件
            if (zipFile != null && zipFile.exists()) {
                if (!zipFile.delete()) {
                    System.err.println("警告：临时文件删除失败：" + zipFile.getAbsolutePath());
                }
            }
        }
    }

    @ApiOperation("导入项目")
    @PostMapping("/import")
    public PmResult imports(@RequestParam("file") MultipartFile file){
        if (file.isEmpty()) {
            return PmResult.error("上传文件为空");
        }
        String message=projectExporterService.importProject(file);
        return PmResult.success(message);
    }

    @ApiOperation("复制项目")
    @GetMapping("/copy")
    public PmResult copy(String projectId){
       projectExporterService.copyProject(projectId);
        return PmResult.success();
    }
}
