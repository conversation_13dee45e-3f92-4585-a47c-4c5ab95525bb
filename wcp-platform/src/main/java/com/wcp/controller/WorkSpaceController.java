package com.wcp.controller;

import com.wcp.core.controller.BaseController;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmWorkSpace;
import com.wcp.domain.dto.PmWorkSpaceDto;
import com.wcp.service.PmWorkSpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description: 工作空间模块相关控制类
 * Author: qianchao
 * Date: 2024/1/15 14:55
 */

@RestController
@RequestMapping("/space")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"工作空间接口"})
public class WorkSpaceController extends BaseController {

    @Autowired
    private PmWorkSpaceService workSpaceService;

    /**
     * 获取空间列表
     * @return list
     */
    @GetMapping("/list")
    @ApiOperation("获取当前登录用户下的空间列表")
    public PmResult getWorkSpaceById() {
        List<PmWorkSpaceDto> pmWorkSpaces = workSpaceService.queryWorkSpaceByLoginUser();
        return PmResult.success(pmWorkSpaces);
    }

    /**
     * 添加一个工作空间
     * @return boolean
     */
    @PostMapping("/add")
    @ApiOperation("添加一个工作空间")
    public PmResult addWorkSpace(@RequestBody PmWorkSpace pmWorkSpace) {
        Boolean add = workSpaceService.createWorkSpace(null,pmWorkSpace);
        return add ? PmResult.success() :PmResult.error();
    }

    /**
     * 修改一个工作空间
     * @return boolean
     */
    @PostMapping("/update")
    @ApiOperation("修改工作空间")
    public PmResult updatedWorkSpace(@RequestBody PmWorkSpace pmWorkSpace) {
        Boolean add = workSpaceService.updateWorkSpace(null,pmWorkSpace);
        return add ? PmResult.success() :PmResult.error();
    }

    /**
     * 删除一个空间
     * @param spaceId 空间id
     * @return boolean
     */
    @PostMapping("/remove")
    @ApiOperation("删除一个空间")
    @ApiImplicitParam(name = "spaceId",value = "空间ID", required = true, paramType = "form")
    public PmResult removeWorkSpace(String spaceId) {
        Boolean delete = workSpaceService.deleteWorkSpace(spaceId);
        return delete ? PmResult.success() :PmResult.error();
    }
}
