package com.wcp.controller;

import com.wcp.core.http.PmResult;
import com.wcp.domain.PmSpTeamTree;
import com.wcp.service.PmSpTeamTreeService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 1. @Description 团队分组
 * 2. <AUTHOR>
 * 3. @Date 2025/3/17 15:01
 */
@RestController
@RequestMapping("/team/group")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
public class PmSpTeamTreeController {
    @Autowired
    private PmSpTeamTreeService pmSpTeamTreeService;
    @ApiOperation("添加分组")
    @PostMapping("/add")
    public PmResult addGroup(@RequestBody PmSpTeamTree pmSpTeamTree){
        boolean result=pmSpTeamTreeService.addGroup(pmSpTeamTree);
        return result?PmResult.success():PmResult.error();
    }

    @ApiOperation("添加分组")
    @PostMapping("/update")
    public PmResult update(@RequestBody PmSpTeamTree pmSpTeamTree){
        boolean result=pmSpTeamTreeService.updateGroup(pmSpTeamTree);
        return result?PmResult.success():PmResult.error();
    }
}
