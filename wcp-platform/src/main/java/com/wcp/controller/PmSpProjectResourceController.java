package com.wcp.controller;

import com.wcp.core.http.PmResult;
import com.wcp.domain.PmSpProjectFile;
import com.wcp.domain.PmSpProjectResource;
import com.wcp.domain.dto.PmSpProjectFileDto;
import com.wcp.service.PmSpProjectFileService;
import com.wcp.service.PmSpProjectResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 1. @Description 项目资源管理器
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 13:58
 */
@RestController
@RequestMapping("/project/resource")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"项目资源管理器"})
public class PmSpProjectResourceController {

    @Autowired
    private PmSpProjectResourceService pmSpProjectResourceService;
    @Autowired
    private PmSpProjectFileService pmSpProjectFileService;

    @GetMapping("/tree-list")
    @ApiOperation("资源树列表")
    public PmResult getResourceTree(@RequestHeader("projectid") String projectid) {
        return PmResult.success(pmSpProjectResourceService.getResourceTree(projectid));
    }
    @PostMapping("/add")
    @ApiOperation("新增文件夹")
    public PmResult addResource(@RequestBody PmSpProjectResource resource,@RequestHeader("projectid") String projectid) {
         int result=pmSpProjectResourceService.addResource(resource,projectid);
        return result>0?PmResult.success("新增成功"):PmResult.error("新增失败");
    }

    @PutMapping("/update")
    @ApiOperation("修改文件夹")
    public PmResult updateResource(@RequestBody PmSpProjectResource resource) {
        int result=pmSpProjectResourceService.updateResource(resource);
        return result>0?PmResult.success("修改成功"):PmResult.error("修改失败");
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除文件夹")
    public PmResult deleteResource(@RequestParam String id) {
        int result= pmSpProjectResourceService.deleteResource(id);
        return result>0?PmResult.success("删除成功"):PmResult.error("删除失败");
    }

    @PostMapping("/upload")
    @ApiOperation("上传资源文件")
    public PmResult uploadFile(@RequestParam("file") MultipartFile file, @RequestHeader("projectid") String projectid, @ModelAttribute PmSpProjectFile pmSpProjectFile
    ) {
        int result=pmSpProjectFileService.uploadFile(file, projectid, pmSpProjectFile);
        return result>0?PmResult.success("新增成功"):PmResult.error("新增失败");
    }

    @PutMapping("/update-file")
    @ApiOperation("修改资源文件")
    public PmResult updateFile(@RequestParam(value = "file",required = false) MultipartFile file, @RequestHeader("projectid") String projectid, @ModelAttribute PmSpProjectFile pmSpProjectFile
    ) {
        int result=pmSpProjectFileService.updateFile(file, projectid, pmSpProjectFile);
        return result>0?PmResult.success("修改成功"):PmResult.error("修改失败");
    }

    @DeleteMapping("/delete-file")
    @ApiOperation("删除单个文件")
    public PmResult deleteFile(@RequestParam String id) {
        int result= pmSpProjectFileService.deleteFile(id);
        return result>0?PmResult.success("删除成功"):PmResult.error("删除失败");
    }

    @GetMapping("/list")
    @ApiOperation("资源列表")
    public PmResult list(@RequestParam String id) {
        List<PmSpProjectFileDto> pmSpProjectFileDtoList=pmSpProjectFileService.queryFileList(id);
        return PmResult.success(pmSpProjectFileDtoList);
    }

    @GetMapping("/qeury-detail")
    @ApiOperation("查询资源详情")
    public PmResult list(@RequestParam String id,@RequestParam String encoding) throws IOException {
        Object result=pmSpProjectFileService.queryDetail(id,encoding);
        return result!=null? PmResult.success(result):PmResult.error();
    }
}
