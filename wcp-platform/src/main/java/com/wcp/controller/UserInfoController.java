package com.wcp.controller;
import com.wcp.core.controller.BaseController;

import com.wcp.core.http.PmResult;
import com.wcp.service.IUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户相关接口
 */
@RestController
@RequestMapping("/user")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"用户信息接口"})
public class UserInfoController extends BaseController {
    @Autowired
    public IUserInfoService userInfoService;

    /**
     * 获取用户账号信息
     * @return
     */
    @GetMapping("/queryMe")
    @ApiOperation("查询当前用户信息")
    public PmResult queryMe(){
       return PmResult.success(userInfoService.queryMe(getUserId()));
    }


}
