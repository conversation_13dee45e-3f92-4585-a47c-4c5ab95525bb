package com.wcp.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.wcp.core.controller.BaseController;
import com.wcp.core.http.PmResult;
import com.wcp.core.page.TableDataInfo;
import com.wcp.domain.PmGcRequest;
import com.wcp.service.PmGcAjaxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 1. @Description 数据接口管理
 * 2. <AUTHOR>
 * 3. @Date 2025/2/14 11:05
 */
@RestController
@RequestMapping("/request")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"项目接口管理"})
public class PmGcAjaxController extends BaseController {
    @Autowired
    private PmGcAjaxService pmGcAjaxService;

    /**
     * 接口信息保存
     * @param jsonObject
     * @return
     */
    @PostMapping("/save")
    @ApiOperation("接口保存")
    public PmResult ajaxSave(@RequestBody JSONObject jsonObject,@RequestHeader("projectid") String projectid){
        PmGcRequest pmGcAjax=new PmGcRequest();
        pmGcAjax.setProjectId(projectid);
        pmGcAjax.setPath(jsonObject.getString("path"));
        pmGcAjax.setName(jsonObject.getString("name"));
        pmGcAjax.setOrderIndex(jsonObject.getInteger("index"));
        pmGcAjax.setSetting(jsonObject.toJSONString());
        pmGcAjax.setId(jsonObject.getString("id"));
        int result=pmGcAjaxService.ajaxSave(pmGcAjax);
        return result>0?PmResult.success():PmResult.error();
    }


    /**
     * 获取系统配置的接口列表信息
     * @param projectid
     * @return
     */
    @PostMapping("/list")
    @ApiOperation("项目接口列表信息")
    public PmResult ajaxList(@RequestBody JSONObject jsonObject,@RequestHeader("projectid") String projectid){
        int pageNum=jsonObject.getInteger("pageNum");
        int pageSize=jsonObject.getInteger("pageSize");
        PageHelper.startPage(pageNum, pageSize);
        List<JSONObject> ajaxList=pmGcAjaxService.ajaxList(projectid);
        TableDataInfo tableDataInfo=getDataTable(ajaxList);
        JSONObject json=new JSONObject();
        json.put("rows",tableDataInfo.getRows());
        json.put("total",tableDataInfo.getTotal());
        return PmResult.success(json);
    }



    /**
     * 接口删除
     * @param pmGcAjax
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation("接口删除")
    public PmResult ajaxDelete(@RequestBody PmGcRequest pmGcAjax){
        int result=pmGcAjaxService.ajaxDelete(pmGcAjax);
        return result>0?PmResult.success():PmResult.error();
    }
}
