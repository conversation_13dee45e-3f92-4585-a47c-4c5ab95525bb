package com.wcp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.controller.BaseController;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmGcTopology;
import com.wcp.domain.PmLoginUser;
import com.wcp.service.PmGcTopologyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 1. @Description 拓扑管理
 * 2. <AUTHOR>
 * 3. @Date 2025/6/23 14:05
 */
@RestController
@RequestMapping("/topology")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"拓扑管理"})
public class PmGcTopologyController extends BaseController {

    @Autowired
    private PmGcTopologyService pmGcTopologyService;

    @PostMapping("/query-idxs")
    @ApiOperation("查询项目下的拓扑图(不包含data)")
    public PmResult queryTopologyIdxs(@RequestHeader("projectid") String projectid) {
        List<PmGcTopology> list = pmGcTopologyService.queryTopologyIdxs(projectid);
        return PmResult.success(list);
    }

    @PostMapping("/list")
    @ApiOperation("查询项目下的拓扑图")
    public PmResult queryTopologyList(@RequestHeader("projectid") String projectid) {
        List<PmGcTopology> list = pmGcTopologyService.queryTopologyList(projectid);
        return PmResult.success(list);
    }

    @PostMapping("/query-topology")
    @ApiOperation("查询拓扑图 根据id")
    public PmResult queryTopologyById(@RequestBody JSONObject jsonObject, @RequestHeader("projectid") String projectid) {
        JSONArray topoIdArray = jsonObject.getJSONArray("topologyId");
        List<PmGcTopology> topologys = pmGcTopologyService.queryTopologyById(projectid, topoIdArray.toArray(new String[0]));
        return PmResult.success(topologys);
    }

    @PostMapping("/save")
    @ApiOperation("拓扑图保存")
    public PmResult save(@RequestBody @Validated PmGcTopology pmGcTopology, @RequestHeader("projectid") String projectid) {
        PmLoginUser loginUser = SecurityUtils.getLoginUser();
        String account = loginUser.getUser().getAccount();
        pmGcTopology.setAccount(account);
        pmGcTopology.setProjectId(projectid);
        int result = pmGcTopologyService.saveTopology(pmGcTopology);
        return result > 0 ? PmResult.success() : PmResult.error();
    }

    @PostMapping("/delete")
    @ApiOperation("拓扑图删除")
    public PmResult delete(@RequestBody List<String> ids, @RequestHeader("projectid") String projectid){
        System.out.println(ids);
        int count = pmGcTopologyService.deleteTopologyByIds(projectid, ids);

        return count > 0 ?
                PmResult.success("成功删除 " + count + " 条记录") :
                PmResult.error("删除失败或记录不存在");
    }
}
