package com.wcp.controller;
import com.alibaba.fastjson.JSONObject;
import com.wcp.core.controller.BaseController;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmSpDataSource;
import com.wcp.minio.MinioService;
import com.wcp.service.PmSpDataSourceService;
import io.dataease.plugins.common.dto.datasource.TableDesc;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Description: 平台数据源相关
 * Author: qianchao
 * Date: 2024/3/18 11:05
 */
@RestController
@RequestMapping("/datasource")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"平台数据源"})
public class DataSourceController extends BaseController {
    @Autowired
    private PmSpDataSourceService pmSpDataSourceService;
    @GetMapping("/getMinioDB")
    @ApiOperation("查询静态数据")
    public PmResult getMinioDB(){
        return PmResult.success(MinioService.executeMinioQuery("WCP_FX_INSPECTION","select STCD  from WCP_FX_INSPECTION limit 100"));
    }

    /**
     * 获取数据源
     * @param jsonObject
     * @param projectid
     * @return
     */
    @PostMapping("/list")
    public PmResult getDataSource(@RequestBody JSONObject jsonObject,@RequestHeader("projectid") String projectid){
        String sourceType=jsonObject.getString("sourceType");
        List<PmSpDataSource> dataSourceList=pmSpDataSourceService.dataSourceList(sourceType,projectid);
        return PmResult.success(dataSourceList);
    }

    /**
     * 获取数据源表
     * @param jsonObject
     * @param projectid
     * @return
     */
    @PostMapping("/table-list")
    public PmResult getTables(@RequestBody JSONObject jsonObject,@RequestHeader("projectid") String projectid){
        String service=jsonObject.getString("service");
        List<TableDesc> tableDescList=pmSpDataSourceService.queryTables(service,projectid);
        return PmResult.success(tableDescList);
    }

    /**
     * 查询表数据
     * @param jsonObject
     * @param projectid
     * @return
     */
    @PostMapping("/table-data")
    public PmResult getTableData(@RequestBody JSONObject jsonObject,@RequestHeader("projectid") String projectid){
        String service=jsonObject.getString("service");
        String table=jsonObject.getString("table");
        List<Map<String,Object>> dataList=pmSpDataSourceService.queryTableData(service,table,projectid);
        return PmResult.success(dataList);
    }

}
