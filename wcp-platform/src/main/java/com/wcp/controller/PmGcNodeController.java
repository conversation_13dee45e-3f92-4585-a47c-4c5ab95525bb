package com.wcp.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.controller.BaseController;
import com.wcp.core.http.PmResult;
import com.wcp.core.text.PmStringUtils;
import com.wcp.core.text.PmUUID;
import com.wcp.domain.PmGcData;
import com.wcp.domain.PmGcNode;

import com.wcp.domain.PmGcPage;
import com.wcp.domain.dto.PmGcDataDto;
import com.wcp.service.PmGcDataService;
import com.wcp.service.PmGcNodeService;
import com.wcp.service.PmGcPageService;
import com.wcp.utils.TreeNode;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * Description: 图元树
 * Author: qianchao
 * Date: 2024/3/4 16:09
 */
@RestController
@RequestMapping("/pm-gcdata")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"图元树形管理"})
public class PmGcNodeController extends BaseController {
    @Autowired
    public PmGcNodeService pmGcNodeService;
    @Autowired
    private PmGcDataService pmGcDataService;

    @Autowired
    private PmGcPageService pmGcPageService;
    /**
     * 添加图元树形节点
     * @param pmGcNode
     * @return
     */
    @PostMapping("/save-node")
    @ApiOperation("添加图元树形节点")
    public PmResult saveNode(@RequestBody PmGcNode pmGcNode) {
        String projectId=getProjectId();
        if(!PmStringUtils.isNotEmpty(projectId)){
            return PmResult.error("hander请求中不存在项目ID");
        }
        pmGcNode.setProjectId(projectId);
        String pId= PmUUID.fastUUID().toString(true);
        pmGcNode.setPid(pId);
        boolean result=pmGcNodeService.saveNode(pmGcNode);
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("id",pId);
        return result?PmResult.success(jsonObject):PmResult.error();
    }

    @PostMapping("/update-node")
    @ApiOperation("修改图元树形节点")
    public PmResult updateNode(@RequestBody PmGcNode pmGcNode) {
        String projectId=getProjectId();
        if(!PmStringUtils.isNotEmpty(projectId)){
            return PmResult.error("hander请求中不存在项目ID");
        }
        pmGcNode.setProjectId(projectId);
        return PmResult.success(pmGcNodeService.updateNode(pmGcNode));
    }

    /**
     * 查询项目的图元树
     * @return
     */
    @GetMapping("/query-nodes")
    @ApiOperation("查询项目的图元树")
    public PmResult queryNodes(){
        List<TreeNode> treeNodeList=pmGcNodeService.queryNodes(getProjectId());
        return PmResult.success(JSON.toJSON(treeNodeList));
    }

    /**
     * 删除图元节点
     * @param jsonObject
     * @return
     */
    @PostMapping("/delete-node")
    @ApiOperation("图元树删除节点")
    @ApiImplicitParam(name = "jsonObject", value = "{\"pId\":1,\"nodeType\":\"folder\"}",paramType = "body")
    public PmResult deleteNode( @RequestBody JSONObject jsonObject,@RequestHeader("projectid") String projectid){
        String pId=jsonObject.getString("pId");
        String nodeType=jsonObject.getString("nodeType");
        if(pId!=null && PmStringUtils.isNotNull(nodeType)){
            int result=pmGcNodeService.deleteNode(pId,nodeType,projectid);
            return result>0?PmResult.success():PmResult.error();
        }else{
            return PmResult.error("请求参数不合法!");
        }
    }

    /**
     * 保存图元
     * @param pmGcData
     * @return
     */
    @PostMapping("/save-graph")
    @ApiOperation("保存图元数据")
    public PmResult saveGraph(@RequestBody PmGcData pmGcData,@RequestHeader("projectid") String projectid) {
        pmGcData.setProjectId(projectid);
       int result=pmGcDataService.saveOrUpdateGraph(pmGcData);
        return result>0?PmResult.success():PmResult.error();
    }

    /**
     * 通过图元点号获取图元内容
     * @param pmGcDataDto
     * @return
     */
    @PostMapping("/query-graph")
    @ApiOperation("图元ID获取图元")
    public PmResult getProjectGraph( @RequestBody PmGcDataDto pmGcDataDto, @RequestHeader("projectid") String projectid) throws IOException {
        String graphId=pmGcDataDto.getGraphId();
        String encoding=pmGcDataDto.getEncoding();
        String rootId=pmGcDataDto.getRootId();
        Object graphData=pmGcDataService.getProejctGraph(graphId,rootId,encoding,projectid);
        return PmResult.success(graphData);
    }

    @PostMapping("/publish-graph")
    @ApiOperation("图元发布")
    public PmResult publishGraphPage(@RequestBody PmGcPage pmGcPage, @RequestHeader("projectid") String projectid){
        pmGcPage.setProjectId(projectid);
        pmGcPage.setAccount(SecurityUtils.getLoginUser().getUser().getAccount());
        int result=pmGcPageService.publishGraphPage(pmGcPage);
        return result>0?PmResult.success():PmResult.error();
    }

    @PostMapping("/delete-route")
    @ApiOperation("删除图元路由")
    public PmResult deleteRoute(@RequestBody PmGcPage pmGcPage, @RequestHeader("projectid") String projectid){
        int result=pmGcPageService.deleteRoute(projectid,pmGcPage.getId());
        return result>0?PmResult.success():PmResult.error();
    }

    @PostMapping("/get-route")
    @ApiOperation("图元路由查询")
    public PmResult getRouteInfo(@RequestBody PmGcPage pmGcPage, @RequestHeader("projectid") String projectid){
        PmGcPage pmGcPageInfo=pmGcPageService.getRouteInfo(projectid,pmGcPage);
        return PmResult.success(pmGcPageInfo);
    }
}
