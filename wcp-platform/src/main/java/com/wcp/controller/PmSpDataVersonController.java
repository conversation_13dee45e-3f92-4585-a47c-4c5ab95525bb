package com.wcp.controller;

import com.wcp.core.http.PmResult;
import com.wcp.domain.PmSpDataVersion;
import com.wcp.service.PmSpDataVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 1. @Description 图元版本
 * 2. <AUTHOR>
 * 3. @Date 2025/3/4 10:07
 */
@RestController
@RequestMapping("/version")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"图元版本管理"})
public class PmSpDataVersonController {
    @Autowired
    private PmSpDataVersionService pmSpDataVersionService;
    /**
     * 新增图元历史版本
     */
    @ApiOperation("新增历史版本")
    @PostMapping("/add")
    public PmResult add(@RequestBody PmSpDataVersion version) {
        int result = pmSpDataVersionService.add(version);
        return result>0?PmResult.success():PmResult.error();
    }

    /**
     * 修改图元历史版本
     */
    @PutMapping("/update")
    public PmResult update(@RequestBody PmSpDataVersion version) {
        int result = pmSpDataVersionService.update(version);
        return result>0?PmResult.success():PmResult.error();
    }

    @DeleteMapping("/delete")
    public PmResult delete(@RequestParam String versionId) {
        int result = pmSpDataVersionService.delete(versionId);
        return result>0?PmResult.success():PmResult.error();

    }

    /**
     * 查询图元历史版本
     */
    @ApiOperation("查询历史版本")
    @GetMapping("/list")
    public PmResult list(@RequestParam  String graphId) {
        return PmResult.success(pmSpDataVersionService.list(graphId));
    }

    @ApiOperation("查询版本详情")
    @GetMapping("/query-history")
    public PmResult getHistoryData(@RequestParam String versionId,@RequestParam String encoding) throws IOException {
        Object result=pmSpDataVersionService.getHistGraph(versionId,encoding);
        return result!=null? PmResult.success(result):PmResult.error();
    }
}
