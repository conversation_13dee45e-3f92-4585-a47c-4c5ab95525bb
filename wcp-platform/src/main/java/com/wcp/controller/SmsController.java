package com.wcp.controller;

import com.aliyuncs.exceptions.ClientException;
import com.wcp.core.http.PmResult;
import com.wcp.sms.ShortMessageService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
/**
 * 1. @Description TODO 短信发送
 * 2. <AUTHOR>
 * 3. @Date 2025/7/15 10:14
 */

@RestController
@RequestMapping("/sms")
public class SmsController {
    @Resource
    private ShortMessageService shortMessageService;

    /**
     * 找回密码
     * @param phone
     * @return
     */
    @GetMapping("/send-pwd-code")
    public PmResult senSmsByRetrievePwd(String phone){
        try {
            Boolean flag=shortMessageService.senSmsByRetrievePwd(phone,"default");
            return flag?PmResult.success("发送成功"):PmResult.error("发送失败");
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 注册验证码
     * @param phone
     * @return
     */
    @GetMapping("/send-reg-code")
    public PmResult senSmsByRegister(String phone){
        try {
            Boolean flag=shortMessageService.senSmsByRegister(phone,"default");
            return flag?PmResult.success("发送成功"):PmResult.error("发送失败");
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }
}

