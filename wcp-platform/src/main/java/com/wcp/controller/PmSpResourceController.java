package com.wcp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmSpResource;
import com.wcp.minio.MinioService;
import com.wcp.service.PmSpResourceService;
import com.wcp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 1. @Description 项目资源管理
 * 2. <AUTHOR>
 * 3. @Date 2025/2/26 10:01
 */
@RestController
@RequestMapping("/resource")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"项目资源管理"})
public class PmSpResourceController {

    @Autowired
    private PmSpResourceService pmSpResourceService;

    @ApiOperation("添加资源")
    @PostMapping("/add")
    public PmResult addResource(@RequestBody PmSpResource resource,@RequestHeader("projectid") String projectid) {
        resource.setProjectId(projectid);
        boolean flag=pmSpResourceService.addResource(resource);
        return flag?PmResult.success("新增成功"):PmResult.error("新增失败");
    }

    @ApiOperation("资源树列表")
    @GetMapping("/folder-list")
    public PmResult folders(@RequestHeader("projectid") String projectid) {
        JSONObject jsonObject=pmSpResourceService.getAllFolders(projectid);
        return PmResult.success(jsonObject);
    }

    /**
     * 修改文件夹
     * @param pmSpResource
     * @return
     */
    @ApiOperation("修改文件夹")
    @PutMapping("/update-resource")
    public PmResult updateResource(@RequestBody PmSpResource pmSpResource){
        int result=pmSpResourceService.updateResource(pmSpResource);
        return result>0?PmResult.success("修改成功"):PmResult.error("修改失败");
    }


    @PostMapping("/delete-resource")
    @ApiOperation("删除文件夹")
    public PmResult deleteResource(@RequestHeader("projectid") String projectid,@RequestBody JSONObject jsonObject){
        JSONArray array=jsonObject.getJSONArray("ids");
        // 将 JsonArray 转换为 List<String>
        List<String> idList = new ArrayList<>();
        if(array!=null && array.size()>0){
            for (Object element : array) {
                idList.add((String) element); // 将每个元素转换为字符串
            }
        }

        boolean result=pmSpResourceService.deleteResource(projectid,idList);
        return result?PmResult.success("删除成功"):PmResult.error("删除失败");
    }

    @PostMapping("/upload-file")
    @ApiOperation("上传文件")
    public PmResult uploadFile(@RequestHeader("projectid") String projectid,@RequestParam("file") MultipartFile[] file,String folderId) {
        boolean flag=pmSpResourceService.upload(file,projectid,folderId);
        return flag?PmResult.success("上传成功"):PmResult.error("上传失败");
    }

    /**
     * 移动文件或者文件夹
     * @param projectid
     * @param projectid
     * @param jsonObject
     * @return
     */
    @PutMapping("/move-resource")
    @ApiOperation("移动文件")
    public PmResult moveResource(@RequestHeader("projectid") String projectid,JSONObject jsonObject) {
        JSONArray  ids=jsonObject.getJSONArray("ids");
        String parentId=jsonObject.getString("parentId");
        boolean flag=pmSpResourceService.moveResource(ids,parentId);
        return flag?PmResult.success("上传成功"):PmResult.error("上传失败");
    }

    @PostMapping("/copy-resource")
    @ApiOperation("复制资源")
    public PmResult copyResource(@RequestHeader("projectid") String projectid,String id) {
        pmSpResourceService.copyResource(projectid,id);
        return PmResult.success();
    }

    /**
     * 获取文件夹子集
     * @param projectId
     * @param jsonObject
     * @return
     */
    @PostMapping("/query-files")
    @ApiOperation("获取文件夹下的子集")
    public PmResult getChildrenFile(@RequestHeader("projectid") String projectId, @RequestBody JSONObject jsonObject){
        if(jsonObject!=null){
            String sortColumn=jsonObject.getString("sortColumn");//按照那一列排序
            String sortOrder=jsonObject.getString("sortOrder");//按照ASC 升序  DESC降序
            String folderId=jsonObject.getString("folderId");//按照ASC 升序  DESC降序
            List<PmSpResource>  pmSpResourceList=pmSpResourceService.getChildrenFile(projectId,folderId,sortColumn,sortOrder);
            return PmResult.success(pmSpResourceList);
        }else{
            return PmResult.success();
        }
    }

    /**
     * 通过objectName生成可访问的签名
     * @param objectName
     * @return
     */
    @GetMapping("generate-file-url")
    public PmResult generateFileUrl(@RequestHeader("projectid") String projectId,String objectName){
        try {
            String file="ebi/project/"+projectId+"/"+objectName;
            String url=MinioService.getPresignedObjectUrl(file);
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("url",url);
            return StringUtil.isNotEmpty(url)?PmResult.success(jsonObject):PmResult.error();
        } catch (Exception e) {
            throw new PmServiceException("生成签名链接失败");
        }
    }
}
