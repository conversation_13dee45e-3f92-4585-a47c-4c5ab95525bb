package com.wcp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmLoginUser;
import com.wcp.domain.PmWorkTeam;
import com.wcp.domain.dto.PlmUserDto;
import com.wcp.domain.dto.PmSpTeamRecycleProjectDto;
import com.wcp.domain.dto.PmWorkTeamDto;
import com.wcp.domain.dto.TeamUserDto;
import com.wcp.domain.vo.PmTeamUserVo;
import com.wcp.enums.PermissionEnum;
import com.wcp.service.PmWorkTeamService;
import com.wcp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description: 空间团队模块相关控制类
 * Author: shixin
 * Date: 2024/1/15 14:55
 */
@RestController
@RequestMapping("/team")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"工作团队接口"})
public class WorkTeamController {
    @Autowired
    private PmWorkTeamService teamService;
    /**
     * 获取团队的基本信息
     * @return  list
     */
    @GetMapping("/list")
    @ApiOperation("获取某个空间下的团队列表")
    @ApiImplicitParam(name = "spaceId",value = "空间ID", required = true, paramType = "form")
    public PmResult getWorkTeamById(String spaceId) {
        List<PmWorkTeamDto> pmWorkTeams = teamService.queryWorkTeamById();
        return PmResult.success(pmWorkTeams);
    }


    /**
     * 添加一个团队
     * @param pmWorkTeam 团队
     * @return json
     */
    @PostMapping("/add")
    @ApiOperation("添加团队")
    public PmResult createWorkTeam(@RequestParam(value = "file",required = false) MultipartFile file, @ModelAttribute PmWorkTeam pmWorkTeam) {
        Boolean add = teamService.createWorkTeam(file,pmWorkTeam);
        return add ? PmResult.success() : PmResult.error();
    }

   /* @PostMapping("/update")
    @ApiOperation("修改团队")
    public PmResult updateWorkTeam(@RequestParam(value = "file",required = false) MultipartFile file, @ModelAttribute PmWorkTeam pmWorkTeam) {
        Boolean add = teamService.updateWorkTeam(file,pmWorkTeam);
        return add ? PmResult.success() : PmResult.error();
    }*/

    @PostMapping("/update")
    @ApiOperation("修改团队")
    public PmResult updateWorkTeam(@RequestBody PmWorkTeam pmWorkTeam) {
        Boolean add = teamService.updateWorkTeam(pmWorkTeam);
        return add ? PmResult.success() : PmResult.error();
    }

    @GetMapping("/query")
    @ApiOperation("查询团队详细信息")
    public PmResult queryWorkTeam(String teamId) {
        PmWorkTeamDto pmWorkTeam = teamService.queryDetail(teamId);
        return PmResult.success(pmWorkTeam);
    }
    /**
     * 删除一个团队
     * @param teamId 团队ID
     * @return Boolean
     */
    @DeleteMapping("/remove")
    @ApiOperation("删除一个团队")
    public PmResult removeWorkSpace(String teamId) {
        Boolean delete = teamService.deleteWorkTeam(teamId);
        return delete ? PmResult.success() :PmResult.error();
    }


    /**
     * 添加项目成员
     * @param jsonObject
     * @return
     */
    @PostMapping("/add-member")
    @ApiOperation("添加团队成员")
    public PmResult addMember(@RequestBody JSONObject jsonObject) {
        List<PmTeamUserVo> projectUsers=new ArrayList<>();
        String permission=jsonObject.getString("permission");
        JSONArray userIds=jsonObject.getJSONArray("userIds");
        String spaceId=jsonObject.getString("spaceId");
        String teamId=jsonObject.getString("teamId");
        if(StringUtil.isEmpty(permission) || userIds==null || userIds.size()==0){
            PmResult.error("请求参数不能为空");
        }
        for(Object id:userIds){
            PmTeamUserVo pmTeamUserVo=new PmTeamUserVo();
            pmTeamUserVo.setUserId((String) id);
            pmTeamUserVo.setTeamId(teamId);
            pmTeamUserVo.setSpaceId(spaceId);
            pmTeamUserVo.setPermission(PermissionEnum.fromName(permission).getCode());
            projectUsers.add(pmTeamUserVo);
        }
        if (projectUsers.size()>0){
            Boolean result = teamService.addTeamUser(projectUsers);
            return result ? PmResult.success():PmResult.error();
        }else{
            return PmResult.error();
        }
    }

    /**
     * 搜索用户
     * @param param
     * @return
     */
    @PostMapping("searchuser/withrole")
    public PmResult searchuser(@RequestBody String param){
        if(StringUtil.isEmpty(param)){
            PmResult.error("请求参数不能为空");
        }
        JSONObject jsonObject=JSONObject.parseObject(param);
        List<PlmUserDto> plmUserList=teamService.searchuser(jsonObject.getString("teamId"),jsonObject.getString("content"));
        return PmResult.success(plmUserList);
    }

    /**
     * 获取团队成员
     * @param teamId 团队ID
     * @return boolean
     */
    @GetMapping("/members")
    @ApiOperation("获取团队成员")
    public PmResult getTeamUserById(String teamId) {
        List<TeamUserDto> projectUserDtoList = teamService.queryTeamUserById(teamId);
        return PmResult.success(projectUserDtoList);
    }

    /**
     * 权限修改
     * @param jsonObject
     * @return
     */
    @PutMapping("/update-permission")
    @ApiOperation("修改权限")
    public PmResult updatePermission(@RequestBody JSONObject jsonObject) {
        if(jsonObject==null){
            throw new PmServiceException("权限已修改");
        }
        TeamUserDto teamUserDto=JSONObject.parseObject(String.valueOf(jsonObject),TeamUserDto.class);
       // String teamId=jsonObject.getString("teamId");
        int result=teamService.updatePermission(teamUserDto);
        return result>0?PmResult.success("权限已修改"):PmResult.error("权限修改失败");
    }
    @GetMapping("/group-project")
    @ApiOperation("获取团队分组项目")
    public PmResult teamGroupProject(String teamId){
        List<Map<String, Object>> list=teamService.queryTeamProjectByGroup(teamId);
        return PmResult.success(list);
    }


    /**
     *查询团队下的回收站
     * @return
     */
    @GetMapping("recycle-bin")
    public PmResult recycleBin(String teamId){
        List<PmSpTeamRecycleProjectDto> list=teamService.queryTeamRecycle(teamId);
        return PmResult.success(list);
    }

    /**
     * 恢复回收站
     * @param projectId
     * @return
     */
    @PutMapping("recycle-bin/restore")
    public PmResult recycleBinRestore(String projectId){
        int result=teamService.recycleRestore(null,projectId);
        return result>0?PmResult.success():PmResult.error();
    }

    /**
     * 清空回收站
     * @param teamId
     * @return
     */
    @DeleteMapping("recycle-bin/clear")
    public PmResult recycleBinClear(String teamId){
        int reuslt=teamService.recycleClear(teamId,null);
        return reuslt>0?PmResult.success():PmResult.error();
    }

    @PostMapping("/uploadTeamProfile")
    @ApiOperation("上传团队图标")
    @ApiImplicitParam(name = "headImage", value = "上传团队头像", required = true, dataType = "MultipartFile", paramType = "form")
    public PmResult uploadTeamProfile(@RequestParam("profileImage") MultipartFile profileImage,@RequestParam("teamId") String teamId){
        // 获取当前登录用户信息
        PmLoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return PmResult.error("用户未登录");
        }
        // 检查文件是否为空
        if (profileImage.isEmpty()) {
            return PmResult.error("上传的图标文件不能为空");
        }

        PmResult result = teamService.uploadTeamProfile(profileImage,teamId);
        if (!result.isEmpty()) {
            return result;
        }
        return PmResult.success("图标更新成功");
    }

    @PostMapping("/teamTransfer")
    @ApiOperation("团队交接")
    public PmResult teamTransfer(@RequestBody JSONObject jsonObject) {
        Boolean delete = teamService.teamTransfer(jsonObject);
        return delete ? PmResult.success() :PmResult.error();
    }

    @PostMapping("/disband")
    @ApiOperation("解散团队")
    public PmResult  disbandTeam(String teamId){
        Boolean delete = teamService.deleteWorkTeam(teamId);
        return delete ? PmResult.success() :PmResult.error();
    }
}
