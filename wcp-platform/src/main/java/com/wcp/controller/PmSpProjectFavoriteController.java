package com.wcp.controller;

import com.wcp.core.http.PmResult;
import com.wcp.domain.PmSpProjectFavorite;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;
import com.wcp.service.PmSpProjectFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 1. @Description 收藏控制类
 * 2. <AUTHOR>
 * 3. @Date 2025/3/24 10:25
 */
@RestController
@RequestMapping("/favorite")
public class PmSpProjectFavoriteController {

    @Autowired
    private PmSpProjectFavoriteService pmSpProjectFavoriteService;

    /**
     * 查询收藏列表
     * @return
     */
    @GetMapping("/list")
    public PmResult queryFavoriteProject(String orderBy){
        List<PmSpTeamGroupProjectDto> list=pmSpProjectFavoriteService.queryFavoriteProject(orderBy);
        return PmResult.success(list);
    }


    /**
     * 添加收藏
     * @param pmSpProjectFavorite
     * @return
     */
    @PostMapping("/add")
    public PmResult add(@RequestBody PmSpProjectFavorite pmSpProjectFavorite){
        boolean result=pmSpProjectFavoriteService.add(pmSpProjectFavorite);
        return result?PmResult.success("收藏成功"):PmResult.error("收藏失败");
    }

    /**
     *
     * @param collectId
     * @return
     */
    @PostMapping("/remove")
    public PmResult remove(String collectId){
        PmSpProjectFavorite pmSpProjectFavorite=new PmSpProjectFavorite();
        pmSpProjectFavorite.setId(collectId);
        int result=pmSpProjectFavoriteService.delete(pmSpProjectFavorite);
        return result>0?PmResult.success("取消收藏成功"):PmResult.error("取消收藏失败");
    }
}
