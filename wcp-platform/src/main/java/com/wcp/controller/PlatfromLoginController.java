package com.wcp.controller;

import com.alibaba.fastjson.JSONObject;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.config.TokenService;
import com.wcp.core.controller.BaseController;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.http.PmResult;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PlmUser;
import com.wcp.domain.PmLoginUser;
import com.wcp.service.IUserInfoService;
import com.wcp.service.PmTeamUserService;
import com.wcp.utils.AESUtil;
import com.wcp.utils.StringUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 用户相关接口
 */
@RestController
@RequestMapping
@Api(tags = {"平台登录接口"})
public class PlatfromLoginController extends BaseController {
    @Autowired
    public IUserInfoService userInfoService;

    @Autowired
    public TokenService tokenService;

    /**
     * 平台用户使用账号密码登录
     * @param param
     * @return
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    @ApiImplicitParam(name = "param", value = "用户账号和密码", required = true,dataType = "JSONObject", paramType = "body")
    public PmResult login(@RequestBody(required = true) JSONObject param) {

        if(param==null){
            return PmResult.error("登录参数不能为空");
        }
        String identifier=param.getString("account");
        String pwd=param.getString("pwd");
        String password=AESUtil.decrypt(pwd);
        // 查找真实用户名
        PlmUser user = userInfoService.findUserByIdentifier(identifier);
        if (user == null) {
            return PmResult.error("用户不存在");
        }

        // 校验用户状态（0表示已注销）
        if (user.getUserStatus() == 0) {
            return PmResult.error("用户已注销，请联系管理员");
        }

        //PmResult ajax = PmResult.success();
        // 生成令牌
        Map<String,Object> token = userInfoService.login(user.getAccount(), password);
        //ajax.put(Constants.TOKEN, token);
        return PmResult.success(token);
    }

    /**
     * 用户注册
     * @param user
     * @return
     */
    @PostMapping("/register")
    @ApiOperation("用户注册")
    public PmResult register(@RequestBody @Validated PlmUser user) {
        if(user.getAccount().toUpperCase().equals("ADMIN")){
           return PmResult.error("admin账号不允许注册");
        }
        String msg = userInfoService.register(user);
        return !PmStringUtils.isEmpty(msg) ? PmResult.error(msg) : PmResult.success("注册成功");
    }

    @PostMapping("/profile")
    @ApiOperation("用户个人设置查询")
    public PmResult profile(@RequestBody(required = true) JSONObject param){
        String account = param.getString("account");
        // 根据account查库
        PlmUser plmUser = userInfoService.selectUserByUserName(account);
        return PmResult.success(plmUser);
    }

    /**
     * 修改密码
     * @param param 包含旧密码和新密码的参数
     * @return 操作结果
     */
    @PostMapping("/changepassword")
    @ApiOperation("修改密码")
    @ApiImplicitParam(name = "param", value = "旧密码和新密码", required = true, dataType = "JSONObject", paramType = "body")
    public PmResult changePassword(@RequestBody(required = true) JSONObject param) {
        // 验证参数完整性
        if (param == null ||
                StringUtil.isEmpty(param.getString("oldPassword")) ||
                StringUtil.isEmpty(param.getString("newPassword"))) {
            return PmResult.error("参数不能为空");
        }

        // 获取当前登录用户信息
        PmLoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return PmResult.error("用户未登录");
        }

        String oldPassword = AESUtil.decrypt(param.getString("oldPassword"));
        String newPassword = AESUtil.decrypt(param.getString("newPassword"));

        try {
            // 验证旧密码是否正确
            userInfoService.validatePassword(loginUser.getUser().getAccount(), oldPassword);

            // 修改密码
            userInfoService.changePassword(loginUser.getUserId(), newPassword);

            return PmResult.success("密码修改成功");
        } catch (PmServiceException e) {
            return PmResult.error(e.getMessage());
        } catch (Exception e) {
            return PmResult.error("密码修改失败，请稍后重试");
        }
    }

    /**
     * 注销用户账户
     * @param param 包含用户密码的参数
     * @return 操作结果
     */
    @PostMapping("/cancel")
    @ApiOperation("注销用户账户")
    @ApiImplicitParam(name = "param", value = "用户密码", required = true, dataType = "JSONObject", paramType = "body")
    public PmResult cancelAccount(@RequestBody(required = true) JSONObject param) {
        // 验证参数完整性
        if (param == null || StringUtil.isEmpty(param.getString("password"))) {
            return PmResult.error("参数不能为空");
        }

        // 获取当前登录用户信息
        PmLoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return PmResult.error("用户未登录");
        }

        // 检查是否为管理员账户
        if (isAdminUser(loginUser)) {
            return PmResult.error("管理员账户不允许注销");
        }

        String password = AESUtil.decrypt(param.getString("password"));

        try {
            // 验证密码是否正确
            userInfoService.validatePassword(loginUser.getUser().getAccount(), password);

            // 执行账户注销
            userInfoService.cancelAccount(loginUser.getUserId());

            return PmResult.success("账户注销成功");
        } catch (PmServiceException e) {
            return PmResult.error(e.getMessage());
        } catch (Exception e) {
            return PmResult.error("账户注销失败，请稍后重试");
        }
    }

    /**
     * 修改用户信息
     * @param plmUser 修改用户信息参数
     * @return 操作结果
     */
    @PostMapping("/update")
    @ApiOperation("修改用户信息")
    @ApiImplicitParam(name = "plmUser", value = "用户信息", required = true, dataType = "PlmUser", paramType = "body")
    public PmResult updateUserInfo(@RequestBody @Validated PlmUser plmUser) {
        // 获取当前登录用户信息
        PmLoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return PmResult.error("用户未登录");
        }

        // 设置当前用户ID，防止越权修改
        plmUser.setUserId(loginUser.getUserId());

        // 执行更新
        PmResult result = userInfoService.updateUserInfo(plmUser, loginUser.getUser());
        if (!result.isEmpty()) {
            return result;
        }
        return PmResult.success("用户信息更新成功");
    }

    @PostMapping("/updatepic")
    @ApiOperation("修改用户头像")
    @ApiImplicitParam(name = "headImage", value = "头像图片文件", required = true, dataType = "MultipartFile", paramType = "form")
    public PmResult updateAvatar(@RequestParam("headImage") MultipartFile headImage){
        // 获取当前登录用户信息
        PmLoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return PmResult.error("用户未登录");
        }

        // 检查文件是否为空
        if (headImage.isEmpty()) {
            return PmResult.error("上传的头像文件不能为空");
        }

        // 检查文件类型
        String contentType = headImage.getContentType();
        if (contentType == null || !(
                        "image/jpeg".equals(contentType) ||
                        "image/jpg".equals(contentType) ||
                        "image/png".equals(contentType) ||
                        "image/gif".equals(contentType)
        )) {
            return PmResult.error("不支持的图片类型，仅支持JPG、PNG、GIF格式");
        }

        PmResult result = userInfoService.updateAvatar(headImage,loginUser);
        if (!result.isEmpty()) {
            return result;
        }
        return PmResult.success("头像更新成功");
    }

    private boolean isAdminUser(PmLoginUser loginUser) {

        return loginUser.getUser().getAccount() != null &&
                "admin1".equals(loginUser.getUser().getAccount());
    }

/*    *//**
     * 系统退出
     * @param httpServletRequest
     *//*
    @PostMapping("/logout")
    public void logout(HttpServletRequest httpServletRequest) {
        PmLoginUser pmLoginUser=tokenService.getLoginUser(httpServletRequest);
        System.out.println(pmLoginUser);
    }*/

}
