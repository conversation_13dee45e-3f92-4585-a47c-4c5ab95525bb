package com.wcp.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.wcp.core.controller.BaseController;
import com.wcp.core.http.PmResult;
import com.wcp.core.page.TableDataInfo;
import com.wcp.domain.PmSpProjectPrefix;
import com.wcp.service.PmSpProjectPrefixService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 1. @Description 项目前缀管理
 * 2. <AUTHOR>
 * 3. @Date 2025/6/13 9:05
 */
@RestController
@RequestMapping("/prefix")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"项目接口管理"})
public class PmSpProjectPrefixController extends BaseController {

    @Autowired
    private PmSpProjectPrefixService pmSpProjectPrefixService;

    /**
     * 项目前缀信息批量保存/修改
     * @param prefixList
     * @return
     */
    @PostMapping("/upsert")
    @ApiOperation("项目前缀保存/修改")
    public PmResult prefixSaveOrUpdates(@RequestBody List<PmSpProjectPrefix> prefixList, @RequestHeader("projectid") String projectid){
        // 将 projectid 设置到每个对象中
        if (prefixList != null && !prefixList.isEmpty()) {
            prefixList.forEach(prefix -> prefix.setProjectId(projectid));
        }
        int result=pmSpProjectPrefixService.prefixSaveOrUpdates(prefixList);
        return result>0?PmResult.success():PmResult.error();
    }

    /**
     * 获取系统配置的项目前缀信息
     * @param projectid
     * @return
     */
    @PostMapping("/list")
    @ApiOperation("项目前缀列表信息")
    public PmResult ajaxList(@RequestBody JSONObject jsonObject,@RequestHeader("projectid") String projectid){
        int pageNum=jsonObject.getInteger("pageNum");
        int pageSize=jsonObject.getInteger("pageSize");
        PageHelper.startPage(pageNum, pageSize);

        List<JSONObject> ajaxList=pmSpProjectPrefixService.prefixList(projectid);
        TableDataInfo tableDataInfo=getDataTable(ajaxList);
        JSONObject json=new JSONObject();
        json.put("rows",tableDataInfo.getRows());
        json.put("total",tableDataInfo.getTotal());
        return PmResult.success(json);
    }


    /**
     * 项目前缀删除
     * @param pmSpProjectPrefix
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation("项目前缀删除")
    public PmResult ajaxDelete(@RequestBody PmSpProjectPrefix pmSpProjectPrefix){
        int result=pmSpProjectPrefixService.prefixDelete(pmSpProjectPrefix);
        return result>0?PmResult.success():PmResult.error();
    }
}
