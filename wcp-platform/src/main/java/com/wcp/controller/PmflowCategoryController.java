package com.wcp.controller;

import com.alibaba.fastjson.JSONObject;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmflowCategory;
import com.wcp.service.PmflowCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/5/19 13:46
 */
@RestController
@RequestMapping("flw-category")
public class PmflowCategoryController {
    @Autowired
    private PmflowCategoryService pmflowCategoryService;
    @PostMapping("/add")
    public PmResult add(@RequestBody PmflowCategory category,@RequestHeader("projectid") String projectid) {
        category.setProjectId(projectid);
        int result=pmflowCategoryService.add(category);
        return result>0? PmResult.success("新增成功"):PmResult.error("新增失败");
    }

    @PostMapping("/update")
    public PmResult update(@RequestBody PmflowCategory category,@RequestHeader("projectid") String projectid) {
        category.setProjectId(projectid);
        int result=pmflowCategoryService.update(category);
        return result>0? PmResult.success("修改成功"):PmResult.error("修改失败");
    }

    @PostMapping("/delete")
    public PmResult delete(@RequestBody JSONObject jsonObject, @RequestHeader("projectid") String projectid) {
        String categoryId=jsonObject.getString("categoryId");
        int result=pmflowCategoryService.delete(categoryId,projectid);
        return result>0? PmResult.success("删除成功"):PmResult.error("删除失败");
    }

    @GetMapping("/list")
    public PmResult  list(@RequestHeader("projectid") String projectid) {
        return PmResult.success(pmflowCategoryService.list(projectid));
    }
}
