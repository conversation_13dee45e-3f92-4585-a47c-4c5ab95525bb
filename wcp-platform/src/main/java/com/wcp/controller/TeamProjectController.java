package com.wcp.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmSpOpenProject;
import com.wcp.domain.PmTeamProject;
import com.wcp.domain.dto.PlmUserDto;
import com.wcp.domain.dto.PmSpTeamGroupProjectDto;
import com.wcp.domain.dto.PmTeamProjectSerachDto;
import com.wcp.domain.dto.ProjectUserDto;
import com.wcp.domain.vo.PmProjectUserVo;
import com.wcp.enums.PermissionEnum;
import com.wcp.service.PmSpOpenProjectService;
import com.wcp.service.PmTeamProjectService;
import com.wcp.service.impl.PmTeamUserServiceImpl;
import com.wcp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 团队项目模块相关控制类
 * Author: shixin
 * Date: 2024/1/15 14:55
 */
@RestController
@RequestMapping("/project")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"团队项目接口"})
public class TeamProjectController {

    @Autowired
    private PmTeamProjectService teamProjectService;
    @Autowired
    private PmTeamUserServiceImpl teamUserService;

    @Autowired
    private PmSpOpenProjectService pmSpOpenProjectService;
    /**
     * 获取团队项目列表
     * @param teamId 团队id
     * @return list
     */
    @GetMapping("/list")
    @ApiOperation("查询团队下的项目")
    public PmResult getTeamProjectList(String teamId,String groupId) {
        List<PmTeamProject> teamProjects = teamProjectService.queryTeamProject(teamId,groupId);
        return PmResult.success(teamProjects);
    }

    /**
     * 添加一个团队项目
     * @param project 项目信息
     * @return boolean
     */
    @PostMapping("/add")
    @ApiOperation("添加项目")
    public PmResult createProjectForTeam(@RequestParam(value = "file",required = false) MultipartFile file, @ModelAttribute PmTeamProject project) {
        Boolean teamProject = teamProjectService.createTeamProject(file,project);
        return teamProject ? PmResult.success():PmResult.error();
    }

    /**
     * 修改项目
     * @param file
     * @param project
     * @return
     */
    @PostMapping("/update")
    @ApiOperation("修改项目")
    public PmResult updateProjectForTeam(@RequestParam(value = "file",required = false) MultipartFile file, @ModelAttribute PmTeamProject project) {
        Boolean teamProject = teamProjectService.updateTeamProject(file,project);
        return teamProject ? PmResult.success():PmResult.error();
    }

    /**
     * 删除一个项目
     * @param project 项目信息
     * @return boolean
     */
    @PostMapping("/remove")
    @ApiOperation("删除团队下的一个项目")
    public PmResult deleteProjectForTeam(@RequestBody PmTeamProject project) {
        Boolean teamProject = teamProjectService.deleteTeamProject(project);
        return teamProject ? PmResult.success():PmResult.error();
    }

    /**
     * 获取项目成员
     * @param projectId 项目id
     * @return boolean
     */
    @GetMapping("/members")
    @ApiOperation("获取项目成员")
    public PmResult getTeamUserById(String projectId) {
        List<ProjectUserDto> projectUserDtoList = teamUserService.queryTeamUserById(projectId);
        return PmResult.success(projectUserDtoList);
    }

    /**
     * 添加项目成员
     * @param jsonObject
     * @return
     */
    @PostMapping("/add-member")
    @ApiOperation("添加项目成员")
    public PmResult addMember(@RequestBody JSONObject jsonObject) {
        List<PmProjectUserVo> projectUsers=new ArrayList<>();
        String permission=jsonObject.getString("permission");
        String projectId=jsonObject.getString("projectId");
        JSONArray userIds=jsonObject.getJSONArray("userIds");
        String spaceId=jsonObject.getString("spaceId");
        String teamId=jsonObject.getString("teamId");
        if(StringUtil.isEmpty(permission) || StringUtil.isEmpty(projectId) || userIds==null || userIds.size()==0){
            PmResult.error("请求参数不能为空");
        }
        for(Object id:userIds){
            PmProjectUserVo projectUser=new PmProjectUserVo();
            projectUser.setUserId((String) id);
            projectUser.setProjectId(projectId);
            projectUser.setTeamId(teamId);
            projectUser.setSpaceId(spaceId);
            projectUser.setPermission(PermissionEnum.fromName(permission).getCode());
            projectUsers.add(projectUser);
        }
        if (projectUsers.size()>0){
            Boolean result = teamProjectService.addProjectUser(projectUsers);
            return result ? PmResult.success():PmResult.error();
        }else{
            return PmResult.error();
        }
    }

    /**
     * 搜索用户
     * @param param
     * @return
     */
    @PostMapping("searchuser/withrole")
    public PmResult searchuser(@RequestBody String param){
        if(StringUtil.isEmpty(param)){
            PmResult.error("请求参数不能为空");
        }
        JSONObject jsonObject=JSONObject.parseObject(param);
        List<PlmUserDto> plmUserList=teamProjectService.searchuser(jsonObject.getString("projectId"),jsonObject.getString("content"));
        return PmResult.success(plmUserList);
    }

    /**
     * 记录最近打开的项目
     * @param projectId
     * @return
     */
    @GetMapping("recent-access")
    public PmResult recentAccess( String projectId){
        if(StringUtil.isEmpty(projectId)){
            PmResult.error("项目ID不能为空");
        }
        PmSpOpenProject pmSpOpenProject=new PmSpOpenProject();
        pmSpOpenProject.setProjectId(projectId);
        int result=pmSpOpenProjectService.recordProjectOpen(pmSpOpenProject);
        return result>0?PmResult.success():PmResult.error();
    }

    /**
     * 项目主页
     * @return
     */
    @GetMapping("home-index")
    public PmResult homeIndex(){
        List<PmSpTeamGroupProjectDto> list=pmSpOpenProjectService.getRecentProjectOpens();
        return PmResult.success(list);
    }

    /**
     * 首页搜索
     * @param param
     * @return
     */
    @PostMapping("search")
    public PmResult search(@RequestBody String param){
        if(StringUtil.isEmpty(param)){
            PmResult.error("搜索内容不能为空");
        }
        JSONObject jsonObject=JSONObject.parseObject(param);
        String content=jsonObject.getString("content");
        JSONArray resultArray=teamProjectService.searchTeamList(content);
        return PmResult.success(resultArray);
    }
}
