package com.wcp.controller;


import com.wcp.core.config.SecurityUtils;
import com.wcp.core.http.PmResult;
import com.wcp.domain.PmLoginUser;
import com.wcp.domain.PmTeamUser;
import com.wcp.service.impl.PmTeamUserServiceImpl;
import com.wcp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 用户团队模块相关控制类
 * Author: 李文琪
 * Date: 2025/6/17 14:55
 */
@RestController
@RequestMapping("/team/user")
@PreAuthorize("@wcp.hasAnyRoles('PERSON,USERS')")
@Api(tags = {"用户团队接口"})
public class PmSpTeamUserController {

    @Autowired
    private PmTeamUserServiceImpl teamUserService;

    @ApiOperation("退出团队")
    @PostMapping("/exit")
    public PmResult addGroup(@RequestBody PmTeamUser pmTeamUser){
        // 获取当前登录用户信息
        PmLoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return PmResult.error("用户未登录");
        }
        String teamId = pmTeamUser.getTeamId();
        // 验证参数完整性
        if (teamId == null || StringUtil.isEmpty(teamId)) {
            return PmResult.error("参数不能为空");
        }
        pmTeamUser.setUserId(loginUser.getUserId());

        boolean result=teamUserService.exit(pmTeamUser);
        return result?PmResult.success():PmResult.error();
    }
}
