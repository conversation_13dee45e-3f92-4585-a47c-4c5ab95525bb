package com.wcp.flow.controller;

import com.alibaba.fastjson.JSONObject;
import com.wcp.core.execption.PmServiceException;
import com.wcp.core.http.PmResult;
import com.wcp.core.page.PageDomain;
import com.wcp.core.page.TableDataInfo;
import com.wcp.core.page.TableSupport;
import com.wcp.http.HttpStatus;
import org.dromara.warm.flow.core.FlowEngine;
import org.dromara.warm.flow.core.chart.BetweenChart;
import org.dromara.warm.flow.core.chart.FlowChart;
import org.dromara.warm.flow.core.dto.ApiResult;
import org.dromara.warm.flow.core.dto.DefJson;
import org.dromara.warm.flow.core.entity.Definition;
import org.dromara.warm.flow.core.exception.FlowException;
import org.dromara.warm.flow.core.invoker.FrameInvoker;
import org.dromara.warm.flow.core.service.ChartService;
import org.dromara.warm.flow.core.service.DefService;
import org.dromara.warm.flow.core.utils.ExceptionUtil;
import org.dromara.warm.flow.core.utils.MapUtil;
import org.dromara.warm.flow.core.utils.page.Page;
import org.dromara.warm.flow.orm.entity.FlowDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.awt.*;
import java.util.*;
import java.util.List;

/**
 * 1. @Description 流程定义
 * 2. <AUTHOR>
 * 3. @Date 2025/5/19 14:38
 */
@RestController
@RequestMapping("flw-definition")
public class PmDefinitionController {
    @Autowired
    private DefService defService;
    @Resource
    private ChartService chartService;
    /**
     * 新增流程设计
     * @param flowDefinition
     * @return
     */
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public PmResult add(@RequestBody FlowDefinition flowDefinition,@RequestHeader("projectid") String projectid) {
        flowDefinition.setTenantId(projectid);
        boolean result=defService.saveAndInitNode(flowDefinition);
        JSONObject jsonObject= new JSONObject();
        jsonObject.put("id",flowDefinition.getId());
        return result? PmResult.success("新增成功",jsonObject):PmResult.error("新增失败");
    }

    /**
     * 修改流程设计
     * @param flowDefinition
     * @return
     */
    @PostMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    public PmResult edit(@RequestBody FlowDefinition flowDefinition,@RequestHeader("projectid") String projectid) {
        flowDefinition.setTenantId(projectid);
        boolean result=(defService.updateById(flowDefinition));
        return result? PmResult.success("修改成功"):PmResult.error("修改失败");
    }

    /**
     * 删除流程设计
     * @param ids
     * @return
     */
    @GetMapping("/delete/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public PmResult remove(@PathVariable List<String> ids) {
        boolean result=defService.removeDef(ids);
        return result? PmResult.success("删除成功"):PmResult.error("删除失败");
    }

    /**
     * 流程设计界面查询流程设计图
     * @param id
     * @return
     */
    @GetMapping("/query-def/{id}")
    public PmResult queryDef(@PathVariable("id") String id) {
        try {

            DefJson defJson=FlowEngine.defService().queryDesign(id);
            return PmResult.success(defJson);
        } catch (Exception e) {
            throw new PmServiceException(ExceptionUtil.handleMsg("获取流程json字符串失败", e));
        }
    }

    /**
     * 查询流程设计
     * @param flowDefinition
     * @return
     */
    @GetMapping("/list")
    public PmResult list(FlowDefinition flowDefinition,@RequestHeader("projectid") String projectid) {
        // flow组件自带分页功能
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Page<Definition> page = Page.pageOf(pageDomain.getPageNum(), pageDomain.getPageSize());
        flowDefinition.setTenantId(projectid);
        page = defService.orderByCreateTime().desc().page(flowDefinition, page);
        Map<String, Object> maps=new HashMap<>();
        maps.put("data",page.getList());
        maps.put("total",page.getTotal());
        return PmResult.success(maps);
    }

    /**
     * 发布流程
     * @param id
     * @return
     */
    @GetMapping("/publish/{id}")
    @Transactional(rollbackFor = Exception.class)
    public PmResult publish(@PathVariable("id") String id) {
        boolean result=defService.publish(id);
        return result? PmResult.success("发布成功"):PmResult.error("发布失败");
    }

    /**
     * 取消发布
     * @param id
     * @return
     */
    @GetMapping("/unPublish/{id}")
    @Transactional(rollbackFor = Exception.class)
    public PmResult unPublish(@PathVariable("id") String id) {
        boolean result=defService.unPublish(id);
        return result? PmResult.success("取消成功"):PmResult.error("取消失败");
    }

    /**
     * 保存流程定义
     * @param defJson
     * @return
     * @throws Exception
     */
    @PostMapping("/save-json")
    @Transactional(rollbackFor = Exception.class)
    public PmResult saveJson(@RequestBody DefJson defJson,@RequestHeader("projectid") String projectid) throws Exception {
        defJson.setTenantId(projectid);
        FlowEngine.defService().saveDef(defJson);
        return PmResult.success();
    }

/*    @GetMapping("/flowChart/{instanceId}")
    public PmResult flowChart(@PathVariable("instanceId") String instanceId) {
        return PmResult.success(chartService.chartIns(instanceId, (flowChartChain) -> {
            List<FlowChart> flowChartList = flowChartChain.getFlowChartList();
            flowChartList.forEach(flowChart -> {
                if (flowChart instanceof BetweenChart) {
                    BetweenChart betweenChart = (BetweenChart) flowChart;
                    Map<String, Object> extMap = betweenChart.getNodeJson().getExtMap();
                    // 给节点顶部增加文字说明
//                    betweenChart.topText("办理时间: 2025-02-08 12:12:12", Color.red);
                    if (MapUtil.isNotEmpty(extMap)) {
                        for(Map.Entry<String, Object> entry : extMap.entrySet()){
                            // 给节点中追加文字
                            betweenChart.addText(entry.getKey() + ":", Color.red);
                            betweenChart.addText((String) entry.getValue(), Color.red);
                        }
                    }
                }
            });
        }));
    }*/


}
