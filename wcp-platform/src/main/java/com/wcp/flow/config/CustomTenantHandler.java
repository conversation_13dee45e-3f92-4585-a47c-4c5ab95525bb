package com.wcp.flow.config;

import org.dromara.warm.flow.core.handler.TenantHandler;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 1. @Description 工作流租戶
 * 2. <AUTHOR>
 * 3. @Date 2025/4/24 10:03
 */

public class CustomTenantHandler implements TenantHandler {
    /**
     * 工作流租戶id 使用項目id
     * @return
     */
    @Override
    public String getTenantId() {
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }
        HttpServletRequest request = attributes.getRequest();
        String projectId=request.getHeader("projectid");
        return projectId;
    }
}
