package com.wcp.flow.config;

import org.dromara.warm.flow.core.handler.TenantHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/5/21 13:44
 */
@Configuration
public class WarmFlowConfig {
    /**
     * 全局租户处理器（可通过配置文件注入，也可用@Bean/@Component方式
     */
    @Bean
    public TenantHandler tenantHandler() {
        return new CustomTenantHandler();
    }
}
