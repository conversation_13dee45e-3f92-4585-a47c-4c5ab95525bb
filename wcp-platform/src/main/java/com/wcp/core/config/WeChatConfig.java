package com.wcp.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Description: TODO
 * Author: qianchao
 * Date: 2024/2/21 11:01
 */
@Configuration
@Data
public class WeChatConfig {

    /**
     * 微信开发平台appId
     */
    @Value("${wxopen.appid}")
    private String openAppid;

    /**
     * 微信开发平台密钥
     */
    @Value("${wxopen.appsecret}")
    private String openAppsecret;

    /**
     * 微信开放平台二维码连接
     */
    private final static String OPEN_QRCODE_URL = "https://open.weixin.qq.com/connect/qrconnect?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_login&state=%s#wechat_redirect";


    /**
     * 开放平台获取access_token地址
     */
    private final static String OPEN_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";

    /**
     * 获取用户信息
     */
    private final static String OPEN_USER_INFO_URL ="https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN";
    public static String getOpenQrcodeUrl() {
        return OPEN_QRCODE_URL;
    }
    public static String getOpenAccessTokenUrl() {
        return OPEN_ACCESS_TOKEN_URL;
    }
    public static String getOpenUserInfoUrl() {
        return OPEN_USER_INFO_URL;
    }

}
