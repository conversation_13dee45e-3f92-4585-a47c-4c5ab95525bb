package com.wcp.core.config;

import com.wcp.minio.MinioConfig;
import com.wcp.minio.MinioService;
import io.minio.MinioClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;

/**
 * Description: 平台服务初始化注解加载配置
 * Author: qianchao
 * Date: 2024/1/5 10:11
 */
@Component
public class PlatformInitBean {

    private static Logger logger= LoggerFactory.getLogger(PlatformInitBean.class);
    @Autowired
    private MinioClient minioClient;
    @Autowired
    private MinioConfig minioConfig;
    @PostConstruct
  public void initProject() {
      logger.info("###################Start loading platform......#############");
        //初始化minio配置
        MinioService.init(minioClient,minioConfig);
  }
}
