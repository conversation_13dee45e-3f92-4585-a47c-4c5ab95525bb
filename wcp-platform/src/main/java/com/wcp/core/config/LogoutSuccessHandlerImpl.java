package com.wcp.core.config;

import com.alibaba.fastjson.JSON;
import com.wcp.core.http.PmHttpStatus;
import com.wcp.core.http.PmResult;
import com.wcp.core.page.ServletUtils;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PmLoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    @Autowired
    private TokenService tokenService;

    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        PmLoginUser loginUser = tokenService.getLoginUser(request);
        if (PmStringUtils.isNotNull(loginUser)) {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(request);
            // 记录用户退出日志
           // AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功"));
        }
        ServletUtils.renderString(response, JSON.toJSONString(PmResult.error(PmHttpStatus.SUCCESS, "退出成功")));
    }
}
