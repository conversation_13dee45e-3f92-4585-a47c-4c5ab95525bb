package com.wcp.core.config;

import com.alibaba.fastjson.JSON;
import com.wcp.core.http.PmResult;
import com.wcp.core.http.PmHttpStatus;
import com.wcp.core.page.ServletUtils;
import com.wcp.core.text.PmStringUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;

/**
 * 认证失败处理类 返回未授权
 *
 * <AUTHOR>
 */
@Component
public class AuthenticationEntryPoint implements org.springframework.security.web.AuthenticationEntryPoint, Serializable {
    private static final long serialVersionUID = -8970718410437077606L;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e)
            throws IOException {
        int code = PmHttpStatus.UNAUTHORIZED;
        String msg = PmStringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
        ServletUtils.renderString(response, JSON.toJSONString(PmResult.error(code, msg)));
    }
}
