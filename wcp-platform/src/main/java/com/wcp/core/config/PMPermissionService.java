package com.wcp.core.config;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PlmRole;
import com.wcp.domain.PlmUser;
import com.wcp.service.IPlatfromRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Component
public class PMPermissionService {

    @Autowired
    private IPlatfromRoleService platfromRoleService;

    /**
     * 获取角色数据权限
     *
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(PlmUser user) {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            roles.add("admin");
        } else {
            // 执行查询
            Set<PlmRole> perms = platfromRoleService.selectRolesByUserId(user.getUserId());
            for (PlmRole perm : perms) {
                if (PmStringUtils.isNotNull(perm)) {
                    roles.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
                }
            }
        }
        return roles;
    }

    /**
     * 查询用户角色
     * @param user
     * @return
     */
    public Set<PlmRole> getRoles(PlmUser user) {
        Set<PlmRole> roles = new HashSet<PlmRole>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            PlmRole role=new PlmRole();
            role.setRoleName("超级管理员");
            role.setRoleKey("ADMIN");
            role.setRoleId(0);
            roles.add(role);
        }else{
            roles.addAll(platfromRoleService.selectRolesByUserId(user.getUserId()));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(PlmUser user) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            perms.add("*:*:*");
        } else {
            //perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
        }
        return perms;
    }
}
