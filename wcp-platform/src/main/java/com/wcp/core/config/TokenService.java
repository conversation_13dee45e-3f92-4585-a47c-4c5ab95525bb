package com.wcp.core.config;

import com.wcp.core.constant.Constants;
import com.wcp.core.redis.PlmRedisCache;
import com.wcp.core.text.DateUtils;
import com.wcp.core.text.PmIdUtils;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PmLoginUser;
import com.wcp.utils.StringUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {
    // 令牌自定义标识
    @Value("${spring.sso.header}")
    private String header;

    // 令牌秘钥
    @Value("${spring.sso.secretKey}")
    private String secret;
    @Autowired
    private PlmRedisCache redisCache;

    // 令牌有效期（默认30分钟）
    @Value("${spring.sso.token-expire-time-web}")
    private int expireTime;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;
    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public PmLoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (PmStringUtils.isNotEmpty(token)) {
            try {
                Claims claims = parseToken(token);
                // 解析对应的权限以及用户信息
                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
                String userKey = getTokenKey(uuid);
                PmLoginUser user=redisCache.getCacheObject(userKey);
                return user;
            } catch (Exception e) {
            }
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(PmLoginUser loginUser) {
        if (PmStringUtils.isNotNull(loginUser) && PmStringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (PmStringUtils.isNotEmpty(token)) {
            Claims claims = parseToken(token);
            // 解析对应的权限以及用户信息
            String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
            String userKey = getTokenKey(uuid);
            redisCache.deleteObject(userKey);
        }
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(PmLoginUser loginUser) {
        String token = PmIdUtils.fastUUID();
        loginUser.setToken(token);
        refreshToken(loginUser);

        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.LOGIN_USER_KEY, token);
        claims.put("ID", loginUser.getUserId());
        claims.put("ACCOUNT", loginUser.getUser().getAccount());
        claims.put("USERNAME", loginUser.getUser().getUserName());
        // 解析对应的权限以及用户信息
        String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
        String userKey = getTokenKey(uuid);
        redisCache.setCacheObject(userKey,loginUser);
        return createToken(claims);
    }



    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param loginUser
     * @return 令牌
     */
    public void verifyToken(PmLoginUser loginUser) {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(PmLoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getUserId());
        System.out.println(userKey);
        redisCache.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);
       // cacheMemory.setValue(Constants.PROJECT_ID,Constants.SERVICE,userKey,loginUser);

      //  cacheMemory.setExpireTime(Constants.PROJECT_ID,Constants.SERVICE,userKey,loginUser.getExpireTime().intValue());
    }



    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    public String createToken(Map<String, Object> claims) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        Date expirationDate = new Date(System.currentTimeMillis() + (expireTime * MILLIS_MINUTE));
        System.out.println("token过期时间"+DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",expirationDate));
        return  Jwts.builder()
                .claims(claims)
                .setExpiration(expirationDate)
                .signWith(key,Jwts.SIG.HS256)
                .compact();
       /* String token = Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret).compact();
        return token;*/
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        /*return Jwts.parser()
                .setSigningKey(secret).build()
                .parseClaimsJws(token)
                .getBody();*/
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        return Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (PmStringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getTokenKey(String uuid) {
        return Constants.LOGIN_TOKEN_KEY + uuid;
    }
}
