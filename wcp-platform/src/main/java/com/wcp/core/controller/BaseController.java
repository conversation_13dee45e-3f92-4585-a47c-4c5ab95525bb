package com.wcp.core.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wcp.core.config.SecurityUtils;
import com.wcp.core.http.PmResult;
import com.wcp.core.http.PmHttpStatus;
import com.wcp.core.page.PageDomain;
import com.wcp.core.page.PageUtils;
import com.wcp.core.page.TableDataInfo;
import com.wcp.core.page.TableSupport;
import com.wcp.core.text.DateUtils;
import com.wcp.core.text.PmSqlUtil;
import com.wcp.core.text.PmStringUtils;
import com.wcp.domain.PmLoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
@RestController
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private HttpServletRequest request;
    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageUtils.startPage();
    }

    /**
     * 设置请求排序数据
     */
    protected void startOrderBy() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (PmStringUtils.isNotEmpty(pageDomain.getOrderBy())) {
            String orderBy = PmSqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.orderBy(orderBy);
        }
    }

    /**
     * 清理分页的线程变量
     */
    protected void clearPage() {
        PageUtils.clearPage();
    }

    /**
     * 响应请求分页数据
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(PmHttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 返回成功
     */
    public PmResult success() {
        return PmResult.success();
    }

    /**
     * 返回失败消息
     */
    public PmResult error() {
        return PmResult.error();
    }

    /**
     * 返回成功消息
     */
    public PmResult success(String message) {
        return PmResult.success(message);
    }

    /**
     * 返回失败消息
     */
    public PmResult error(String message) {
        return PmResult.error(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected PmResult toAjax(int rows) {
        return rows > 0 ? PmResult.success() : PmResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected PmResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return PmStringUtils.format("redirect:{}", url);
    }
    /**
     * 获取用户缓存信息
     */
    public PmLoginUser getLoginUser() {
        PmLoginUser p=SecurityUtils.getLoginUser();
        return SecurityUtils.getLoginUser();
    }

    /**
     * 获取登录用户id
     */
    public String getUserId() {
        return getLoginUser().getUserId();
    }
    public String getProjectId() {
        String projectId=request.getHeader("projectId");
        return projectId;
    }
}
