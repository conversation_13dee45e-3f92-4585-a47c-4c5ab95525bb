package com.wcp.core.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Redis配置类。
 * 该类负责配置并提供自定义的RedisTemplate。
 * 当Redis主机配置存在时，该配置类才会激活。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
@Configuration
public class PlmRedisConfig {

    @Value("${spring.redis.host:#{null}}")
    private String host;

    @Value("${spring.redis.port:6379}")
    private int port;

    @Value("${spring.redis.password:#{null}}")
    private String password;

    @Value("${spring.redis.jedis.pool.max-active:8}")
    private int maxActive;

    @Value("${spring.redis.jedis.pool.max-idle:8}")
    private int maxIdle;

    @Value("${spring.redis.jedis.pool.min-idle:0}")
    private int minIdle;

    @Value("${spring.redis.jedis.pool.max-wait:-1}")
    private long maxWait;
  /*  *//**
     * 配置 Jedis 连接池
     *//*
    @Bean
    public JedisPoolConfig jedisPoolConfig() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(maxActive); // 最大连接数
        poolConfig.setMaxIdle(maxIdle);   // 最大空闲连接数
        poolConfig.setMinIdle(minIdle);   // 最小空闲连接数
        poolConfig.setMaxWaitMillis(maxWait); // 最大等待时间
        return poolConfig;
    }

    *//**
     * 配置 RedisConnectionFactory
     *//*
    @Bean
    public RedisConnectionFactory redisConnectionFactory(JedisPoolConfig jedisPoolConfig) {
        if (host == null) {
            return null; // 如果未配置 Redis 主机，返回 null
        }

        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        if (password != null) {
            config.setPassword(password);
        }

        // 使用 JedisConnectionFactory 并设置连接池
        JedisConnectionFactory factory = new JedisConnectionFactory(config);
        factory.setPoolConfig(jedisPoolConfig);
        factory.afterPropertiesSet(); // 初始化连接池
        return factory;
    }

    *//**
     * 配置 RedisTemplate
     *//*
    @Bean
    public RedisTemplate<String, String> customRedisTemplate(RedisConnectionFactory factory) {
        if (factory == null) {
            return null; // 如果 RedisConnectionFactory 为 null，返回 null
        }

        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        return template;
    }*/
    /**
     * 创建并配置一个自定义的RedisTemplate。
     * 如果Redis主机配置不存在，此方法将返回null。
     * 该方法配置了Redis键值的序列化方式。
     *
     * @param factory Redis连接工厂，用于创建Redis连接。
     * @return 返回配置好的RedisTemplate实例，或在Redis主机未配置时返回null。
     */
    @Bean
    @Conditional(PlmRedisCondition.class)
    public RedisTemplate<String, String> customRedisTemplate(RedisConnectionFactory factory) {
        if (host == null) {
            return null;
        }
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        return template;
    }
}
