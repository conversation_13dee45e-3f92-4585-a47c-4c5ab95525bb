package com.wcp.core.redis;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.utils.HttpClientUtil;
import dm.jdbc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Redis消息发布管理器。
 * 用于将不同类型的消息发布到Redis频道。
 * 当Redis服务不可用时，提供备用的HTTP消息发布机制。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
@Component
@Configuration
@Slf4j
public class PlmRedisMessage {

    @Autowired(required = false)
    @Qualifier("customRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Value("${spring.redis.host:#{null}}")
    private String host;

    @Value("${spring.redis.redis-message:#{null}}")
    private String redisMessage;


    /**
     * 发布JSONObject类型的消息到指定Redis频道。
     *
     * @param channel 频道名称。
     * @param message 要发布的JSONObject消息。
     */
    public void publishMessageByJson(String channel, JSONObject message) {
        if (host != null) {
            if (message == null) {
                return;
            }
            redisTemplate.convertAndSend(channel, message.toJSONString());
        }
    }

    /**
     * 发布JSONArray类型的消息到指定Redis频道。
     *
     * @param channel 频道名称。
     * @param message 要发布的JSONArray消息。
     */
    public void publishMessageByJson(String channel, JSONArray message) {
        if (host != null) {
            if (message == null) {
                return;
            }
            redisTemplate.convertAndSend(channel, message.toJSONString());
        }
    }

    /**
     * 发布String类型的消息到指定Redis频道。
     *
     * @param channel 频道名称。
     * @param message 要发布的String消息。
     */
    public void publishMessageByStr(String channel, String message) {
        if (host != null) {
            redisTemplate.convertAndSend(channel, message);
        }
    }

    /**
     * 发布数据库刷新信息到指定Redis频道。
     * 当Redis不可用时，通过HTTP请求发送到备用服务。
     *
     * @param channel 频道名称。
     * @param message 要发布的数据库刷新信息。
     * @throws IOException 当HTTP请求失败时抛出。
     */
    public void publishDataBaseRefreshMessage(String channel, PlmRedisDataBaseRefreshInfo message) throws IOException {
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(message);
        if (host != null) {
            redisTemplate.convertAndSend(channel, jsonObject.toJSONString());
        } else if (redisMessage != null) {
            String url = "http://IpAndPort/wcp-service/dataBase/refresh";

            String[] split = redisMessage.split(",");
            for (String s : split) {
                if (StringUtil.isEmpty(s)) {
                    return;
                }
                url = url.replace("IpAndPort", s);
                HttpClientUtil.sendPost(url, jsonObject);
            }
        }
    }
}
