package com.wcp.core.redis;

import lombok.*;

/**
 * Redis数据库刷新信息类。
 * 用于封装与Redis数据库刷新相关的信息。
 * 包括原项目名、原服务名、更新后的项目名和服务名。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
public class PlmRedisDataBaseRefreshInfo {

    /**
     * 原项目名称
     */
    private String originalProject;

    /**
     * 原服务名称
     */
    private String originalService;

    /**
     * 更新后的项目名称
     */
    private String project;

    /**
     * 更新后的服务名称
     */
    private String service;
}
