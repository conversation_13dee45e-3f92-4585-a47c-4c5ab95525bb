package com.wcp.core.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Description: 缓存工具类，如果没有配置spring.redis就使用ConcurrentHashMap作为本地缓存
 * Author: qianchao
 * Date: 2024/1/11 15:45
 */
@Component
@Configuration
public class PlmRedisCache {
    @Autowired
    public RedisTemplate redisTemplate;
    //如果没有配置redis，host为null
    @Value("${spring.redis.host:#{null}}")
    private String host;
    private final Map<String, CacheEntry<Object>> localCache = new ConcurrentHashMap<>();

    public <T> void setCacheObject(final String key, final T value) {
        if (host != null) {
            redisTemplate.opsForValue().set(key, value);
        } else {
            localCache.put(key, new CacheEntry<>(value));
        }
    }

    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        if (host != null) {
            redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
        } else {
            localCache.put(key, new CacheEntry<>(value, System.currentTimeMillis() + timeUnit.toMillis(timeout)));
        }
    }

    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        if (host != null) {
            return redisTemplate.expire(key, timeout, unit);
        } else {
            CacheEntry<Object> entry = localCache.get(key);
            if (entry != null) {
                entry.setExpirationTime(System.currentTimeMillis() + unit.toMillis(timeout));
                return true;
            }
            return false;
        }
    }

    public <T> T getCacheObject(final String key) {
        if (host != null) {
            System.out.println(redisTemplate.getExpire(key,TimeUnit.MINUTES));
            ValueOperations<String, T> operation = (ValueOperations<String, T>) redisTemplate.opsForValue();
            return operation.get(key);
        } else {
            CacheEntry<Object> entry = localCache.get(key);
            if (entry != null && !entry.isExpired()) {
                return (T) entry.getValue();
            } else {
                localCache.remove(key);
                return null;
            }
        }
    }

    public boolean deleteObject(final String key) {
        if (host != null) {
            return redisTemplate.delete(key);
        } else {
            return localCache.remove(key) != null;
        }
    }

    public long deleteObject(final Collection<String> keys) {
        if (host != null) {
            return redisTemplate.delete(keys);
        } else {
            long count = 0;
            for (String key : keys) {
                if (localCache.remove(key) != null) {
                    count++;
                }
            }
            return count;
        }
    }

    // ... 其他方法类似 ...

    private static class CacheEntry<V> {
        private final V value;
        private long expirationTime;

        CacheEntry(V value) {
            this.value = value;
        }

        CacheEntry(V value, long expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }

        V getValue() {
            return value;
        }

        boolean isExpired() {
            return expirationTime > 0 && System.currentTimeMillis() > expirationTime;
        }

        void setExpirationTime(long expirationTime) {
            this.expirationTime = expirationTime;
        }
    }
}
