/*
package com.wcp;

import com.aizuda.bpm.engine.FlowLongEngine;
import com.aizuda.bpm.engine.ProcessService;
import com.aizuda.bpm.engine.QueryService;
import com.aizuda.bpm.engine.TaskService;
import com.aizuda.bpm.engine.core.FlowCreator;
import com.aizuda.bpm.engine.entity.FlwHisTask;
import com.aizuda.bpm.engine.entity.FlwTask;
import com.aizuda.bpm.engine.entity.FlwTaskActor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

*/
/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/4/14 15:03
 *//*



@SpringBootTest(classes = WorkflowApplication.class)
public class flowlongTest {
    @Autowired
    private FlowLongEngine flowLongEngine;


    */
/**
     * 创建流程
     *//*


    @Test
    public void deploy() {
        String jsonString="{\"id\":1,\"name\":\"请假审批\",\"key\":\"k0001\",\"nodeConfig\":{\"nodeName\":\"发起人\",\"nodeKey\":\"flk0001\",\"type\":0,\"nodeAssigneeList\":[],\"childNode\":{\"nodeName\":\"直接主管审批\",\"nodeKey\":\"flk0003\",\"type\":1,\"setType\":2,\"nodeAssigneeList\":[],\"examineLevel\":1,\"directorLevel\":1,\"selectMode\":1,\"termAuto\":false,\"term\":0,\"termMode\":1,\"examineMode\":1,\"directorMode\":0}}}";
        InputStream inputStream = new ByteArrayInputStream(jsonString.getBytes(StandardCharsets.UTF_8));
        FlowCreator flowCreator = new FlowCreator("0", "admin");
        flowCreator.tenantId("13fb36ce83ff44ea9b18c08496c634c6");//设置租户，这里是平台projectID
        Long processId = flowLongEngine.processService().deploy(inputStream, flowCreator, true);
        System.out.println(processId);
    }
    //提交流程
    @Test
    public void startInstance(){
        Map<String, Object> args = new HashMap<>();
        args.put("day", 8);
        args.put("flk0003", Arrays.asList("userId1", "userId2"));
        FlowCreator flowCreator = new FlowCreator("000002", "demo");
        flowCreator.tenantId("13fb36ce83ff44ea9b18c08496c634c6");//设置租户，这里是平台ID
        flowLongEngine.startInstanceById(Long.valueOf("1914193234172813313"), flowCreator, args);
       */
/* flowLongEngine.startInstanceById(Long.valueOf("1914193234172813313"), flowCreator, args).ifPresent(instance -> {
            // 其它流程操作
        });*//*

    }

    */
/**
     * 审批接口
     *//*

    @Test
    public void approve(){
         QueryService queryService   = flowLongEngine.queryService();
        // 流程任务服务
         TaskService taskService    = flowLongEngine.taskService();
        // 审批、驳回
        List<FlwTask> tasks3    = queryService.getActiveTasksByInstanceId(1914164827997315073L).get();
        //获取审批人
        List<FlwTaskActor> flwTaskActors=queryService.getTaskActorsByTaskId(tasks3.get(0).getId());
        flwTaskActors.forEach(a -> {
            if ("0000001".equals(a.getActorId())) {
                flowLongEngine.executeTask(tasks3.get(0).getId(), FlowCreator.of(a.getActorId(), a.getActorName()), Collections.singletonMap("reason", "同意"));
               // taskService.rejectTask(tasks3.get(0), FlowCreator.of(a.getActorId(), a.getActorName()), Collections.singletonMap("rejectReason", "不同意"));
            }
        });

    }

    */
/**
     * 获取审批操作日志
     *//*

    @Test
    public void queryTodoList(){
        // 获取当前流程实例的历史操作信息
        List<FlwHisTask> hisTasks =  flowLongEngine.queryService().getHisTasksByInstanceId(1914164827997315073L).get();
        hisTasks.forEach(t -> System.out.println("---> [历史节点] 节点名称 = " + t.getTaskName() + "，创建人 = " + t.getCreateBy()+",处理意见="+t.getVariable()));

    }
}
*/
