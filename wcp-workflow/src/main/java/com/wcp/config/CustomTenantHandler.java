package com.wcp.config;

import com.wcp.thread.WcpThreadLocal;
import org.dromara.warm.flow.core.handler.TenantHandler;

/**
 * 1. @Description 工作流租戶
 * 2. <AUTHOR>
 * 3. @Date 2025/4/24 10:03
 */

public class CustomTenantHandler implements TenantHandler {
    /**
     * 工作流租戶id 使用項目id
     * @return
     */
    @Override
    public String getTenantId() {
        String projectId=WcpThreadLocal.getProjectId();
        return projectId;
    }
}
