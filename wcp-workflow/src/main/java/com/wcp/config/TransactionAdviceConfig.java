package com.wcp.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.annotation.Resource;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/4/22 15:41
 */
@EnableTransactionManagement
@Configuration
public class TransactionAdviceConfig implements TransactionManagementConfigurer {
    @Resource(name = "workflowTransactionManager")
    private PlatformTransactionManager transactionManager;

    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return transactionManager;
    }
}
