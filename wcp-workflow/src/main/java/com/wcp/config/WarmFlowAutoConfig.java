package com.wcp.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/4/22 15:20
 */
@Configuration
@MapperScan(basePackages = {
        "org.dromara.warm.flow.orm.mapper"  // Warm-Flow 内置 Mapper
})
public class WarmFlowAutoConfig {
    @Value("${spring.datasource.url}")
    private String jdbcUrl;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    @Bean
    @Primary
    public DataSource dataSource() {
        DruidDataSource ds = new DruidDataSource();
        ds.setUrl(jdbcUrl);
        ds.setUsername(username);
        ds.setPassword(password);
        ds.setDriverClassName(driverClassName);
        ds.setInitialSize(5);
        ds.setMaxActive(20);
        ds.setMinIdle(3);
        ds.setMaxWait(60000);
        return ds;
    }

    @Bean
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);
        //factoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:/warm/flow/*.xml"));  // 如果你需要 XML Mapper
        return factoryBean.getObject();
    }

    @Bean(name = "workflowTransactionManager")
    @Primary
    public DataSourceTransactionManager workflowTransactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
