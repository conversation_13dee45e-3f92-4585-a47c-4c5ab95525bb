package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description 流程分类
 * 2. <AUTHOR>
 * 3. @Date 2025/5/12 17:10
 */
@ApiGroup(value = "流程流程分类", serviceKey = "flw-category", classify = "flw-category", classifyName = "流程流程分类")
public class FlowCategoryService {

    @ApiService(value = "新增流程分类", serviceKey = "add", signType = 0, notes = "新增流程分类")
    public  void addCategory(Map<String, Object> result, JSONObject object) {
        String sql="INSERT INTO WCP_FLOW_CATEGORY(CATEGORY_ID,CATEGORY_CODE,CATEGORY_NAME,SORT_INDEX,PROJECT_ID) VALUES(?,?,?,?,?)";
        String categoryCode=object.getString("categoryCode");
        String categoryName=object.getString("categoryName");
        int sortIndex=object.getInteger("sortIndex");
        String id= StringUtil.generateUUID();
        String projectId= WcpThreadLocal.getProjectId();
        // 1. 先查是否存在
        String checkSql = "SELECT CATEGORY_NAME FROM WCP_FLOW_CATEGORY WHERE CATEGORY_CODE = ? AND PROJECT_ID=?";
        List<Map<String,Object>> listCount=WcpDataBase.queryMap("wcp",checkSql,new Object[]{categoryCode,projectId},"CATEGORY_NAME-categoryName;");
        if (listCount!=null && listCount.size() > 0) {
            throw new ServiceException("分类编码已存在，不能重复添加！");
        }else{
            WcpDataBase.execute("wcp",sql,new Object[]{id,categoryCode,categoryName,sortIndex,projectId});
        }
    }


    @ApiService(value = "修改流程分类", serviceKey = "update", signType = 2, notes = "修改流程分类")
    public  void updateCategory(Map<String, Object> result, JSONObject object) {
        String categoryCode=object.getString("categoryCode");
        String categoryName=object.getString("categoryName");
        String categoryId=object.getString("categoryId");
        int sortIndex=object.getInteger("sortIndex");
        String projectId= WcpThreadLocal.getProjectId();
        // 1. 检查是否有重复的 categoryCode（排除自己）
        String checkSql = "SELECT CATEGORY_ID FROM WCP_FLOW_CATEGORY WHERE CATEGORY_CODE = ? AND PROJECT_ID = ? AND CATEGORY_ID != ?";
        List<Map<String, Object>> existing = WcpDataBase.queryMap("wcp", checkSql, new Object[]{categoryCode, projectId, categoryId}, null);
        if (existing != null && !existing.isEmpty()) {
            throw new ServiceException("分类编码已存在，不能重复！");
        }
        // 2. 执行更新
        String updateSql = "UPDATE WCP_FLOW_CATEGORY SET CATEGORY_CODE = ?, CATEGORY_NAME = ?, SORT_INDEX = ? WHERE CATEGORY_ID = ? AND PROJECT_ID = ?";
        WcpDataBase.execute("wcp", updateSql, new Object[]{categoryCode, categoryName, sortIndex, categoryId, projectId});

    }


    @ApiService(value = "删除流程分类", serviceKey = "delete", signType = 2, notes = "删除流程分类")
    public  void deleteCategory(Map<String, Object> result, JSONObject object) {
        String id = object.getString("categoryId");
        String projectId = WcpThreadLocal.getProjectId();

        if (StringUtil.isEmpty(id)) {
            throw new ServiceException("分类ID不能为空！");
        }

        // 可选：检查是否被引用（如流程模板中使用），根据业务需要判断是否添加

        String deleteSql = "DELETE FROM WCP_FLOW_CATEGORY WHERE CATEGORY_ID = ? AND PROJECT_ID = ?";
        boolean flag = WcpDataBase.execute("wcp", deleteSql, new Object[]{id, projectId});
        if (!flag) {
            throw new ServiceException("删除失败，分类可能不存在！");
        }
    }

    @ApiService(value = "查询流程分类", serviceKey = "list", signType = 2, notes = "查询流程分类")
    public  void queryCategory(Map<String, Object> result, JSONObject object) {
        String projectId = WcpThreadLocal.getProjectId();
        String sql = "SELECT CATEGORY_ID, CATEGORY_CODE , CATEGORY_NAME , SORT_INDEX  " +
                " FROM WCP_FLOW_CATEGORY WHERE PROJECT_ID = ? ORDER BY SORT_INDEX ASC";
        List<Map<String, Object>> list = WcpDataBase.queryMap("wcp", sql, new Object[]{projectId}, "CATEGORY_ID-categoryId;CATEGORY_CODE-categoryCode;CATEGORY_NAME-categoryName;SORT_INDEX-sortIndex");
        result.put("data", list);
    }
}
