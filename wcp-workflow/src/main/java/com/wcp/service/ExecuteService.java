package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.http.HttpStatus;
import com.wcp.thread.WcpThreadLocal;
import org.apache.commons.lang3.StringUtils;
import org.dromara.warm.flow.core.chart.BetweenChart;
import org.dromara.warm.flow.core.chart.FlowChart;
import org.dromara.warm.flow.core.dto.FlowParams;
import org.dromara.warm.flow.core.entity.Instance;
import org.dromara.warm.flow.core.enums.SkipType;
import org.dromara.warm.flow.core.service.ChartService;
import org.dromara.warm.flow.core.service.InsService;
import org.dromara.warm.flow.core.service.NodeService;
import org.dromara.warm.flow.core.service.TaskService;
import org.dromara.warm.flow.core.utils.MapUtil;

import javax.annotation.Resource;
import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2025/5/20 16:27
 */
@ApiGroup(value = "流程实例", serviceKey = "flw-execute", classify = "flw-execute", classifyName = "流程实例")
public class ExecuteService {
    @Resource
    private InsService insService;
    @Resource
    private TaskService taskService;
    @Resource
    private NodeService nodeService;
    //todo 流程状态    流程状态（0待提交 1审批中 2审批通过 4终止 5作废 6撤销 8已完成 9已退回 10失效 11拿回）
    /**
     *清理脏数据
     delete from wcp_flow_node;
     delete from wcp_flow_skip;
     delete from wcp_flow_task;
     delete from wcp_flow_user;
     delete from wcp_flow_his_task;
     delete from wcp_flow_instance;
     delete from wcp_flow_definition;
     */
    @Resource
    private ChartService chartService;
    @ApiService(value = "查询待办任务", serviceKey = "toDoPage", signType = 2, notes = "查询待办任务")
    public  void toDoPage(Map<String, Object> result, JSONObject object) {
        String account= WcpThreadLocal.getUserAccount();
        Integer currentPage=object.getInteger("currentPage");
        Integer pageSize=object.getInteger("currentPage");
        String sql=" SELECT  t.ID,t.NODE_CODE,t.NODE_NAME,t.NODE_TYPE,t.DEFINITION_ID,t.INSTANCE_ID,t.CREATE_TIME,t.UPDATE_TIME,t.TENANT_ID,  " +
                "            t.FLOW_STATUS,i.BUSINESS_ID,i.ACTIVITY_STATUS,d.FLOW_NAME,t.FORM_CUSTOM,t.FORM_PATH  " +
                "        FROM  WCP_FLOW_TASK t  " +
                "        LEFT JOIN WCP_FLOW_USER uu ON uu.ASSOCIATED = t.ID  " +
                "        LEFT JOIN WCP_FLOW_DEFINITION d ON t.DEFINITION_ID = d.ID  " +
                "        LEFT JOIN WCP_FLOW_INSTANCE i ON t.INSTANCE_ID = i.ID  "+
                "        WHERE UU.PROCESSED_BY=?   AND T.NODE_CODE=I.NODE_CODE  "+
                "        ORDER BY T.CREATE_TIME DESC";
        Map<String, Object> pageData=WcpDataBase.queryMapByPaging("wcp",sql,new Object[]{account},"ID-taskId;NODE_CODE-nodeCode;NODE_NAME-nodeName;NODE_TYPE-nodeType;DEFINITION_ID-definitionId;INSTANCE_ID-instanceId;" +
                "CREATE_TIME-createTime;UPDATE_TIME-updateTime;TENANT_ID-tenantId;FLOW_STATUS-flowStatus;" +
                "BUSINESS_ID-businessId;ACTIVITY_STATUS-activityStatus;FLOW_NAME-flowName;" +
                "FORM_CUSTOM-formCustom;FORM_PATH-formPath",currentPage,pageSize,null);
        result.put(HttpStatus.DATA,pageData);

    }

    @ApiService(value = "审批通过", serviceKey = "pass", signType = 2, notes = "审批通过")
    public void approve(Map<String, Object> result, JSONObject object) {
        // 流程变量
        //.ignore(true) 忽略权限
        String taskId=object.getString("taskId");
        String handler=WcpThreadLocal.getUserAccount();//办理人
        String message=object.getString("messages");//审批意见
        FlowParams flowParams=FlowParams.build().skipType(SkipType.PASS.getKey()).message(message)
                .handler(handler).ignore(true).ignoreCooperate(true).ignoreDepute(true).tenantId(WcpThreadLocal.getProjectId());
        taskService.skip(taskId,flowParams);
    }

    @ApiService(value = "退回", serviceKey = "reject", signType = 2, notes = "退回")
    public void reject(Map<String, Object> result, JSONObject object) {
        // 流程变量
        //.ignore(true) 忽略权限
        String taskId=object.getString("taskId");
        String handler=WcpThreadLocal.getUserAccount();//办理人
        String message=object.getString("messages");//审批意见
        String nodeCode=object.getString("nodeCode");//当前节点编码
        String instanceId=object.getString("instanceId");
        Instance instance =insService.getById(instanceId);
        FlowParams flowParams=FlowParams.build().skipType(SkipType.PASS.getKey()).message(message)
                .handler(handler).ignore(true).tenantId(WcpThreadLocal.getProjectId());
        Map<String,Object> variable=instance.getVariableMap();
        //如果nodeCode不为空就回退到指定节点
        if(StringUtils.isNotEmpty(nodeCode)){
            taskService.rejectAtWill(taskId,nodeCode,message,variable);
        }else{
            //否则回退到开始节点
            taskService.reject(taskId,message,variable);
        }
    }
    /**
     * 查询审批流程图
     * @param result
     * @param object
     */
    @ApiService(value = "查询流程图", serviceKey = "flowChart", signType = 2, notes = "查询流程图")
    public void flowChart(Map<String, Object> result, JSONObject object) {
        String instanceId=object.getString("instanceId");
        String charData=chartService.chartIns(instanceId, (flowChartChain) -> {
            List<FlowChart> flowChartList = flowChartChain.getFlowChartList();
            flowChartList.forEach(flowChart -> {
                if (flowChart instanceof BetweenChart) {
                    BetweenChart betweenChart = (BetweenChart) flowChart;
                    Map<String, Object> extMap = betweenChart.getNodeJson().getExtMap();
                    // 给节点顶部增加文字说明
                    // betweenChart.topText("办理时间: 2025-02-08 12:12:12", Color.red);
                    if (MapUtil.isNotEmpty(extMap)) {
                        for(Map.Entry<String, Object> entry : extMap.entrySet()){
                            // 给节点中追加文字
                            betweenChart.addText(entry.getKey() + ":", Color.red);
                            betweenChart.addText((String) entry.getValue(), Color.red);
                        }
                    }
                }
            });
        });
        result.put("data",charData);
    }
}
