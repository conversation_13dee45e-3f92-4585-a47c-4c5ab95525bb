<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wcp</groupId>
        <artifactId>wcp</artifactId>
        <version>3.0</version>
    </parent>

    <artifactId>wcp-workflow</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
    </properties>

    <dependencies>
        <!--<dependency>
            <groupId>com.aizuda</groupId>
            <artifactId>flowlong-spring-boot-starter</artifactId>
            <version>1.1.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>-->
      <!--  <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
        </dependency>
-->
       <!-- <dependency>
            <groupId>org.dromara.warm</groupId>
            <artifactId>warm-flow-mybatis-sb-starter</artifactId>
            <version>1.6.10-beat</version>
        </dependency>-->
        <dependency>
            <groupId>org.dromara.warm</groupId>
            <artifactId>warm-flow-mybatis-plus-sb-starter</artifactId>
            <version>1.7.2-beat</version>
        </dependency>
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-common</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-database</artifactId>
            <version>3.0</version>
        </dependency>

       <!-- <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.2</version>
        </dependency>
    </dependencies>

</project>