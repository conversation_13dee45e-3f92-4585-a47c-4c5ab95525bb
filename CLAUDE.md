# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

WCP3.0 是一个基于 Spring Boot 2.7.3 和 JDK 17 的多模块 Java 项目，采用 Maven 构建。这是一个企业级平台管理系统，包含用户管理、项目管理、工作流、单点登录等功能。

## 模块架构

- **wcp-common**: 公共模块，包含自定义注解、工具类、Redis缓存、MinIO服务等核心组件
- **wcp-platform**: 平台主模块，使用 MyBatis Plus，包含控制器、业务逻辑、实体类等
- **wcp-service**: 应用服务模块，沿用 wcp2.0 版本的 JDBC 方式
- **wcp-database**: 数据库访问模块，提供 JDBC 封装和多数据库支持
- **wcp-core**: 核心模块，包含定时器、平台服务等
- **wcp-sso**: 单点登录模块
- **wcp-workflow**: 工作流模块，使用 warm-flow

## 构建与运行命令

```bash
# 编译整个项目
mvn clean compile

# 打包项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# 运行平台模块 (端口: 9090)
cd wcp-platform
mvn spring-boot:run

# 运行服务模块 (端口: 9091)  
cd wcp-service
mvn spring-boot:run

# 运行测试
mvn test
```

## 开发配置

### 调试权限设置
- **wcp-platform**: 修改 `spring.security` 为 `false` 关闭权限过滤
- **wcp-service**: 修改 `spring.sso.openPermissions` 为 `false` 关闭权限过滤

### 数据库支持
项目支持多种数据库：MySQL、达梦、Oracle、人大金仓

### 重要端口
- wcp-platform: 9090 (上下文路径: /wcp-platform)
- wcp-service: 9091 (上下文路径: /hfds)

## 开发规范

### 数据库规范
- 表名和字段统一使用大写
- 字段禁止使用数据库关键字
- MyBatis 中只有模糊查询可使用 `$`，其他查询使用 `#{}`

### 接口开发
- platform 模块接口需要使用 Swagger 注解
- service 模块需要登录拦截的接口将 signtype 值设为 2
- Swagger 文档地址: http://127.0.0.1:9090/wcp-platform/doc.html

## 技术栈

### 核心框架
- Spring Boot 2.7.3
- MyBatis Plus 3.5.2
- Spring Security
- Quartz 定时器
- warm-flow 工作流

### 数据库与缓存
- MySQL/达梦/Oracle/人大金仓
- Redis (Jedis 3.7.0)

### 其他组件
- JWT Token
- MinIO 对象存储
- WebSocket
- 微信开放平台集成

## 项目打包结构

打包后目录结构：
```
wcp/wcp-platform/target/wcp/
├── lib/              # 项目依赖 jar
├── config/           # 项目配置
└── wcp-platform.jar  # 平台 jar 包
```