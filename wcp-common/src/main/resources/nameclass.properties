Object=java.lang.Object
Long=java.lang.Long
long=java.lang.Long.TYPE
Integer=java.lang.Integer
int=java.lang.Integer.TYPE
Short=java.lang.Short
short=java.lang.Short.TYPE
Byte=java.lang.Byte
byte=java.lang.byte
Float=java.lang.Float
float=java.lang.Float.TYPE
Double=java.lang.Double
double=java.lang.Double.TYPE
Boolean=java.lang.Boolean
boolean=java.lang.Boolean.TYPE
String=java.lang.String
Calendar=java.util.Calendar
Date=java.util.Date
Timestamp=java.security.Timestamp
LocalDate=java.time.LocalDate
LocalTime=java.time.LocalTime
LocalDateTime=java.time.LocalDateTime
HashSet=java.util.HashSet
TreeSet=java.util.TreeSet
LinkedHashSet=java.util.LinkedHashSet
ArrayList=java.util.ArrayList
List=java.util.List
LinkedList=java.util.LinkedList
HashMap=java.util.HashMap
TreeMap=java.util.TreeMap
LinkedHashMap=java.util.LinkedHashMap
JSONObject=com.alibaba.fastjson.JSONObject
JSONArray=com.alibaba.fastjson.JSONArray
