package com.wcp.aspect;

import com.wcp.annotation.RequirePermission;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2024/10/31 17:38
 */
@Aspect
@Component
public class PermissionAspect {
    @Before("@annotation(requirePermission)")
    public void checkPermission(RequirePermission requirePermission) {
        String requiredPermission = requirePermission.value();

        String userPermission = getCurrentUserPermission();

        if (!hasPermission(userPermission, requiredPermission)) {
            throw new ServiceException("No permission to access this resource", HttpStatus.UNAUTHORIZED);
        }
    }

    private String getCurrentUserPermission() {
        return "USER";
    }

    private boolean hasPermission(String userPermission, String requiredPermission) {
        return userPermission.equals(requiredPermission);
    }
}
