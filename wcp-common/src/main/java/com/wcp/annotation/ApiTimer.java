package com.wcp.annotation;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME) // 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ ElementType.METHOD }) // 定义注解的作用目标**作用范围字段、枚举的常量/方法
@Documented // 说明该注解将被包含在javadoc中
public @interface ApiTimer {
	/**
	 * 任务分组
	 * @return
	 */
	String group() default "";
	/**
	 * 作用标记，代表这个作用到什么上
	 * 不限制：0
	 * 某行业：4
	 * 某公司：5
	 * 某项目：6
	 * @return
	 */
	int signType() default 0;
	/**
	 * 与signType对应，当signType为-1、0、1时，此属性无效，此属性可以含多个属性，用逗号分隔
	 * @return
	 */
	String sign() default "";
	/**
	 * 时间表达式
	 * @return
	 */
	ApiTimerJob[] timerJob() default {};
}
