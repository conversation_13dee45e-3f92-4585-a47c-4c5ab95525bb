package com.wcp.annotation;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME) // 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ ElementType.METHOD }) // 定义注解的作用目标**作用范围字段、枚举的常量/方法
@Documented // 说明该注解将被包含在javadoc中
public @interface ApiExtension {
	/**
	 * 附加属性的名称
	 * @return
	 */
	public String name() default "";
	/**
	 * 附加属性的类型
	 * 可以为url、字符串、图片等等
	 * @return
	 */
	public String type() default "";
	/**
	 * 附加属性的值
	 * @return
	 */
	public String value() default "";
}
