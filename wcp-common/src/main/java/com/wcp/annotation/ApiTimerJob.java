package com.wcp.annotation;

import java.lang.annotation.*;
@Retention(RetentionPolicy.RUNTIME) // 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ ElementType.TYPE }) // 定义注解的作用目标**作用范围字段、枚举的常量/方法
@Documented // 说明该注解将被包含在javadoc中
public @interface ApiTimerJob {
	/**
	 * 任务ID
	 * @return
	 */
	long id() default 0L;
	/**
	 * 任务名称
	 * @return
	 */
	String name() default "";
	/**
	 * 时间表达式
	 * @return
	 */
	String cron() default "";
	/**
	 * 附加参数，Json形式
	 * @return
	 */
	String extra() default "";
	/**
	 * 任务描述
	 * @return
	 */
	String notes() default "";
}
