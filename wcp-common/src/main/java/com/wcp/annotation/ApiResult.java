package com.wcp.annotation;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME) // 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ ElementType.TYPE }) // 定义注解的作用目标**作用范围字段、枚举的常量/方法
@Documented // 说明该注解将被包含在javadoc中
public @interface ApiResult {
	/**
	 * 参数对应的名称
	 * @return
	 */
	public String name() default "";
	/**
	 * 结果示例值,直接用字符串即可,解析会做自动转换
	 * @return
	 */
	public String value() default "";
	/**
	 * 结果类型,直接用字符串即可,或者url
	 * @return
	 */
	public String resultType() default "String";
	/**
	 * 结果类型字节码
	 * @return
	 */
	public Class<?> clazz() default String.class;
	/**
	 * 结果详细描述
	 * @return
	 */
	public String notes() default "";

	/**
	 * 返回结果的下一级注解
	 * @return
	 */
	public ApiChildResult[] children() default {};
}
