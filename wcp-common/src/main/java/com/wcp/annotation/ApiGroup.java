package com.wcp.annotation;

import org.springframework.stereotype.Service;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME) // 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ ElementType.TYPE }) // 定义注解的作用目标**作用范围字段、枚举的常量/方法
@Documented // 说明该注解将被包含在javadoc中
@Service
public @interface ApiGroup {
	/**
	 * 对该分组接口进行简单的描述
	 * @return
	 */
	String value() default "";
	/**
	 * 服务分组标志
	 * @return
	 */
	String serviceKey() default "";
	/**
	 * 分类标志,例wcp/platform
	 * @return
	 */
	String classify() default "";
	/**
	 * 分类名称
	 * @return
	 */
	String classifyName() default "";
	/**
	 * 对该服务接口的详细描述
	 * @return
	 */
	 String notes() default "";
	/**
	 * 是否要隐藏
	 * @return
	 */
    boolean hidden() default false;
    
}
