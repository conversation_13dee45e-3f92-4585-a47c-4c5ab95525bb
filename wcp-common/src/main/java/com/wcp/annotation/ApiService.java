package com.wcp.annotation;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME) // 注解会在class字节码文件中存在，在运行时可以通过反射获取到
@Target({ ElementType.METHOD, ElementType.TYPE }) // 定义注解的作用目标**作用范围字段、枚举的常量/方法
@Documented // 说明该注解将被包含在javadoc中
public @interface ApiService {
	/**
	 * 对该服务接口进行简单的描述
	 * @return
	 */
	public String value() default "";
	/**
	 * 服务获取标志
	 * @return
	 */
	public String serviceKey() default "";
	/**
	 * 请求方式:GET、POST、PUT、DELETE、NONE
	 * @return
	 */
	public String requestType() default "NONE";
	/**
	 * 分类标志,如果所属类有ApiGroup,则该属性无效,例wcp/platform
	 * @return
	 */
	public String classify() default "";
	/**
	 * 分类名称,如果所属类有ApiGroup,则该属性无效
	 * @return
	 */
	public String classifyName() default "";
	/**
	 * 作用标记，代表这个作用到什么上
	 * 有私有接口：-1
	 * 不限制：0
	 * 平台：1
	 * 项目：2
	 * 共享通用接口：3
	 * 某行业：4
	 * 某公司：5
	 * 某项目：6
	 * @return
	 */
	public int signType() default 0;
	/**
	 * 与signType对应，当signType为-1、0、1时，此属性无效，此属性可以含多个属性，用逗号分隔
	 * @return
	 */
	public String sign() default "";
	/**
	 * 关联的图元名称
	 * @return
	 */
	public String graphName() default "";
	/**
	 * 参数类型集合
	 * @return
	 */
	public ApiParam[] params() default {};
	/**
	 * 附加属性的定义
	 * 可以在前段定义API图元，此处可以显示相应数据
	 * @return
	 */
	public ApiExtension[] extensions() default {};
	/**
	 * 对该服务接口的详细描述
	 * @return
	 */
	public String notes() default "";
	/**
	 * 返回结果
	 * @return
	 */
	public ApiResult[] results() default {};
	/**
	 * 是否需要接口还回成功标志
	 * @return
	 */
	public boolean nomark() default true;
	/**
	 * 接口编写人员
	 * @return
	 */
	public String author() default "Guest";
	/**
	 * 该服务报错时返回的错误码
	 * @return
	 */
	public int code() default 6000;

	/**
	 * 表示是否可以被scanRequests接口扫描到，默认都是可以被扫描
	 * @return
	 */
	public boolean scaner() default true;
}
