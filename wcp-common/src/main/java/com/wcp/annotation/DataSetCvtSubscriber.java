package com.wcp.annotation;


import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME) // 注解会在class字节码文件中存在，在运行时可以通过反射获取到  
@Target({ElementType.FIELD,ElementType.METHOD})//定义注解的作用目标**作用范围字段、枚举的常量/方法  
@Documented//说明该注解将被包含在javadoc中  
public @interface DataSetCvtSubscriber 
{
	/**
	 * 设置方案
	 * 获取或设置，分别为read和write
	 * @return
	 */
	public String setType() default "write";
	/**
	 * 数据类型，类名
	 * @return
	 */
	public String dataType() default "";
	/**
	 * 如果是Map类型的话，key的数据类型
	 * @return
	 */
	public String keyType() default "";
	/**
	 * 如果是Map或List类型的话，value的数据类型
	 * @return
	 */
	public String valueType() default "";
}

