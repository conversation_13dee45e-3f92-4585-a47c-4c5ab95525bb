package com.wcp.sms;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.wcp.execption.ServiceException;
import com.wcp.redis.CacheMemory;
import com.wcp.utils.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ShortMessageService {


    @Value("${spring.sms.regionId:}")
    public String regionId;
    @Value("${spring.sms.accessKeyId:}")
    public String accessKeyId;
    @Value("${spring.sms.accessKeySecret:}")
    public String accessKeySecret;
    @Value("${spring.sms.domain:}")
    public String domain;
    @Value("${spring.sms.signName:}")
    public String signName;
/**
     * 发送短信验证码
     *
     * @param mobile
     * @return
     */
    public String sendSms(String mobile,String templateCode) throws ClientException {

        DefaultProfile profile = DefaultProfile.getProfile(regionId,
                accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);

        //生成6位数的随机数作为验证码
        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(domain);
        //设置sms的版本,我看网上大部分用的都是2017-05-25
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", regionId);
        //目标手机号
        request.putQueryParameter("PhoneNumbers", mobile);
        //签名名称
        request.putQueryParameter("SignName", signName);
        //短信模板code
        request.putQueryParameter("TemplateCode", templateCode);
        //模板中变量替换
        request.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        CommonResponse response = client.getCommonResponse(request);

        //判断返回的信息是否包含code:ok,如果包含则代表发送验证码成功
        if (response.getData().contains("\"Code\":\"OK\"")) {
            //成功返回验证码
            return code;
        }
        return null;
    }
    /**
     *找回密码
     * @param phone
     * @return
     * @throws
     */

    public Boolean senSmsByRetrievePwd(String phone,String projectId) throws ClientException {

        //判断手机号是否为空,如果为空直接返回不执行后续操作
        if (StringUtil.isEmpty(phone)) {
           throw new ServiceException("手机号码不能为空");
        }
        //根据手机号获取redis 中的key
        String smsKey = CacheMemory.getNestKey("wcp", "aliyun", "sms-pwd", phone);

        //根据key在redis中获取value
        String value = CacheMemory.getValue(projectId, "wcp", String.class, smsKey);

        //判断redis中有没有
        if (value != null) {
            throw new ServiceException("验证码还未失效,请稍后重试");
        }
        //发送验证码
        String code = sendSms(phone,"SMS_490765226");
        //判断验证码是否为空,如果为空代表未发送成功
        if (StringUtil.isEmpty(code)) {
            throw new ServiceException("验证码发送失败,请重试");
       }
        //将验证码存入到redis中
        CacheMemory.setValue(projectId, "wcp", smsKey, code);
        //给验证码设置一个过期时间
        CacheMemory.setExpireTime("default", "wcp", smsKey, 300000);
        return true;
    }
/**
     * 注册验证手机号
     * @param phone
     * @return
     * @throws ClientException
     */
    public Boolean senSmsByRegister(String phone,String projectId) throws ClientException {

        //判断手机号是否为空,如果为空直接返回不执行后续操作
        if (StringUtil.isEmpty(phone)) {
            throw new ServiceException("手机号码不能为空");
        }
        //根据手机号获取redis 中的key
        String smsKey = CacheMemory.getNestKey("wcp", "aliyun", "sms-reg", phone);

        //根据key在redis中获取value
        String value = CacheMemory.getValue(projectId, "wcp", String.class, smsKey);
        //判断redis中有没有
        if (value != null) {
            throw new ServiceException("验证码还未失效,请稍后重试");
        }
        //发送验证码
        String code = sendSms(phone,"SMS_490670330");
        //判断验证码是否为空,如果为空代表未发送成功
        if (StringUtil.isEmpty(code)) {
            throw new ServiceException("验证码发送失败,请重试");
        }
        //将验证码存入到redis中
        CacheMemory.setValue(projectId, "wcp", smsKey, code);
        //给验证码设置一个过期时间
        CacheMemory.setExpireTime(projectId, "wcp", smsKey, 300000);
        return true;
    }

    /**
     * 登录发送验证码
     * @param phone
     * @param projectId
     * @return
     * @throws ClientException
     */
    public Boolean senSmsBylogin(String phone,String projectId) throws ClientException {

        //判断手机号是否为空,如果为空直接返回不执行后续操作
        if (StringUtil.isEmpty(phone)) {
            throw new ServiceException("手机号码不能为空");
        }
        //根据手机号获取redis 中的key
        String smsKey = CacheMemory.getNestKey("wcp", "aliyun", "sms-login", phone);

        //根据key在redis中获取value
        String value = CacheMemory.getValue(projectId, "wcp", String.class, smsKey);
        //判断redis中有没有
        if (value != null) {
            throw new ServiceException("验证码还未失效,请稍后重试");
        }
        //发送验证码
        String code = sendSms(phone,"SMS_491035227");
        //判断验证码是否为空,如果为空代表未发送成功
        if (StringUtil.isEmpty(code)) {
            throw new ServiceException("验证码发送失败,请重试");
        }
        //将验证码存入到redis中
        CacheMemory.setValue(projectId, "wcp", smsKey, code);
        //给验证码设置一个过期时间
        CacheMemory.setExpireTime(projectId, "wcp", smsKey, 300000);
        return true;
    }
}
