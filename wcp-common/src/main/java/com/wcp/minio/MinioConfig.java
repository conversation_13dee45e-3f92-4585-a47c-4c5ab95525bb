package com.wcp.minio;

import io.minio.MinioClient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description: TODO
 * Author: qianchao
 * Date: 2024/1/11 17:48
 */
@Configuration
@Data
public class MinioConfig {
    @Value("${spring.minio.endpoint:http://127.0.0.1:9000}")
    private String endpoint;

    @Value("${spring.minio.accessKey:default}")
    private String accessKey;

    @Value("${spring.minio.secretKey:default}")
    private String secretKey;
    @Value("${spring.minio.bucketName:default}")
    private String bucketName;

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
    }
}
