package com.wcp.minio;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.utils.StringUtil;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.*;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.Date;

/**
 * Description: minioIo服务类
 * Author: qianchao
 * Date: 2024/1/11 17:53
 */

public class MinioService {

    private static MinioClient minioClient;
    private static MinioConfig minioConfig;
    private static final Logger log = LoggerFactory.getLogger(MinioService.class);

    public static void init(MinioClient client, MinioConfig config) {
        minioClient = client;
        minioConfig = config;
    }

    /**
     * Description : 上传文件
     * @param objectName
     * @param file
     */
    public static void uploadFile(String objectName, MultipartFile file) {
        InputStream inputStream =null;
        try {
            inputStream=file.getInputStream();
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(objectName)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType(file.getContentType())
                            .build()
            );
        } catch (Exception e) {
            throw new ServiceException("上传文件失败", HttpStatus.ERROR);
        }finally {
            if (inputStream != null) {
                try {
                    inputStream.close(); // 关闭流
                } catch (IOException e) {
                    log.error("关闭文件流失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 查询minio中文件列表
     * @param prefix 地址
     * @return
     */
    public static JSONArray queryFiles(String prefix) {
        JSONArray resultArray = new JSONArray();

        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .prefix(prefix)
                            .recursive(true)
                            .build()
            );

            Map<String, JSONObject> dirMap = new TreeMap<>();

            for (Result<Item> result : results) {
                Item item = result.get();
                String objectName = item.objectName();

                if (item.isDir()) continue;

                String relativePath = objectName.substring(prefix.length());
                String[] parts = relativePath.split("/");

                if (parts.length == 1) {
                    // 是文件
                    JSONObject fileJson = new JSONObject();
                    fileJson.put("name", parts[0]);
                    fileJson.put("url", "/" + objectName);
                    fileJson.put("type", 2); // 文件类型
                    resultArray.add(fileJson);
                } else {
                    // 是子文件夹中的文件
                    String firstDir = parts[0];
                    JSONObject dir = dirMap.computeIfAbsent(firstDir, k -> {
                        JSONObject d = new JSONObject();
                        d.put("name", k);
                        d.put("type", 1); // 文件夹类型
                        d.put("children", new JSONArray());
                        return d;
                    });

                    JSONObject fileJson = new JSONObject();
                    fileJson.put("name", parts[parts.length - 1]);
                    fileJson.put("url", "/" + objectName);
                    fileJson.put("type", 2); // 文件类型

                    dir.getJSONArray("children").add(fileJson);
                }
            }

            resultArray.addAll(dirMap.values());

        } catch (Exception e) {
            log.error("获取 MinIO [{}] 下文件失败：{}", prefix, e.getMessage(), e);
        }

        return resultArray;
    }
    public static void downloadFiles(JSONArray fileArray, String localDir) {
        for (int i = 0; i < fileArray.size(); i++) {
            JSONObject fileObj = fileArray.getJSONObject(i);
            int type = fileObj.getIntValue("type");
            String name = fileObj.getString("name");

            if (type == 1) {
                // 子文件夹：递归
                JSONArray children = fileObj.getJSONArray("children");
                if (children != null && !children.isEmpty()) {
                    String subDir = localDir + File.separator + name;
                    new File(subDir).mkdirs(); // 创建本地子目录
                    downloadFiles(children, subDir); // 递归下载子目录
                }
            } else if (type == 2) {
                // 是文件，获取 URL，下载并保存
                String url = fileObj.getString("url");
                try (InputStream inputStream = MinioService.download(url)) {
                    File outFile = new File(localDir, name);
                    outFile.getParentFile().mkdirs();

                    try (FileOutputStream fos = new FileOutputStream(outFile)) {
                        byte[] buffer = new byte[8192];
                        int len;
                        while ((len = inputStream.read(buffer)) != -1) {
                            fos.write(buffer, 0, len);
                        }
                        System.out.println("下载成功: " + url + " -> " + outFile.getAbsolutePath());
                    }
                } catch (Exception e) {
                    System.err.println("下载失败: " + url);
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * Description : 查询项目ID下面的所有文件
     * @param projectId
     * @return
     */
    public static JSONArray listFiles(String projectId) {
        JSONArray jsonArray = new JSONArray();
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .prefix(projectId)
                            .recursive(true)
                            .build());

            JSONObject root = new JSONObject();
            root.put("name", projectId);
            root.put("children", new JSONArray());
            jsonArray.add(root);

            for (Result<Item> result : results) {
                Item item = result.get();
                String objectName = item.objectName();

                if (objectName.equals(projectId)) {
                    continue; // Skip the directory itself
                }
                String[] parts = objectName.split("/");
                JSONObject current = root;
                for (int i = 1; i < parts.length; i++) {
                    String part = parts[i];
                    if (part.isEmpty()) {
                        continue;
                    }
                    JSONArray children = current.getJSONArray("children");
                    JSONObject child = null;
                    for (Object obj : children) {
                        JSONObject jsonObject = (JSONObject) obj;
                        if (jsonObject.getString("name").equals(part)) {
                            child = jsonObject;
                            break;
                        }
                    }
                    if (child == null) {
                        child = new JSONObject();
                        child.put("name", part);
                        //child.put("children", new JSONArray());
                        child.put("url","/"+objectName.substring(0,objectName.indexOf(part)+part.length()));
                        if (!part.contains(".")) { // Check if it's a directory
                            child.put("children", new JSONArray());
                        }
                        /*if (i < parts.length-1 ) { // Check if it's a directory
                            child.put("children", new JSONArray());
                        }
                        if(child.getJSONArray("children")==null && !part.contains(".")){
                            child.put("children", new JSONArray());
                        }*/
                        children.add(child);
                    }
                    current = child;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonArray;
    }

    /**
     * Description : 下载一个文件
     * @param objectName
     * @return
     * @throws Exception
     */
    public static InputStream download(String objectName) throws Exception {
        InputStream stream = minioClient.getObject(
                GetObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
        return stream;
    }

    /**
     * Description : 删除一个对象以及子集对象
     * @param objectName
     * @throws Exception
     */
    public static void deleteObject( String objectName) {
        try {
            if (StringUtils.isNotBlank(objectName)) {
                Iterable<io.minio.Result<Item>> list = minioClient.listObjects(
                        ListObjectsArgs.builder().bucket(minioConfig.getBucketName()).prefix(objectName).recursive(true).build());
                list.forEach(e -> {
                    try {
                        minioClient.removeObject(RemoveObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
                    }catch (Exception exception) {
                        exception.printStackTrace();
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量删除文件夹及其子文件
     * @param folderPrefix
     */
    public static void batchDeleteFolder(String folderPrefix) {
        try {
            if (StringUtils.isNotBlank(folderPrefix)) {
                // 确保 prefix 最后带 "/"
                if (!folderPrefix.endsWith("/")) {
                    folderPrefix += "/";
                }

                Iterable<Result<Item>> results = minioClient.listObjects(
                        ListObjectsArgs.builder()
                                .bucket(minioConfig.getBucketName())
                                .prefix(folderPrefix)  // 确保是 prefix
                                .recursive(true)       // 递归找子文件
                                .build()
                );

                List<DeleteObject> deleteObjects = new ArrayList<>();

                for (Result<Item> result : results) {
                    Item item = result.get();
                    System.out.println("准备删除对象：" + item.objectName());  // 加一行打印确认
                    deleteObjects.add(new DeleteObject(item.objectName()));
                }

                if (!deleteObjects.isEmpty()) {
                    // 注意！！！ removeObjects返回的是错误项，需要遍历
                    Iterable<Result<DeleteError>> errors = minioClient.removeObjects(
                            RemoveObjectsArgs.builder()
                                    .bucket(minioConfig.getBucketName())
                                    .objects(deleteObjects)
                                    .build()
                    );

                    for (Result<DeleteError> errorResult : errors) {
                        DeleteError error = errorResult.get();
                        System.err.println("删除失败：" + error.objectName() + "，错误：" + error.message());
                    }
                } else {
                    System.out.println("没有找到子文件，不需要删除！");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Description :获取文件的修改时间
     * @param objectName
     * @return
     */
    public static Calendar getFileLastModified(String objectName) {
        try {
            StatObjectResponse stat = minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(objectName)
                            .build()
            );
            ZonedDateTime lastModified = stat.lastModified();
            Calendar calendar = GregorianCalendar.from(lastModified);
            return calendar;
        } catch (Exception e) {
            log.error("获取文件状态失败：{}", e.getMessage());
            throw new ServiceException("获取文件状态失败", HttpStatus.ERROR);
        }
    }

    /**
     *
     * @param objectName 通过objectName生成一个可访问的链接
     * @param time 以秒为单位
     * @return
     * @throws Exception
     */
    public static String getPresignedObjectUrl(String objectName,int time) {
        GetPresignedObjectUrlArgs build = GetPresignedObjectUrlArgs
                .builder().bucket(minioConfig.getBucketName()).object(objectName).expiry(time).method(Method.GET).build();
        try {
            String urlString= minioClient.getPresignedObjectUrl(build);
            /*URL url = new URL(urlString);
            // 获取路径部分（去掉前缀部分）
            String tempUrl= url.getFile();
            int firstSlashIndex = tempUrl.indexOf('/');

            int secondSlashIndex = tempUrl.indexOf('/', firstSlashIndex + 1);
            return  tempUrl.substring(secondSlashIndex);*/
            return urlString;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Description : 通过objectName生成一个可访问的链接
     * @param objectName
     * @return
     * @throws Exception
     */
    public static String getPresignedObjectUrl(String objectName) throws Exception {
        GetPresignedObjectUrlArgs build = GetPresignedObjectUrlArgs
                .builder().bucket(minioConfig.getBucketName()).object(objectName).method(Method.GET).build();
        return minioClient.getPresignedObjectUrl(build);
    }

    /**
     * Description : 上传一个文件
     * @param stream
     * @param objectName
     * @throws Exception
     */
    public static void uploadFile(InputStream stream, String objectName) throws Exception {
        minioClient.putObject(PutObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName)
                .stream(stream, -1, 10485760).build());
    }
    /**
     * Description : 判断文件是否存在
     * @param objectName 文件名
     * @return
     */
    public static boolean isObjectExist(String objectName) {
        boolean exist = true;
        try {
            minioClient.statObject(StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
        } catch (Exception e) {
            exist = false;
        }
        return exist;
    }
    /**
     * Description : 判断文件夹是否存在
     * @param objectName 文件夹名称
     * @return
     */
    public static boolean isFolderExist(String objectName) {
        boolean exist = false;
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder().bucket(minioConfig.getBucketName()).prefix(objectName).recursive(false).build());
            for (Result<Item> result : results) {
                Item item = result.get();
                if (item.objectName()!=null) {
                    exist = true;
                }
            }
        } catch (Exception e) {
            exist = false;
        }
        return exist;
    }

    /**
     * Description : 创建文件夹
     * @param folderName
     * @return
     * @throws Exception
     */
    public static String createFolder(String folderName) throws Exception{
        return minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(folderName)
                        .stream(new ByteArrayInputStream(new byte[]{}), 0, -1)
                        .build()).etag();
    }
    /**
     *Description :  删除文件
     * @param objectName 文件名称
     */
    public static void removeFile(String objectName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .build());
    }
    /**
     * Description : 批量删除文件
     * @param keys 需要删除的文件列表
     * @return
     */
    public static void removeFiles(List<String> keys) {
        List<DeleteObject> objects = new LinkedList<>();
        keys.forEach(s -> {
            objects.add(new DeleteObject(s));
            try {
                removeFile(s);
            } catch (Exception e) {
                log.error("批量删除失败！error:{}",e);
            }
        });
    }

    /**
     * Description : csv数据上传到minio H2关联查询
     * @param tableName
     * @param sql
     * @return
     */
    public static List<Map<String, Object>> executeMinioQuery(String tableName,String sql) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        Connection conn = null;
        try {
            // 连接到 H2 数据库
            Class.forName("org.h2.Driver");
            String dbUrl = "jdbc:h2:mem:wcp;MODE=MySQL"; // 内存模式
            conn = DriverManager.getConnection(dbUrl);
            // 创建表并加载数据
            Statement stmt = conn.createStatement();
            // 从 MinIO 加载数据
            String fileUrl=minioConfig.getEndpoint()+"/"+minioConfig.getBucketName()+"/"+"wcp_fx_inspection_202403180843.csv";
            stmt.executeUpdate("CREATE TABLE IF NOT EXISTS "+tableName+" AS SELECT * FROM CSVREAD('"+fileUrl+"')");
            // 查询数据
            ResultSet resultSet = stmt.executeQuery(sql);
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (resultSet.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = StringUtil.isNullOrEmpty(metaData.getColumnLabel(i))?metaData.getColumnName(i):metaData.getColumnLabel(i);
                    Object columnValue = resultSet.getObject(i);
                    row.put(columnName, columnValue);
                }
                resultList.add(row);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (conn != null) {
                    conn.close(); // 关闭数据库连接
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return resultList;
    }

    /**
     * 重命名文件夹
     * @param oldFolderName 旧文件夹
     * @param newFolderName 新文件夹
     */
    public static void renameFolder(String oldFolderName,String newFolderName) throws Exception{
        Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .prefix(oldFolderName)
                        .recursive(true)
                        .build());
        results.forEach(e -> {
            try {
                if (!e.get().objectName().contains(".")){
                    //如果没有文件后缀表示读完了 最后一个文件目录 返回
                    return;
                }
                String s = e.get().objectName();
                String[] fileName = URLDecoder.decode(e.get().objectName(),"utf-8").split("/");
                minioClient.copyObject(CopyObjectArgs.builder()
                        .source(CopySource.builder().bucket(minioConfig.getBucketName()).object(URLDecoder.decode(e.get().objectName(),"utf-8")).build())
                        .bucket(minioConfig.getBucketName())
                        .object(newFolderName+"/"+fileName[fileName.length - 1])
                        .build());
                //删除旧文件
                minioClient.removeObject(RemoveObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(s)
                        .build());
            } catch (Exception ex) {
                throw new RuntimeException();
            }
        });

        // 删除文件夹本身
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(oldFolderName)
                        .build());
    }

    /**
     * 重命名 MinIO 中的文件
     * @param originalObjectName 原文件名
     * @param newObjectName     新文件名
     * @return 是否重命名成功
     */
    public boolean renameFile(String originalObjectName, String newObjectName) {
        try {
            String bucketName=minioConfig.getBucketName();
            // 检查存储桶是否存在
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!isExist) {
                System.err.println("Bucket does not exist: " + bucketName);
                return false;
            }
            // 复制对象到新名称
            minioClient.copyObject(
                    CopyObjectArgs.builder()
                            .bucket(bucketName)
                            .object(newObjectName)
                            .source(
                                    CopySource.builder()
                                            .bucket(bucketName)
                                            .object(originalObjectName)
                                            .build())
                            .build());
            // 删除原对象
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(originalObjectName)
                            .build());

            System.out.println("File renamed successfully from " + originalObjectName + " to " + newObjectName);
            return true;
        } catch (MinioException | InvalidKeyException | NoSuchAlgorithmException | IOException e) {
            System.err.println("Error occurred while renaming file: " + e.getMessage());
            return false;
        }
    }

    /**
     * Description : 复制文件
     * @param sourceObjectName 源文件名称
     * @param destObjectName 目标文件名称
     */
    public static void copyObject(String sourceObjectName,String destObjectName){
        try{
            minioClient.copyObject(CopyObjectArgs.builder()
                    .source(CopySource.builder().bucket(minioConfig.getBucketName()).object(URLDecoder.decode(sourceObjectName,"utf-8")).build())
                    .bucket(minioConfig.getBucketName())
                    .object(destObjectName)
                    .build());
        } catch (Exception e) {
            System.out.println("Error occurred: " + e.getMessage());
        }
    }

    public static List<String> copyFolder(String sourceFolder, String destinationFolder) {
        List<String> copiedFiles = new ArrayList();

        try {
            Iterable<Result<Item>> objects = minioClient.listObjects((ListObjectsArgs)((ListObjectsArgs.Builder)ListObjectsArgs.builder().bucket(minioConfig.getBucketName())).prefix(sourceFolder).recursive(true).build());
            objects.forEach((e) -> {
                try {
                    if (((Item)e.get()).objectName().contains(".")) {
                        String s = ((Item)e.get()).objectName();
                        String[] fileName = URLDecoder.decode(((Item)e.get()).objectName(), "utf-8").split("/");
                        minioClient.copyObject((CopyObjectArgs)((CopyObjectArgs.Builder)((CopyObjectArgs.Builder)CopyObjectArgs.builder().source((CopySource)((CopySource.Builder)((CopySource.Builder)CopySource.builder().bucket(minioConfig.getBucketName())).object(URLDecoder.decode(((Item)e.get()).objectName(), "utf-8"))).build()).bucket(minioConfig.getBucketName())).object(destinationFolder + "/" + fileName[fileName.length - 1])).build());
                        copiedFiles.add(destinationFolder + "/" + fileName[fileName.length - 1]);
                    }
                } catch (Exception var5) {
                    throw new RuntimeException();
                }
            });
            return copiedFiles;
        } catch (Exception var4) {
            throw new ServiceException("Minio复制文件夹时发生异常", 400);
        }
    }

    /**
     * 获取指定目录下的所有图片URL以及json文件url 并排序
     * @param directory 目录路径（如：zonghe/ebi/project/.../rehearsal/）
     * @return 图片URL列表
     */
    public static List<String> listImageUrls( String directory) {
        List<Map.Entry<String, Integer>> imageEntries = new ArrayList<>();
        // 存储global_bounds.json的URL
        String jsonUrl = null;

        try {
            // 检查目录是否以斜杠结尾
            if (!directory.endsWith("/")) {
                directory += "/";
            }

            // 遍历目录下的所有对象
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .prefix(directory)  // 只列出指定目录下的对象
                            .recursive(false)   // 不递归子目录
                            .build());

            for (Result<Item> result : results) {
                Item item = result.get();
                String objectName = item.objectName();

                // 检查是否为global_bounds.json
                if (objectName.endsWith("global_bounds.json")) {
                    jsonUrl = getPresignedObjectUrl(objectName);
                    continue; // 跳过后续图片处理逻辑
                }

                // 过滤出图片文件
                if (isImageFile(item.objectName())) {

                    // 提取文件名（去除路径部分）
                    String fileName = objectName.substring(objectName.lastIndexOf('/') + 1);
                    // 提取_后的数字部分
                    int number = extractNumberFromFileName(fileName);
                    // 将对象名和提取的数字存入列表
                    imageEntries.add(new AbstractMap.SimpleEntry<>(objectName, number));
                }
            }

            // 按提取的数字排序
            imageEntries.sort(Map.Entry.comparingByValue());

            // 生成排序后的URL列表
            List<String> urlList = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : imageEntries) {
                String url = getPresignedObjectUrl(entry.getKey());
                if (url != null) {
                    urlList.add(url);
                }
            }
            // 如果找到JSON文件，添加到列表末尾
            if (jsonUrl != null) {
                urlList.add(jsonUrl);
            }

            return urlList;
        } catch (Exception e) {
            System.out.println("获取图片URL列表失败");
            return Collections.emptyList();
        }
    }

    /**
     * 批量上传文件夹中所有图片到MinIO
     * @param localDirectory 本地文件夹路径
     * @param targetFolder  上传路径
     * @return 成功上传的文件列表（包含MinIO完整路径）
     */
    public static List<String> uploadDirectoryImages(String localDirectory, String targetFolder) {
        List<String> uploadedFiles = new ArrayList<>();
        File directory = new File(localDirectory);

        // 检查本地文件夹是否存在且为目录
        if (!directory.exists() || !directory.isDirectory()) {
            return uploadedFiles;
        }
        try {
            // 确保存储桶存在
            System.out.println(minioConfig.getBucketName());
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(minioConfig.getBucketName()).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(minioConfig.getBucketName()).build());
            }

            // 遍历本地文件夹中的所有文件
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    // 处理图片和JSON文件
                    if (file.isFile() && (isImageFile(file.getName()) || isJsonFile(file.getName()))) {
                        try (InputStream inputStream = new FileInputStream(file)) {
                            // 构建MinIO中的完整对象名
                            String objectName = targetFolder + file.getName();

                            // 上传文件
                            minioClient.putObject(PutObjectArgs.builder()
                                    .bucket(minioConfig.getBucketName())
                                    .object(objectName)
                                    .stream(inputStream, file.length(), -1)
                                    .contentType(getContentType(file.getName()))
                                    .build());

                            uploadedFiles.add(objectName);
                        } catch (Exception e) {
                        }
                    }
                }
            }

            return uploadedFiles;
        } catch (Exception e) {
            return uploadedFiles;
        }
    }

    /**
     * 判断文件是否为JSON类型
     */
    public static boolean isJsonFile(String fileName) {
        return fileName.toLowerCase().endsWith(".json");
    }

    /**
     * 根据文件名获取Content-Type
     */
    public static String getContentType(String fileName) {
        String lowerCaseName = fileName.toLowerCase();
        if (lowerCaseName.endsWith(".jpg") || lowerCaseName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerCaseName.endsWith(".png")) {
            return "image/png";
        } else if (lowerCaseName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerCaseName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (lowerCaseName.endsWith(".webp")) {
            return "image/webp";
        } else if (lowerCaseName.endsWith(".json")) {
            return "application/json";
        }

        return "application/octet-stream";
    }

    /**
     * 判断文件是否为图片类型
     */
    public static boolean isImageFile(String fileName) {
        String lowerCaseName = fileName.toLowerCase();
        return lowerCaseName.endsWith(".jpg") || lowerCaseName.endsWith(".jpeg") ||
                lowerCaseName.endsWith(".png") || lowerCaseName.endsWith(".gif") ||
                lowerCaseName.endsWith(".bmp") || lowerCaseName.endsWith(".webp");
    }

    /**
     * 从文件名中提取_后的数字部分
     * @param fileName 文件名
     * @return 提取的数字，如果没有则返回0
     */
    public static int extractNumberFromFileName(String fileName) {
        try {
            int underscoreIndex = fileName.lastIndexOf('_');
            if (underscoreIndex != -1) {
                // 提取_后的部分
                String numberPart = fileName.substring(underscoreIndex + 1);
                // 去除扩展名
                int dotIndex = numberPart.lastIndexOf('.');
                if (dotIndex != -1) {
                    numberPart = numberPart.substring(0, dotIndex);
                }
                // 转换为整数
                return Integer.parseInt(numberPart);
            }
        } catch (NumberFormatException e) {

        }
        return 0; // 默认返回0
    }
}
