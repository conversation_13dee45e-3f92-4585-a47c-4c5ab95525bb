package com.wcp.component.chart.data;

import com.wcp.component.common.AComponent;
import lombok.Data;

@Data
public class ChartAppValue extends AComponent {
    /** 序列名称 */
    public String name;
    /** 序列键 */
    public String key;
    /** 序列值 */
    public Double[] value;
    /** 序列所在的Grid */
    public int gridIndex = 0;
    /** 序列是否是使用主轴 */
    public boolean mainAxis = true;
    /** 序列是否可拖拽 */
    public boolean draggable = false;
    /** 序列类型 */
    public String type;

    public ChartAppValue(String name, String key, Double[] value, int gridIndex, String type) {
        this(name, key, value, gridIndex, true, false, type);
    }

    public ChartAppValue(String name, String key, Double[] value, int gridIndex, boolean mainAxis, boolean draggable, String type) {
        this.name = name;
        this.key = key;
        this.value = value;
        this.gridIndex = gridIndex;
        this.mainAxis = mainAxis;
        this.draggable = draggable;
        this.type = type;
    }
}
