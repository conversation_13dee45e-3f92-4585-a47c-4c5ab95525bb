package com.wcp.component.chart.appchart;

import com.alibaba.fastjson.JSONObject;
import com.wcp.component.chart.common.ChartGrid;
import com.wcp.component.chart.common.ChartSeriesOption;
import com.wcp.component.chart.common.ChartTooltipOption;
import com.wcp.component.chart.common.ChartxAxisOption;
import com.wcp.component.chart.data.ChartFixedData;
import com.wcp.component.chart.series.ChildSeries;
import com.wcp.component.common.AComponent;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 业务应用图形类
 * @auth AndyLu
 * @date 2023-01-07
 */
@Data
public class AppFixedChartCpt extends AComponent {
    /** 图形与左边边框的距离，不包含图例，不设置采用默认值 */
//    private Integer left = 50;
//    /** 图形与右边边框的距离，不包含图例，不设置采用默认值 */
//    private Integer right= 50;
//    /** 图形与上边边框的距离，不包含图例，不设置采用默认值 */
//    private Integer top= 50;
//    /** 图形与下边边框的距离，不包含图例，不设置采用默认值 */
//    private Integer bottom= 50;
//    /** 各内部图形的水平距离，不设置采用默认值 */
//    private Integer horiSpacing= 50;
//    /** 各内部图形的垂直距离，不设置采用默认值 */
//    private Integer vertiSpacing= 50;

    private List<ChartFixedData> data;

    private List<ChildSeries> series;

    private List<ChartGrid> grid = new ArrayList<>();

    private ChartTooltipOption tooltipOption;

    private Boolean dataDraggable = true;

    private Integer dataDraggableNum = 0;

    private ChartSeriesOption seriesOption = new ChartSeriesOption();

    private ChartGrid gridOption;

    private ChartxAxisOption xaxisOption = new ChartxAxisOption();

    private Object[] title = new Object[]{};

    private JSONObject theme = new JSONObject();

    private JSONObject toolbox = new JSONObject();

    private String[] dataZoom = new String[]{};

    private JSONObject yAxisOption = new JSONObject();

    private JSONObject legendOption = new JSONObject();

    private JSONObject scrollbar = new JSONObject();

    private JSONObject scrollbarThumb = new JSONObject();
    private JSONObject scrollbarButton = new JSONObject();
    private JSONObject scrollbarCorner = new JSONObject();
    private JSONObject scrollbarStyle = new JSONObject();
    private JSONObject scrollbarTrack = new JSONObject();

    //    {
//        "data": [{
//        "key": 1,
//                "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
//        "value": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
//    }, {
//        "key": 2,
//                "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
//        "value": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
//    }],
//        "series": [{
//        "name": "系列1",
//                "type": "line",
//                "gridIndex": 0,
//                "secondaryXAxis": false,
//                "secondaryYAxis": false,
//                "draggable": true,
//                "dataIndex": 0,
//                "dataKey": "value"
//    }, {
//        "name": "系列2",
//                "type": "line",
//                "gridIndex": 0,
//                "secondaryXAxis": false,
//                "secondaryYAxis": false,
//                "draggable": true,
//                "dataIndex": 0,
//                "dataKey": "value2"
//    }],
//        "grids": [],
//        "dataDraggable": true,
//            "dataDraggableNum": 0,
//            "seriesOption": {
//        "ratio": 1
//    },
//        "gridOption": {
//        "left": 50,
//                "right": 50,
//                "top": 50,
//                "bottom": 50,
//                "horiSpacing": 50,
//                "vertiSpacing": 50,
//                "range": 1,
//                "maxNum": 2
//    },
//        "xAxisOption": {
//        "dataIndex": 0,
//                "dataKey": "x"
//    },
//        "yAxisOption": {},
//        "legendOption": {},
//        "tooltipOption": {
//        "show": false,
//                "trigger": "axis"
//    },
//        "title": [],
//        "theme": {},
//        "scrollbar": {},
//        "scrollbarThumb": {},
//        "scrollbarTrack": {},
//        "scrollbarButton": {},
//        "scrollbarCorner": {},
//        "scrollbarStyle": {}
//    }

    /**
     * 往数据集中添加数据
     * @param chartData
     */
    public void addChartData(ChartFixedData chartData) {
        if(data == null) {
            data = new ArrayList<ChartFixedData>();
        }
        data.add(chartData);
    }

    /**
     * 清空数据集
     */
    public void clearChartData() {
        data = new ArrayList<>();
    }
}
