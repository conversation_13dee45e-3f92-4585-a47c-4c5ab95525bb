package com.wcp.component.chart.common;

import com.wcp.component.common.AComponent;
import lombok.Data;

/**
 * 直角坐标系 grid 中的 y 轴，一般情况下单个 grid 组件最多只能放左右两个 y 轴，多于两个 y 轴需要通过配置 offset 属性防止同个位置多个 Y 轴的重叠。
 */
@Data
public class ChartyAxis extends AComponent {

    /**
     * 坐标轴类型
     * value' 数值轴，适用于连续数据。
     *
     * 'category' 类目轴，适用于离散的类目数据。为该类型时类目数据可自动从 series.data 或 dataset.source 中取，或者可通过 yAxis.data 设置类目数据。
     *
     * 'time' 时间轴，适用于连续的时序数据，与数值轴相比时间轴带有时间的格式化，在刻度计算上也有所不同，例如会根据跨度的范围来决定使用月，星期，日还是小时范围的刻度。
     *
     * 'log' 对数轴。适用于对数数据。
     */
    private String type = "value";

}
