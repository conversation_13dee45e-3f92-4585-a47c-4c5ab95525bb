package com.wcp.component.chart.series;

import com.wcp.component.common.AComponent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChildSeries extends AComponent {

    private String name = "系类一";

    private String type = "line";

    private Integer gridIndex = 0;

    private Boolean secondaryXAxis = false;

    private Boolean secondaryYAxis = false;

    private Boolean draggable = true;

    private Integer dataIndex = 0;

    private String dataKey = "values";

    private String symbol;

    private Boolean smooth;
}
