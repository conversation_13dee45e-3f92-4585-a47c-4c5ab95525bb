package com.wcp.component.chart.common;

import com.alibaba.fastjson.JSONObject;
import com.wcp.component.common.AComponent;
import com.wcp.utils.StringUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 前端组件文本样式类
 * @auth AndyLu
 * @date 2023-01-07
 */
@Data
public class ChartTextStyle extends AComponent {
    /** 主标题文字的颜色 */
    public String color = "#333";
    /** 主标题文字字体的风格(ChartConst.FontStyle) */
    public String fontStyle = ChartConst.FontStyle.NORMAL;
    /** 主标题文字字体的粗细(ChartConst.FontWeight) */
    public String fontWeight = ChartConst.FontWeight.BOLDER;
    /** 主标题文字的字体系列('serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei', ...) */
    public String fontFamily = "sans-serif";
    /** 主标题文字的字体大小 */
    public Integer fontSize = 18;
    /** 行高(rich 中如果没有设置 lineHeight，则会取父层级的 lineHeight) */
    public Integer lineHeight;
    /** 文本显示宽度 */
    public Integer width;
    /** 文本显示高度 */
    public Integer height;
    /** 文字本身的描边颜色 */
    public String textBorderColor;
    /** 文字本身的描边宽度 */
    public Integer textBorderWidth;
    /**
     * 文字本身的描边类型(ChartConst.BorderType)
     * 自 v5.0.0 开始，也可以是 number 或者 number 数组，例如：textBorderType: [5, 10]
     * */
    public String textBorderType;
    /**
     * 用于设置虚线的偏移量，可搭配 textBorderType 指定 dash array 实现灵活的虚线效果
     */
    public Integer textBorderDashOffset;
    /** 文字本身的阴影颜色 */
    public String textShadowColor;
    /** 文字本身的阴影长度 */
    public Double textShadowBlur;
    /** 文字本身的阴影 X 偏移 */
    public Double textShadowOffsetX;
    /** 文字本身的阴影 Y 偏移 */
    public Double textShadowOffsetY;
    /**  文字超出宽度是否截断或者换行,配置width时有效(ChartConst.Overflow) */
    public String overflow = ChartConst.Overflow.NONE;
    /** 在overflow配置为'truncate'的时候,可以通过该属性配置末尾显示的文本 */
    public String ellipsis = "...";
    /** 可以自定义富文本样式。利用富文本样式，可以在标签中做出非常丰富的效果 */
    public JSONObject richObject;

    public JSONObject toJSONObject(){
        JSONObject jsonObject = super.toJSONObject();
        String textBorderType = jsonObject.getString("textBorderType");
        if(textBorderType != null && textBorderType.trim().startsWith("[")) {
            textBorderType = textBorderType.trim();
            List<Integer> tempBoderTypeList = new ArrayList<Integer>();
            String[] tempBoderTypeArr = textBorderType.substring(1, textBorderType.length() - 1).split(",");
            for (int i = 0; i < tempBoderTypeArr.length; i++) {
                try {
                    tempBoderTypeList.add(Integer.parseInt(tempBoderTypeArr[i].trim()));
                } catch (NumberFormatException e) { }
            }
            jsonObject.put("textBorderType", tempBoderTypeList);
        } else if(textBorderType != null && StringUtil.isInteger(textBorderType)) {
            jsonObject.put("textBorderType", Integer.parseInt(textBorderType.trim()));
        }
        return jsonObject;
    }
}
