package com.wcp.component.chart.common;

/**
 * 图形常量定义
 * @auth AndyLu
 * @date 2023-01-07
 */
public class ChartConst {
    /**
     * 序列显示类型
     */
    public static class SeriesType {
        /** 折线/面积图 */
        public final static String LINE = "line";
        /** 柱状/条形图 */
        public final static String BAR = "bar";
        /** 饼图 */
        public final static String PIE = "pie";
        /** 散点（气泡）图 */
        public final static String SCATTER = "scatter";
        /** 带有涟漪特效动画的散点（气泡） */
        public final static String EFFECTSCATTER = "effectScatter";
        /** 雷达图 */
        public final static String RADAR = "radar";
        /** 树型图 */
        public final static String TREE = "tree";
        /** 树型图 */
        public final static String TREEMAP = "treemap";
        /** 旭日图 */
        public final static String SUNBURST = "sunburst";
        /** 箱形图 */
        public final static String BOXPLOT = "boxplot";
        /** K线图 */
        public final static String CANDLESTICK = "candlestick";
        /** 热力图 */
        public final static String HEATMAP = "heatmap";
        /** 地图 */
        public final static String MAP = "map";
        /** 平行坐标系的系列 */
        public final static String PARALLEL = "parallel";
        /** 线图 */
        public final static String LINES = "lines";
        /** 关系图 */
        public final static String GRAPH = "graph";
        /** 桑基图 */
        public final static String SANKEY = "sankey";
        /** 漏斗图 */
        public final static String FUNNEL = "funnel";
        /** 仪表盘 */
        public final static String GAUGE = "gauge";
        /** 象形柱图 */
        public final static String PICTORIALBAR = "pictorialBar";
        /** 主题河流 */
        public final static String THEMERIVER = "themeRiver";
        /** 自定义系列 */
        public final static String CUSTOM = "custom";
    }
    /**
     * 打开超链接target属性
     */
    public static class OpenLink {
        /** 当前窗口打开 */
        public final static String SELF = "self";
        /** 新窗口打开 */
        public final static String BLANK = "blank";
    }
    /**
     * 字体样式
     */
    public static class FontStyle {
        /** 常规 */
        public final static String NORMAL = "normal";
        /** 斜体 */
        public final static String ITALIC = "italic";
        /** 斜线 */
        public final static String OBLIQUE = "oblique";
    }
    /**
     * 字体粗细
     */
    public static class FontWeight {
        /** 常规 */
        public final static String NORMAL = "normal";
        /** 加粗 */
        public final static String BOLD = "bold";
        /** 加粗黑体 */
        public final static String BOLDER = "bolder";
        /** 变淡 */
        public final static String LIGHTER = "lighter";
    }
    /**
     * 边框类型
     */
    public static class BorderType {
        /** 纯色 */
        public final static String SOLID = "solid";
        /** 虚线 */
        public final static String DASHED = "dashed";
        /** 点缀 */
        public final static String DOTTED = "dotted";
    }
    /**
     * 文字超出宽度是否截断或者换行
     */
    public static class Overflow {
        /** 不设置 */
        public final static String NONE = "none";
        /** 截断，并在末尾显示ellipsis配置的文本，默认为... */
        public final static String TRUNCATE = "truncate";
        /** 换行 */
        public final static String BREAK = "break";
        /** 换行，跟'break'不同的是，在英语等拉丁文中，'breakAll'还会强制单词内换行 */
        public final static String BREAKALL = "breakAll";
    }
    /**
     * 水平对齐定义
     */
    public static class HoriAlign {
        /** 自动 */
        public final static String AUTO = "auto";
        /** 靠左 */
        public final static String LEFT = "left";
        /** 居中 */
        public final static String CENTER = "center";
        /** 靠左 */
        public final static String RIGHT = "right";
    }
    /**
     * 垂直对齐定义
     */
    public static class VertAlign {
        /** 自动 */
        public final static String AUTO = "auto";
        /** 靠上 */
        public final static String TOP = "top";
        /** 居中 */
        public final static String MIDDLE = "middle";
        /** 靠下 */
        public final static String BOTTOM = "bottom";
    }
}
