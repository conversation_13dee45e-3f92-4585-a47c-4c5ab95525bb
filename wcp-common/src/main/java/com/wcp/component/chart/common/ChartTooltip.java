package com.wcp.component.chart.common;

import com.alibaba.fastjson.JSONObject;
import com.wcp.component.common.AComponent;
import com.wcp.utils.StringUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 本坐标系特定的 tooltip 设定
 * 提示框组件可以设置在多种地方：
 * 可以设置在全局，即 tooltip
 * 可以设置在坐标系中，即 grid.tooltip、polar.tooltip、single.tooltip
 * 可以设置在系列中，即 series.tooltip
 * 可以设置在系列的每个数据项中，即 series.data.tooltip
 * @auth AndyLu
 * @date 2023-01-08
 */
@Data
public class ChartTooltip extends AComponent {
    /** 是否显示提示框组件。包括提示框浮层和 axisPointer。*/
    public Boolean show = true;
    /**
     * 触发类型，可选：
     * 'item'：数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。
     * 'axis'：坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
     * 'none'：什么都不触发。
     * */
    public String trigger = "axis";
    /** 坐标轴指示器是指示坐标轴当前刻度的工具。*/
    public JSONObject axisPointer;
    /**
     *  绝对位置，相对于容器左侧 10px, 上侧 10 px(position: [10, 10])
     *  相对位置，放置在容器正中间(position: ['50%', '50%'])
     * */
    public String position;
    /**
     * 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
     * 示例：formatter: '{b0}: {c0}<br />{b1}: {c1}'
     */
    public String formatter;
    /** tooltip 中数值显示部分的格式化回调函数。*/
    public String valueFormatter;
    /** 提示框浮层的背景颜色。*/
    public String backgroundColor;
    /** 提示框浮层的边框颜色。*/
    public String borderColor;
    /** 提示框浮层的边框宽。*/
    public Double borderWidth;
    /**
     * 标题内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距
     * 设置内边距为 5(padding: 5)
     * 设置上下的内边距为 5，左右的内边距为 10(padding: [5, 10])
     * 分别设置四个方向的内边距 (padding: [
     *     5,  // 上
     *     10, // 右
     *     5,  // 下
     *     10, // 左
     * ])
     */
    public String padding = "5";
    /** 提示框浮层的文本样式 */
    public ChartTextStyle textStyle;
    /**
     * 额外附加到浮层的 css 样式。如下为浮层添加阴影的示例
     * 示例：extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);'
     * */
    public String extraCssText;

    public JSONObject toJSONObject() {
        JSONObject jsonObject = super.toJSONObject();
        String padding = jsonObject.getString("padding");
        if (padding != null && padding.trim().startsWith("[")) {
            padding = padding.trim();
            List<Integer> paddingList = new ArrayList<Integer>();
            String[] paddingListArr = padding.substring(1, padding.length() - 1).split(",");
            for (int i = 0; i < paddingListArr.length; i++) {
                try {
                    paddingList.add(Integer.parseInt(paddingListArr[i].trim()));
                } catch (NumberFormatException e) {
                }
            }
            jsonObject.put("padding", paddingList);
        } else if (padding != null && StringUtil.isInteger(padding)) {
            jsonObject.put("padding", Integer.parseInt(padding.trim()));
        }
        return jsonObject;
    }
}
