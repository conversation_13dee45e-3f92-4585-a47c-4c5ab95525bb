package com.wcp.component.chart.common;

import com.alibaba.fastjson.JSONObject;

import com.wcp.component.common.AComponent;
import com.wcp.utils.StringUtil;
import lombok.Data;

@Data
public class ChartGrid extends AComponent {
    /** 组件 ID。默认不指定。指定则可用于在 option 或者 API 中引用组件 */
    public String id;
    /** 是否显示直角坐标系网格 */
    public Boolean show;
    /** 所有图形的 zlevel 值，zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面 */
    public Integer zlevel;
    /**
     * 组件的所有图形的z值。控制图形的前后顺序。z值小的图形会被z值大的图形覆盖
     * z相比zlevel优先级更低，而且不会创建新的 Canvas
     * */
    public Integer z = 2;
    /**
     * title 组件离容器左侧的距离(ChartConst.HoriAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'left', 'center', 'right'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String left = "50";
    /**
     * title 组件离容器上侧的距离(ChartConst.VertAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'top', 'middle', 'bottom'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String top = "50";
    /**
     * title 组件离容器右侧的距离(ChartConst.HoriAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'left', 'center', 'right'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String right = "50";
    /**
     * title 组件离容器下侧的距离(ChartConst.VertAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'top', 'middle', 'bottom'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String bottom = "50";
    /** 各内部图形的水平距离，不设置采用默认值 */
    public Integer horiSpacing= 50;
    /** 各内部图形的垂直距离，不设置采用默认值 */
    public Integer vertiSpacing= 50;
    /** grid 组件的宽度。默认自适应 'auto'，可以是百分比，也可以是具体的数字 */
    public String width;
    /** grid 组件的高度。默认自适应 'auto'，可以是百分比，也可以是具体的数字 */
    public String height;
    /**
     * grid 区域是否包含坐标轴的刻度标签。
     * containLabel 为 false 的时候：
     * grid.left grid.right grid.top grid.bottom grid.width grid.height 决定的是由坐标轴形成的矩形的尺寸和位置。
     * 这比较适用于多个 grid 进行对齐的场景，因为往往多个 grid 对齐的时候，是依据坐标轴来对齐的。
     * containLabel 为 true 的时候：
     * grid.left grid.right grid.top grid.bottom grid.width grid.height 决定的是包括了坐标轴标签在内的所有内容所形成的矩形的位置。
     * 这常用于『防止标签溢出』的场景，标签溢出指的是，标签长度动态变化时，可能会溢出容器或者覆盖其他组件。
     * */
    public Boolean containLabel;
    /** 网格背景色，默认透明，注意：此配置项生效的前提是，设置了 show: true */
    public String backgroundColor;
    /** 网格的边框颜色。支持的颜色格式同 backgroundColor，注意：此配置项生效的前提是，设置了 show: true */
    public String borderColor;
    /** 网格的边框线宽，注意：此配置项生效的前提是，设置了 show: true */
    public Double borderWidth;
    /** 图形阴影的模糊大小。该属性配合 shadowColor,shadowOffsetX, shadowOffsetY 一起设置图形的阴影效果。注意：此配置项生效的前提是，设置了 show: true */
    public Double shadowBlur;
    /** 阴影颜色。支持的格式同color，注意：此配置项生效的前提是，设置了 show: true */
    public String shadowColor;
    /** 阴影水平方向上的偏移距离，注意：此配置项生效的前提是，设置了 show: true */
    public Double shadowOffsetX;
    /** 阴影垂直方向上的偏移距离，注意：此配置项生效的前提是，设置了 show: true */
    public Double shadowOffsetY;
    /** 本坐标系特定的 tooltip 设定 */
    public ChartTooltip tooltip;

    public Integer range = 1;

    public Integer maxNum = 2;

    public JSONObject toJSONObject(){
        JSONObject jsonObject = super.toJSONObject();
        String left = jsonObject.getString("left");
        if(left != null && StringUtil.isInteger(left)) {
            jsonObject.put("left", Integer.parseInt(left.trim()));
        }
        String top = jsonObject.getString("top");
        if(top != null && StringUtil.isInteger(top)) {
            jsonObject.put("top", Integer.parseInt(top.trim()));
        }
        String right = jsonObject.getString("right");
        if(right != null && StringUtil.isInteger(right)) {
            jsonObject.put("right", Integer.parseInt(right.trim()));
        }
        String bottom = jsonObject.getString("bottom");
        if(bottom != null && StringUtil.isInteger(bottom)) {
            jsonObject.put("bottom", Integer.parseInt(bottom.trim()));
        }
        String width = jsonObject.getString("width");
        if(width != null && StringUtil.isInteger(width)) {
            jsonObject.put("width", Integer.parseInt(width.trim()));
        }
        String height = jsonObject.getString("height");
        if(height != null && StringUtil.isInteger(height)) {
            jsonObject.put("height", Integer.parseInt(height.trim()));
        }
        return jsonObject;
    }
}
