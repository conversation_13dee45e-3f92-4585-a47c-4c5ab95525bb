package com.wcp.component.chart.common;

import lombok.Data;

@Data
public class ChartLegend {

    /**
     * 图例的类型。
     */
    public String type;

    /**
     * 组件 ID。默认不指定。指定则可用于在 option 或者 API 中引用组件。
     */
    public String id;

    public Boolean show = true;

    /**
     * 所有图形的 zlevel 值。
     */
    public String zlevel;

    /**
     * 组件的所有图形的z值。控制图形的前后顺序。z值小的图形会被z值大的图形覆盖。
     *
     * z相比zlevel优先级更低，而且不会创建新的 Canvas。
     */
    public Integer z = 2;

    /**
     * 图例组件离容器左侧的距离
     */
    public String left = "auto";

    /**
     * 图例组件离容器上侧的距离
     */
    public String top = "auto";

    /**
     * 图例组件离容器右侧的距离。
     */
    public String right = "auto";

    /**
     * 图例组件离容器下侧的距离
     */
    public String bottom = "auto";

    /**
     * 图例组件的宽度。默认自适应。
     */
    public String  width = "auto";

    /**
     * 图例列表的布局朝向。
     */
    public String  height = "auto";

    /**
     * 图例标记和文本的对齐。默认自动，根据组件的位置和 orient 决定，当组件的 left 值为 'right' 以及纵向布局（orient 为 'vertical'）的时候为右对齐，即为 'right'
     */
    public String  orient = "horizontal";

    /**
     * 图例内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距。
     */
    public Integer  padding = 5;

    /**
     *图例每项之间的间隔。横向布局时为水平间隔，纵向布局时为纵向间隔
     */
    public Integer itemGap =10;

    /**
     * 图例标记的图形宽度。
     */
    public Integer itemWidth = 25;

    /**
     * 图例标记的图形高度
     */
    public Integer itemHeight = 14;

    /**
     * 图例的图形样式。其属性的取值为 'inherit' 时，表示继承系列中的属性值。
     */
    public String  itemStyle;

    /**
     * 图例图形中线的样式，用于诸如折线图图例横线的样式设置。其属性的取值为 'inherit' 时，表示继承系列中的属性值
     */
    public String  lineStyle;

    /**
     * 图形旋转角度，类型为 number | 'inherit'。如果为 'inherit'，表示取系列的 symbolRotate。
     */
    public String symbolRotate;

    /**
     * 用来格式化图例文本，支持字符串模板和回调函数两种形式。
     */
    public String formatter;

    /**
     * 图例选择的模式，控制是否可以通过点击图例改变系列的显示状态。默认开启图例选择，可以设成 false 关闭。
     *
     * 除此之外也可以设成 'single' 或者 'multiple' 使用单选或者多选模式。
     */
    public Boolean selectedMode = true;

    /**
     * 图例关闭时的颜色。
     */
    public String  inactiveColor = "#ccc";

    /**
     * 图例关闭时的描边颜色
     */
    public String inactiveBorderColor = "#ccc";

    /**
     *图例关闭时的描边粗细。如果为 'auto' 表示：如果系列存在描边，则取 2，如果系列不存在描边，则取 0。如果为 'inherit' 则表示：始终取系列的描边粗细。。
     */
    public String inactiveBorderWidth = "auto";

}
