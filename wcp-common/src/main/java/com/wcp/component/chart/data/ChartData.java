package com.wcp.component.chart.data;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 前端绑定数据集
 */
@Data
public class ChartData {

    /**
     * 数据集合对应的key
     */
    private String key;

    /**
     * x 轴对应的数据集
     */
    private Double[] x;

    /**
     * 对应应数据值
     */
    private Double[] values;

    public ChartData() {

    }

    public ChartData(String key, Double[] x, Double[] values) {
        this.key = key;
        this.x = x;
        this.values = values;
    }
}
