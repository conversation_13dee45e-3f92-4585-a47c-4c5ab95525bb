package com.wcp.component.chart.common;

import com.alibaba.fastjson.JSONObject;
import com.wcp.component.common.AComponent;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *前端直角坐标系 grid 中的 y 轴选项
 */
@Data
@NoArgsConstructor
public class ChartyAxisOption extends AComponent {

    private JSONObject axisTick;

    private JSONObject axisLine;

    private String name;

    private Boolean inverse;

    private Double max;

    private Double min;

    private String nameLocation;

    public ChartyAxisOption(Boolean axisTick_show, Boolean axisLine_show,Boolean axisLine_onZero,String name , Boolean inverse, Double max, Double min, String nameLocation) {
        JSONObject axisTick = new JSONObject();
        JSONObject axisLine = new JSONObject();
        if (axisTick_show != null) axisTick.put("show",axisTick_show);
        if (axisLine_show != null) axisLine.put("show",axisLine_show);
        if (axisLine_onZero != null) axisLine.put("onZero",axisLine_onZero);
        if (max != null) this.max = Double.parseDouble(String.format("%.2f",max));
        if (min != null) this.min = Double.parseDouble(String.format("%.2f",min));
        this.axisTick = axisTick;
        this.axisLine = axisLine;
        this.name = name;
        this.inverse = inverse;
        this.nameLocation = nameLocation;
    }
}
