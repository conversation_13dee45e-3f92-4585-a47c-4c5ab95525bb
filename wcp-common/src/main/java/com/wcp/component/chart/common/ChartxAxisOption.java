package com.wcp.component.chart.common;

import com.alibaba.fastjson.JSONObject;
import com.wcp.component.common.AComponent;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *前端直角坐标系 grid 中的 x 轴选项
 */
@Data
@NoArgsConstructor
public class ChartxAxisOption extends AComponent {

    private JSONObject axisLabel;

    private JSONObject axisLine;

    private String name;

    private JSONObject nameTextStyle;

    private JSONObject splitLine;

    public ChartxAxisOption(Boolean axisLabel_show, String axisLine_color, Boolean onZero, String name) {
        JSONObject axisLabel = new JSONObject();
        JSONObject axisLine = new JSONObject();
        if (axisLabel_show != null) axisLabel.put("show",axisLabel_show);
        if (axisLine_color != null) axisLabel.put("color",axisLine_color);
        if (onZero != null) axisLine.put("onZero",onZero);
        this.axisLabel = axisLabel;
        this.axisLine = axisLine;
        this.name = name;
    }
}
