package com.wcp.component.chart.common;

import com.wcp.component.common.AComponent;
import lombok.Data;

/**
 * 前端直角坐标系 grid 中的 x 轴，一般情况下单个 grid 组件最多只能放上下两个 x 轴，多于两个 x 轴需要通过配置 offset 属性防止同个位置多个 x 轴的重叠。
 */
@Data
public class ChartxAxis extends AComponent {

    /**
     * 坐标轴类型
     * category:类目轴，适用于离散的类目数据。为该类型时类目数据可自动从 series.data 或 dataset.source 中取，或者可通过 xAxis.data 设置类目数据。
     */
    private String type = "category";

    /**
     * 类目数据，在类目轴（type: 'category'）中有效。
     *
     * 如果没有设置 type，但是设置了 axis.data，则认为 type 是 'category'。
     *
     * 如果设置了 type 是 'category'，但没有设置 axis.data，则 axis.data 的内容会自动从 series.data 中获取，这会比较方便。不过注意，axis.data 指明的是 'category' 轴的取值范围。如果不指定而是从 series.data 中获取，那么只能获取到 series.data 中出现的值。比如说，假如 series.data 为空时，就什么也获取不到
     */
    private Object[] data = new String[]{"Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"};

}
