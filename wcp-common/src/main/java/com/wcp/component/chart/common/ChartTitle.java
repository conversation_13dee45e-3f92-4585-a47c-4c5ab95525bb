package com.wcp.component.chart.common;

import com.alibaba.fastjson.JSONObject;
import com.wcp.component.common.AComponent;
import com.wcp.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

public class ChartTitle extends AComponent {
    /** 组件 ID。默认不指定。指定则可用于在 option 或者 API 中引用组件 */
    public String title;
    /** 是否显示标题组件 */
    public Boolean show = true;
    /** 主标题文本，支持使用 \n 换行 */
    public String text;
    /** 主标题文本超链接 */
    public String link;
    /** 指定窗口打开主标题超链接(ChartConst.OpenLink) */
    public String target;
    /** 文本样式 */
    public ChartTextStyle textStyle;
    /** 副标题文本，支持使用 \n 换行 */
    public String subtext;
    /** 副标题文本超链接 */
    public String sublink;
    /** 指定窗口打开副标题超链接(ChartConst.OpenLink) */
    public String subtarget;
    /** 副标题文本样式 */
    public ChartSubTextStyle subtextStyle;
    /** 整体（包括 text 和 subtext）的水平对齐(ChartConst.HoriAlign) */
    public String textAlign = ChartConst.HoriAlign.AUTO;
    /** 整体（包括 text 和 subtext）的垂直对齐(ChartConst.HoriAlign) */
    public String textVerticalAlign = ChartConst.VertAlign.AUTO;
    /** 是否触发事件 */
    public Boolean triggerEvent;
    /**
     * 标题内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距
     * 设置内边距为 5(padding: 5)
     * 设置上下的内边距为 5，左右的内边距为 10(padding: [5, 10])
     * 分别设置四个方向的内边距 (padding: [
     *     5,  // 上
     *     10, // 右
     *     5,  // 下
     *     10, // 左
     * ])
     */
    public String padding = "5";
    /** 主副标题之间的间距 */
    public Integer itemGap = 10;
    /** 所有图形的 zlevel 值, zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面 */
    public Integer zlevel;
    /** 组件的所有图形的z值。控制图形的前后顺序。z值小的图形会被z值大的图形覆盖，z相比zlevel优先级更低，而且不会创建新的 Canvas */
    public Integer z;
    /**
     * title 组件离容器左侧的距离(ChartConst.HoriAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'left', 'center', 'right'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String left = ChartConst.HoriAlign.AUTO;
    /**
     * title 组件离容器上侧的距离(ChartConst.VertAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'top', 'middle', 'bottom'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String top = ChartConst.VertAlign.AUTO;
    /**
     * title 组件离容器右侧的距离(ChartConst.HoriAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'left', 'center', 'right'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String right = ChartConst.HoriAlign.AUTO;
    /**
     * title 组件离容器下侧的距离(ChartConst.VertAlign)
     * 值可以是像 20 这样的具体像素值，可以是像 '20%' 这样相对于容器高宽的百分比，也可以是 'top', 'middle', 'bottom'
     * 如果 left 的值为'left', 'center', 'right'，组件会根据相应的位置自动对齐
     */
    public String bottom = ChartConst.VertAlign.AUTO;
    /** 标题背景色，默认透明'transparent' */
    public String backgroundColor;
    /** 标题的边框颜色。支持的颜色格式同 backgroundColor */
    public String borderColor;
    /** 标题的边框线宽 */
    public Double borderWidth;
    /**
     * 圆角半径，单位px，支持传入数组分别指定 4 个圆角半径
     * borderRadius: 5, // 统一设置四个角的圆角大小
     * borderRadius: [5, 5, 0, 0] //（顺时针左上，右上，右下，左下）
     */
    public String borderRadius;
    /**
     * 图形阴影的模糊大小。该属性配合 shadowColor,shadowOffsetX, shadowOffsetY 一起设置图形的阴影效果
     * 注意：此配置项生效的前提是，设置了 show: true 以及值不为 tranparent 的背景色 backgroundColor。
     */
    public Double shadowBlur;
    /** 阴影颜色。支持的格式同color，注意：此配置项生效的前提是，设置了 show=true */
    public String shadowColor;
    /** 阴影水平方向上的偏移距离，注意：此配置项生效的前提是，设置了 show=true */
    public Double shadowOffsetX;
    /** 阴影垂直方向上的偏移距离，注意：此配置项生效的前提是，设置了 show=true */
    public Double shadowOffsetY;

    public JSONObject toJSONObject(){
        JSONObject jsonObject = super.toJSONObject();
        String padding = jsonObject.getString("padding");
        if(padding != null && padding.trim().startsWith("[")) {
            padding = padding.trim();
            List<Integer> paddingList = new ArrayList<Integer>();
            String[] paddingListArr = padding.substring(1, padding.length() - 1).split(",");
            for (int i = 0; i < paddingListArr.length; i++) {
                try {
                    paddingList.add(Integer.parseInt(paddingListArr[i].trim()));
                } catch (NumberFormatException e) { }
            }
            jsonObject.put("padding", paddingList);
        } else if(padding != null && StringUtil.isInteger(padding)) {
            jsonObject.put("padding", Integer.parseInt(padding.trim()));
        }
        String left = jsonObject.getString("left");
        if(left != null && StringUtil.isInteger(left)) {
            jsonObject.put("left", Integer.parseInt(left.trim()));
        }
        String top = jsonObject.getString("top");
        if(top != null && StringUtil.isInteger(top)) {
            jsonObject.put("top", Integer.parseInt(top.trim()));
        }
        String right = jsonObject.getString("right");
        if(right != null && StringUtil.isInteger(right)) {
            jsonObject.put("right", Integer.parseInt(right.trim()));
        }
        String bottom = jsonObject.getString("bottom");
        if(bottom != null && StringUtil.isInteger(bottom)) {
            jsonObject.put("bottom", Integer.parseInt(bottom.trim()));
        }
        String borderRadius = jsonObject.getString("borderRadius");
        if(borderRadius != null && borderRadius.trim().startsWith("[")) {
            borderRadius = borderRadius.trim();
            List<Integer> borderRadiusList = new ArrayList<Integer>();
            String[] borderRadiusArr = borderRadius.substring(1, borderRadius.length() - 1).split(",");
            for (int i = 0; i < borderRadiusArr.length; i++) {
                try {
                    borderRadiusList.add(Integer.parseInt(borderRadiusArr[i].trim()));
                } catch (NumberFormatException e) { }
            }
            jsonObject.put("borderRadius", borderRadiusList);
        } else if(borderRadius != null && StringUtil.isInteger(borderRadius)) {
            jsonObject.put("borderRadius", Integer.parseInt(borderRadius.trim()));
        }
        return jsonObject;
    }
}
