package com.wcp.component.table.apptable;


import com.wcp.component.common.AComponent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppTableCpt extends AComponent {
    /** 表格数据 */
    private List<Map<String, Object>> data;
    /** 表格显示的列 */
    private List<String> mainProps;

    /**
     * 往表格里面添加数据，如果行号小于等于行数，则能添加进去，否则添加不进去
     * @param row      行号
     * @param key      数据标志
     * @param value    星期
     * @return
     */
    public AppTableCpt addData(int row, String key, Object value) {
        if(data == null) {
            data = new ArrayList<>();
        }
        if(data.size() < row) {
            return this;
        } else if(data.size() == row) {
            data.add(new HashMap<>());
        }
        Map<String, Object> tempMap = data.get(row);
        tempMap.put(key, value);
        return this;
    }
    public AppTableCpt addShowItem(String key) {
        if(mainProps == null) {
            mainProps = new ArrayList<>();
        }
        mainProps.add(key);
        return this;
    }
}
