package com.wcp.component.common;

import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 前端组件工具类
 * @auth AndyLu
 * @date 2023-01-07
 */
public class ComponentUtil {
    /**
     * 把组件属性定义实例转换成json对象
     * @param component
     * @return
     * @throws IllegalAccessException
     */
    public static JSONObject transInstanceToJson(AComponent component) {
        JSONObject jsonObject = new JSONObject();
        Class<?> clazz = component.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            field.setAccessible(true);
            Object fieldValue = null;
            try {
                fieldValue = field.get(component);
            } catch (IllegalAccessException e) { }
            // 如果为空则不转换
            if(fieldValue == null) continue;
            // 如果是集合，则迭代转换，因为前端所有的集合都是列表
            if(fieldValue instanceof Collection) {
                List<Object> tempList = new ArrayList<Object>();
                Collection collection = ((Collection)fieldValue);
                if(collection.size() == 0) continue;
                Iterator<?> iterator = collection.iterator();
                while(iterator.hasNext()) {
                    Object item = iterator.next();
                    if(item instanceof AComponent) {
                        item = transInstanceToJson((AComponent)item);
                    }
                    tempList.add(item);
                }
                jsonObject.put(field.getName(), tempList);
            } else if(fieldValue.getClass().isArray()){
            // 如果是数组，则循环转换，因为前端所有的集合都是列表
                List<Object> tempList = new ArrayList<Object>();
                int length = Array.getLength(fieldValue);
                for (int j = 0; j < length; j++) {
                    Object item = Array.get(fieldValue, j);
                    if(item instanceof AComponent) {
                        item = transInstanceToJson((AComponent)item);
                    }
                    tempList.add(item);
                }
                jsonObject.put(field.getName(), tempList);
            } else if(fieldValue instanceof Map) {
            // 如果Map,则直接迭代转换
                JSONObject tempObject = new JSONObject();
                Map map = ((Map)fieldValue);
                if(map.size() == 0) continue;
                Iterator<Map.Entry<String, ?>> iterator = map.entrySet().iterator();
                while(iterator.hasNext()) {
                    Map.Entry<String, ?> entry = iterator.next();
                    String key = entry.getKey();
                    Object item = entry.getValue();
                    if(item instanceof AComponent) {
                        item = transInstanceToJson((AComponent)item);
                    }
                    tempObject.put(key, item);
                }
                jsonObject.put(field.getName(), tempObject);
            } else { // 其它属性，直接转换
                jsonObject.put(field.getName(), fieldValue);
            }
        }
        return jsonObject;
    }
}
