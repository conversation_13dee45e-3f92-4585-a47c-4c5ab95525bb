package com.wcp.utils;

import org.apache.log4j.Logger;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 此类为快速反射工具
 *
 * <AUTHOR>
 * @date 2018.12.20
 */
public class FastReflec {
    private static final Logger log = Logger.getLogger(FastReflec.class);
    /**
     * 类字节码缓存
     */
    private static Map<String, Class<?>> classMap = new HashMap<>();
    /**
     * 构造方法节节码缓存
     */
    private static Map<String, Constructor<?>> constructorMap = new HashMap<>();
    /**
     * 函数方法节节码缓存
     */
    private static Map<String, Method> methodMap = new HashMap<>();

    /**
     * 返回得到类字节码
     *
     * @param className 类名称
     * @return
     * @throws ClassNotFoundException
     */
    public static Class<?> forName(String className) throws ClassNotFoundException {
        Class<?> clazz = classMap.get(className);
        if (clazz == null) {
            clazz = Class.forName(className);
            synchronized (classMap) {
                classMap.put(className, clazz);
            }
        }
        return clazz;
    }

    /**
     * 注册类字节码到
     *
     * @param clazz 类字节码
     * @return
     * @throws ClassNotFoundException
     */
    public static void registerClass(Class<?> clazz) {
        if (clazz == null) return;
        synchronized (classMap) {
            classMap.put(clazz.getName(), clazz);
        }
    }

    /**
     * 返回给定参数类型的构造方法
     *
     * @param clazz          类字节码
     * @param parameterTypes 参数类字节码数组
     * @return
     * @throws NoSuchMethodException
     */
    public static Constructor<?> getConstructor(Class<?> clazz, Class<?>... parameterTypes) throws NoSuchMethodException {
        if (clazz == null) return null;
        String key = clazz.getName();
        for (int i = 0; i < parameterTypes.length; i++) {
            key += ";" + parameterTypes[i].getName();
        }
        Constructor<?> constructor = constructorMap.get(key);
        if (constructor != null) return constructor;
        //因为以下循环会查找所有的有构造方法，因此如果参数为零的构造方法可以直接获取，加快查找速度
        if (parameterTypes.length == 0) {
            constructor = clazz.getConstructor();
            if (constructor != null) {
                synchronized (constructorMap) {
                    constructorMap.put(key, constructor);
                }
                return constructor;
            } else {
                return null;
            }
        }
        //查找所有的构造方法，因为构造方法会会有继承的关系，所有需要循环查找，第一次查找效率较低
        List<Constructor<?>> hasFindContructors = new ArrayList<>();
        List<Class<?>[]> tempParamClass = new ArrayList<>();
        Constructor<?>[] constructors = clazz.getConstructors();
        for (int i = 0; i < constructors.length; i++) {
            Constructor<?> tempConstructor = constructors[i];
            Class<?>[] tempParamTypes = tempConstructor.getParameterTypes();
            //需要查找的构造方法与所给定的构造方法参数个数相同才能比较
            if (tempParamTypes.length == parameterTypes.length) {
                Class<?>[] clazzArr = new Class[tempParamTypes.length];
                //依次对比构造方法的参数，查看所给参数是否与构造方法的参数相同或是其子类
                boolean isMatch = true;
                for (int j = 0; j < tempParamTypes.length; j++) {
                    Class<?> baseClass = tempParamTypes[j];
                    Class<?> compareClass = parameterTypes[j];
                    //如果构造函数是基本类型，需要做基本类型的转换
                    //从外面转进来的不可能是基本类型，因为用getClass获取的类型会自动进行封箱
                    //函数构造方法可能会是基本类型，需要把构造方法的基本类型转换成封箱类型再进行比较
                    if (compareClass.isPrimitive()) {
                        compareClass = baseClassConvert(compareClass);
                    }
                    if (baseClass.isPrimitive()) {
                        baseClass = baseClassConvert(baseClass);
                    }
                    if (!baseClass.isAssignableFrom(compareClass)) {
                        isMatch = false;
                        break;
                    }
                    clazzArr[j] = tempParamTypes[j];
                }
                //如果匹配则返回构建方法
                if (isMatch) {
                    hasFindContructors.add(tempConstructor);
                    tempParamClass.add(clazzArr);
                }
            }
        }
        if (hasFindContructors.size() == 0) {
            return null;
        } else {
            int[][] tempCount = new int[hasFindContructors.size()][parameterTypes.length];
            for (int i = 0; i < tempParamClass.size(); i++) {
                Class<?>[] tempParamTypes1 = tempParamClass.get(i);
                for (int j = 0; j < tempParamClass.size(); j++) {
                    if (i == j) continue;
                    Class<?>[] tempParamTypes2 = tempParamClass.get(j);
                    for (int k = 0; k < tempParamTypes1.length; k++) {
                        if (!tempParamTypes1[k].equals(tempParamTypes2[k]) && tempParamTypes1[k].isAssignableFrom(tempParamTypes2[k])) {
                            tempCount[i][k] = 1;
                        }
                    }
                }
            }
            //都是底层类才能说明已经找到
            int findIdx = -1;
            for (int i = 0; i < tempCount.length; i++) {
                int total = 0;
                for (int j = 0; j < parameterTypes.length; j++) {
                    total += tempCount[i][j];
                }
                if (total == 0) {
                    findIdx = i;
                    break;
                }
            }
            if (findIdx > -1) {
                constructor = hasFindContructors.get(findIdx);
                synchronized (constructorMap) {
                    constructorMap.put(key, constructor);
                }
                return constructor;
            } else {
                log.error(key + "调用构造方法的参数是模棱两可的!");
            }
        }
        return null;
    }

    /**
     * 返回给定参数类型的函数方法
     *
     * @param clazz          类字节码
     * @param methodName     函数方法名称
     * @param parameterTypes 参数类字节码数组
     * @return
     * @throws NoSuchMethodException
     */
    public static Method getMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) throws NoSuchMethodException {
        if (clazz == null) return null;
        String key = clazz.getName() + "," + methodName;
        for (int i = 0; i < parameterTypes.length; i++) {
            key += ";" + parameterTypes[i].getName();
        }
        Method method = methodMap.get(key);
        if (method != null) return method;
        //因为以下循环会查找所有的有函数方法，因此如果参数为零的函数方法可以直接获取，加快查找速度
        if (parameterTypes.length == 0) {
            method = clazz.getMethod(methodName);
            if (method != null) {
                synchronized (methodMap) {
                    methodMap.put(key, method);
                }
                return method;
            } else {
                return null;
            }
        }
        //查找所有的函数方法，因为函数方法会会有继承的关系，所有需要循环查找，第一次查找效率较低
        List<Method> hasFindMethods = new ArrayList<>();
        List<Class<?>[]> tempParamClass = new ArrayList<>();
        Method[] methods = clazz.getMethods();
        for (int i = 0; i < methods.length; i++) {
            Method tempMethod = methods[i];
            if (!tempMethod.getName().equals(methodName)) {
                continue;
            }
            Class<?>[] tempParamTypes = tempMethod.getParameterTypes();
            //需要查找的函数方法与所给定的函数方法参数个数相同才能比较
            if (tempParamTypes.length == parameterTypes.length) {
                Class<?>[] clazzArr = new Class[tempParamTypes.length];
                //依次对比函数方法的参数，查看所给参数是否与函数方法的参数相同或是其子类
                boolean isMatch = true;
                for (int j = 0; j < tempParamTypes.length; j++) {
                    Class<?> baseClass = tempParamTypes[j];
                    Class<?> compareClass = parameterTypes[j];
                    //如果函数函数是基本类型，需要做基本类型的转换
                    //从外面转进来的不可能是基本类型，因为用getClass获取的类型会自动进行封箱
                    //函数函数方法可能会是基本类型，需要把函数方法的基本类型转换成封箱类型再进行比较
                    if (compareClass.isPrimitive()) {
                        compareClass = baseClassConvert(compareClass);
                    }
                    if (baseClass.isPrimitive()) {
                        baseClass = baseClassConvert(baseClass);
                    }
                    if (!baseClass.isAssignableFrom(compareClass)) {
                        isMatch = false;
                        break;
                    }
                    clazzArr[j] = tempParamTypes[j];
                }
                //如果匹配则返回构建方法
                if (isMatch) {
                    hasFindMethods.add(tempMethod);
                    tempParamClass.add(clazzArr);
                }
            }
        }
        if (hasFindMethods.size() == 0) {
            return null;
        } else {
            int[][] tempCount = new int[hasFindMethods.size()][parameterTypes.length];
            for (int i = 0; i < tempParamClass.size(); i++) {
                Class<?>[] tempParamTypes1 = tempParamClass.get(i);
                for (int j = 0; j < tempParamClass.size(); j++) {
                    if (i == j) continue;
                    Class<?>[] tempParamTypes2 = tempParamClass.get(j);
                    for (int k = 0; k < tempParamTypes1.length; k++) {
                        if (!tempParamTypes1[k].equals(tempParamTypes2[k]) && tempParamTypes1[k].isAssignableFrom(tempParamTypes2[k])) {
                            tempCount[i][k] = 1;
                        }
                    }
                }
            }
            //都是底层类才能说明已经找到
            int findIdx = -1;
            for (int i = 0; i < tempCount.length; i++) {
                int total = 0;
                for (int j = 0; j < parameterTypes.length; j++) {
                    total += tempCount[i][j];
                }
                if (total == 0) {
                    findIdx = i;
                    break;
                }
            }
            if (findIdx > -1) {
                method = hasFindMethods.get(findIdx);
                synchronized (methodMap) {
                    methodMap.put(key, method);
                }
                return method;
            } else {
                log.error(key + "调用函数方法的参数是模棱两可的!");
            }
        }
        return null;
    }

    /**
     * 返回给定字段名称的字段
     *
     * @param clazz     类字节码
     * @param fieldName 字段名称
     * @return
     * @throws NoSuchFieldException
     */
    public static Field getField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        if (clazz == null) return null;
        return clazz.getField(fieldName);
    }

    /**
     * 返回给定字段名称的字段
     *
     * @param clazz     类字节码
     * @param fieldName 字段名称
     * @return
     * @throws NoSuchFieldException
     */
    public static Field getDeclaredField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        if (clazz == null) return null;
        return clazz.getDeclaredField(fieldName);
    }

    private static Class<?> baseClassConvert(Class<?> clazz) {
        if (clazz.equals(Boolean.TYPE)) {
            return Boolean.class;
        } else if (clazz.equals(Character.TYPE)) {
            return Character.class;
        } else if (clazz.equals(Byte.TYPE)) {
            return Byte.class;
        } else if (clazz.equals(Short.TYPE)) {
            return Short.class;
        } else if (clazz.equals(Integer.TYPE)) {
            return Integer.class;
        } else if (clazz.equals(Long.TYPE)) {
            return Long.class;
        } else if (clazz.equals(Float.TYPE)) {
            return Float.class;
        } else if (clazz.equals(Double.TYPE)) {
            return Double.class;
        } else if (clazz.equals(Void.TYPE)) {
            return Void.class;
        }
        return Void.class;
    }
}
