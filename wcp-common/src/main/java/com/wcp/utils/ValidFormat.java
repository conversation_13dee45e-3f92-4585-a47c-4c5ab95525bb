package com.wcp.utils;

import java.text.DecimalFormat;
import java.text.FieldPosition;
import java.text.NumberFormat;
import java.text.ParsePosition;

/**
 * 有效位数格式化
 *
 * <AUTHOR>
 * @date 2017-9-11
 */
public class ValidFormat extends NumberFormat {
    private static final long serialVersionUID = 1L;

    /**
     * -1 表示不设置有效位
     */
    private int validLength = -1;
    /**
     * 是否四舍五入
     */
    private boolean roundAbled = true;

    /**
     * 是否四舍五入
     */
    public boolean isRoundAbled() {
        return roundAbled;
    }

    /**
     * 是否四舍五入
     */
    public void setRoundAbled(boolean roundAbled) {
        this.roundAbled = roundAbled;
    }

    /**
     * 获取有效位数
     *
     * @return
     */
    public int getValidLength() {
        return validLength;
    }

    /**
     * 设置有效位数
     *
     * @param validLength
     */
    public void setValidLength(int validLength) {
        this.validLength = validLength;
    }

    public ValidFormat() {
        super();
    }

    public ValidFormat(int validLength) {
        super();
        this.validLength = validLength;
    }

    @Override
    public StringBuffer format(double number, StringBuffer result,
                               FieldPosition fieldPosition) {
        DecimalFormat decimalFormat = new DecimalFormat();
        String tempString = decimalFormat.format(number, result, fieldPosition).toString();
        String str = tempString;
        //格式化出来的字符串可能含有,号
        if (tempString.indexOf(",") >= 0) {
            str = tempString.substring(0, tempString.indexOf(",")) + tempString.substring(tempString.indexOf(",") + 1);
        }
        //将字符拆分成小数点前后两部分
        String intPatten = "";
        String demicalPatten = "";
        if (str.indexOf(".") > 0) {
            intPatten = str.split("[.]")[0];
            demicalPatten = str.split("[.]")[1];
        } else {
            intPatten = str;
            demicalPatten = "";
        }

        if (validLength <= 0)
            return new StringBuffer(str);
        //只进行整数位截取
        if (intPatten.length() >= validLength) {
            StringBuffer returnStr = new StringBuffer();
            String intS = intPatten.substring(0, validLength);
            if (roundAbled) {
                if (validLength + 1 <= intPatten.length()) {
                    Integer rountS = Integer.parseInt(intPatten.substring(validLength, validLength + 1));
                    if (rountS >= 5) {
                        intS = (Integer.parseInt(intS) + 1) + "";
                    }
                }
            }
            returnStr.append(intS);
            for (int i = 0; i < (intPatten.length() - validLength); i++) {
                returnStr.append("0");
            }
            return returnStr;
        }//只进行小数位截取
        else {
            if (demicalPatten.length() <= 0) {
                return new StringBuffer(intPatten);
            } else if (demicalPatten.length() <= (validLength - intPatten.length())) {
                StringBuffer tempBuffer = new StringBuffer(intPatten + "." + demicalPatten);
                return tempBuffer;
            } else {
                String decimal = demicalPatten.substring(0, validLength - intPatten.length());
                if (roundAbled) {
                    if (validLength - intPatten.length() + 1 <= demicalPatten.length()) {
                        Integer rountD = Integer.parseInt(demicalPatten.substring(validLength - intPatten.length(), validLength - intPatten.length() + 1));
                        if (rountD >= 5)
                            decimal = (Integer.parseInt(decimal) + 1) + "";

                    }
                }
                return new StringBuffer(intPatten + "." + decimal);
            }
        }

    }

    @Override
    public StringBuffer format(long number, StringBuffer result,
                               FieldPosition fieldPosition) {
        DecimalFormat decimalFormat = new DecimalFormat();
        String tempString = decimalFormat.format(number, result, fieldPosition).toString();
        String str = tempString;
        //格式化出来的字符串可能含有,号
        if (tempString.indexOf(",") >= 0) {
            str = tempString.substring(0, tempString.indexOf(",")) + tempString.substring(tempString.indexOf(",") + 1);
        }
        //将字符拆分成小数点前后两部分
        String intPatten = "";
        String demicalPatten = "";
        if (str.indexOf(".") > 0) {
            intPatten = str.split("[.]")[0];
            demicalPatten = str.split("[.]")[1];
        } else {
            intPatten = str;
            demicalPatten = "";
        }

        if (validLength <= 0)
            return new StringBuffer(str);
        //只进行整数位截取
        if (intPatten.length() >= validLength) {
            StringBuffer returnStr = new StringBuffer();
            String intS = intPatten.substring(0, validLength);
            if (roundAbled) {
                if (validLength + 1 <= intPatten.length()) {
                    Integer rountS = Integer.parseInt(intPatten.substring(validLength, validLength + 1));
                    if (rountS >= 5) {
                        intS = (Integer.parseInt(intS) + 1) + "";
                    }
                }
            }
            returnStr.append(intS);
            for (int i = 0; i < (intPatten.length() - validLength); i++) {
                returnStr.append("0");
            }
            return returnStr;
        }//只进行小数位截取
        else {
            if (demicalPatten.length() <= 0) {
                return new StringBuffer(intPatten);
            } else if (demicalPatten.length() <= (validLength - intPatten.length())) {
                StringBuffer tempBuffer = new StringBuffer(intPatten + "." + demicalPatten);
                return tempBuffer;
            } else {
                String decimal = demicalPatten.substring(0, validLength - intPatten.length());
                if (roundAbled) {
                    if (validLength - intPatten.length() + 1 <= demicalPatten.length()) {
                        Integer rountD = Integer.parseInt(demicalPatten.substring(validLength - intPatten.length(), validLength - intPatten.length() + 1));
                        if (rountD >= 5)
                            decimal = (Integer.parseInt(decimal) + 1) + "";

                    }
                }
                return new StringBuffer(intPatten + "." + decimal);
            }
        }
    }

    @Override
    public Number parse(String source, ParsePosition parsePosition) {
        DecimalFormat decimalFormat = new DecimalFormat();
        return decimalFormat.parse(source, parsePosition);
    }
}
