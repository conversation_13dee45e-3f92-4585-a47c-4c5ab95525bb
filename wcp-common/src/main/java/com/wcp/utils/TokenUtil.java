package com.wcp.utils;


import com.wcp.redis.RedisCache;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
@Component
public class TokenUtil {

    @Value("${spring.sso.secretKey}")
    private  String secret;


    // 令牌有效期（默认30分钟）
    @Value("${spring.sso.token-expire-time-web}")
    private  int expireTime;
    @Value("${spring.sso.isCache}")
    private boolean isCache;
    @Autowired
    private RedisCache redisCache;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;

    public  String createToken(Map<String,Object> loginUser) {
        String token = StringUtil.generateUUID();
        Object expiresInObj = loginUser.get("expires_in");
        long expiresIn = expiresInObj != null ? (Long.parseLong(expiresInObj.toString()))/60 : expireTime;

        //使用了缓存
        if(isCache){
            Date expirationDate = new Date(System.currentTimeMillis() + (expiresIn * MILLIS_MINUTE));
            loginUser.put("exp",expirationDate.getTime());
            redisCache.setCacheObject(token,loginUser);
            return token;
        }else{
            //不使用缓存
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            Date expirationDate = new Date(System.currentTimeMillis() + (expiresIn * MILLIS_MINUTE));
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("token过期时间"+sdf.format(expirationDate));
            loginUser.put("exp",expirationDate.getTime());
            return Jwts.builder()
                    .setClaims(loginUser)
                    .setExpiration(expirationDate)
                    .signWith(key, SignatureAlgorithm.HS256)
                    .compact();
        }
    }

    /**
     * 刷新token
     * @param token
     * @return
     */
    public String refreshToken(String token) {
        if(isCache){
            if(redisCache.getCacheObject(token)!=null){
                Map<String, Object> dataMap = redisCache.getCacheObject(token);
                Date exp = (Date) dataMap.get("exp");
                if(exp.getTime()>System.currentTimeMillis()){
                    //未过期
                    Date expirationDate = new Date(System.currentTimeMillis() + (expireTime * MILLIS_MINUTE));
                    dataMap.put("exp",expirationDate.getTime());
                }
            }
        }
        else {
            Map<String, Object> dataMap=null;
            //不使用缓存从token中解析
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            try{
                dataMap= Jwts.parser()
                        .verifyWith(key)
                        .build()
                        .parseSignedClaims(token)
                        .getPayload();
            }catch (ExpiredJwtException ex){
                ex.printStackTrace();
            }catch (Exception ex) {
                // 其他异常，比如无效的JWT等
                ex.printStackTrace();
            }
            if(dataMap!=null){
                long expireTime = (long)dataMap.get("exp")*1000;
                long currentTime = System.currentTimeMillis();
                // 计算时间差（毫秒）
                long timeDifferenceMillis = expireTime - currentTime;
                // 将时间差转换为分钟
                long timeDifferenceMinutes = timeDifferenceMillis / (1000 * 60);
                if(timeDifferenceMinutes>0 && timeDifferenceMinutes<=30){
                    //生成一个新的token
                    Map<String,Object> tokenData=getTokenValues(token);
                    Map<String,Object> loginUser=new HashMap<>();
                    loginUser.put("ID",tokenData.get("ID"));
                    loginUser.put("ACCOUNT",tokenData.get("ACCOUNT"));
                    loginUser.put("USERNAME",tokenData.get("USERNAME"));
                    return createToken(loginUser);
                }
            }
        }
        return null;
    }

    /**
     * 获取token用户信息
     * @param token
     * @return
     */

    public Map<String, Object> getTokenValues(String token) {
        if(isCache){
           return redisCache.getCacheObject(token);
        }else{
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            return Jwts.parser()
                    .verifyWith(key)
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        }

    }

    /**
     * 判断是否失效
     * @param token
     * @return
     */
    public boolean isValidToken(String token){
        Map<String, Object> dataMap=null;
        if(isCache){
            //使用缓存从缓存中获取
            dataMap=redisCache.getCacheObject(token);
        }else if(StringUtil.isNotEmpty(token)){
            //不使用缓存从token中解析
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            try{
                dataMap= Jwts.parser()
                        .verifyWith(key)
                        .build()
                        .parseSignedClaims(token)
                        .getPayload();
            }catch (ExpiredJwtException ex){
                return false;
            }catch (Exception ex) {
                // 其他异常，比如无效的JWT等
                return false;
            }
        }
        if(dataMap!=null){
            long expireTime = (long)dataMap.get("exp")*1000;
            long currentTime = System.currentTimeMillis();
            Date expirationDate = new Date(expireTime);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("token过期时间"+sdf.format(expirationDate));
            if(expireTime>currentTime){
                return true;
            }
        }
        return false;

    }

    public static void main(String[] args) {
        String token="eyJhbGciOiJIUzI1NiJ9.eyJsb2dpbl91c2VyX2tleSI6IjY5NDkwN2I2LTUxZGEtNGE2My1hZTBlLTNkZTkyMTYzZWNmNSIsImV4cCI6MTc0ODU4OTk2NX0.qYi3mOgpKYp0bqfYRYPzuE0pUYJ2QKa--6x2XpWLZ-s";
        Map<String, Object> map=new TokenUtil().getTokenValues(token);
        System.out.println(map);
    }
}
