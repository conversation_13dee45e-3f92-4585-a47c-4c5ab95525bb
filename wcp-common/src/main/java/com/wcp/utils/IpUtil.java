package com.wcp.utils;

import org.apache.log4j.Logger;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.net.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018年1月9日 上午10:20:11
 * @version V1.0
 */
public class IpUtil {

	private static Logger logger = Logger.getLogger(IpUtil.class);
	/**
	 * 获取客户端的IP地址的方法是：request.getRemoteAddr()，这种方法在大部分情况下都是有效的。
	 * 但是在通过了Apache,Squid等反向代理软件就不能获取到客户端的真实IP地址了，如果通过了多级反向代理的话，
	 * X-Forwarded-For的值并不止一个，而是一串IP值， 究竟哪个才是真正的用户端的真实IP呢？
	 * 答案是取X-Forwarded-For中第一个非unknown的有效IP字符串。
	 * 例如：X-Forwarded-For：*************, *************, *************, ************* 
	 * 用户真实IP为： *************
	 * @param request
	 * @return
	 */
	public static String getIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("X-Forwarded-For");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
			if ("127.0.0.1".equals(ip)||"0:0:0:0:0:0:0:1".equals(ip)) {
				/** 根据网卡取本机配置的IP */
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
					ip = inet.getHostAddress();
				} catch (UnknownHostException e) {
					logger.error("IpHelper error." + e.toString());
				}
			}
		}
		//对于通过多个代理的情况， 第一个IP为客户端真实IP,多个IP按照','分割 "***.***.***.***".length() = 15
		if (ip != null && ip.length() > 15) {
			if (ip.indexOf(",") > 0) {
				ip = ip.substring(0, ip.indexOf(","));
			}
		}
		return ip;
	}
	
	/**
	 * @Description: 根据IP获取MAC地址
	 * @param ip
	 * @return
	 * @throws Exception
	 */
    public static String getMACAddress(final String ip)  {
    	String OS = System.getProperty("os.name").toLowerCase();
    	if(OS.toLowerCase().indexOf("windows")>-1){
    		return getMacInWindows(ip);
    	}else if(OS.toLowerCase().indexOf("linux")>-1){
    		return getMacInLinux(ip);
    	}
    	return null;
    	
    }
    
	public static String getMacInWindows(final String ip) {
		String macAddress = "";
    	try {
    		String str = "";
    		Process p = Runtime.getRuntime().exec("nbtstat -a " + ip);
    		InputStreamReader ir = new InputStreamReader(p.getInputStream());
    		LineNumberReader input = new LineNumberReader(ir);
    		for (int i = 1; i < 100; i++) {
    			str = input.readLine();
    			if (str != null) {
    				if (str.indexOf("MAC") > 1) {
    					macAddress = str.substring(str.indexOf("=") + 2, str.length());
    					break;
    				}
    			}
    		}
		} catch (Exception e) {
			System.out.println("获取Mac失败");
		}
		return macAddress;
		
	}

	
	
	public static String getMacInLinux(String ip) {
		String mac = "";
		if (ip != null) {
			try {
				Process process = Runtime.getRuntime().exec("arp " + ip);
				InputStreamReader ir = new InputStreamReader(process.getInputStream());
				LineNumberReader input = new LineNumberReader(ir);
				String line;
				StringBuffer s = new StringBuffer();
				while ((line = input.readLine()) != null) {
					s.append(line);
				}
				mac = s.toString();
				if (mac != null && mac.length()>0) {
					mac = mac.substring(mac.indexOf(":") - 2, mac.lastIndexOf(":") + 3);
				} else {
					mac = null;
				}
				return mac;
			} catch (Exception e) {
				System.out.println("获取Mac失败");
			}
		}
		return mac;

	}
	
	public static List<String> getLocalIPList() {
        List<String> ipList = new ArrayList<String>();
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            NetworkInterface networkInterface;
            Enumeration<InetAddress> inetAddresses;
            InetAddress inetAddress;
            String ip;
            while (networkInterfaces.hasMoreElements()) {
                networkInterface = networkInterfaces.nextElement();
                inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    inetAddress = inetAddresses.nextElement();
                    if (inetAddress != null && inetAddress instanceof Inet4Address) { // IPV4
                        ip = inetAddress.getHostAddress();
                        ipList.add(ip);
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return ipList;
    }
	// 第一种方法:Jdk1.5的InetAddresss,代码简单。当对端机器开了防火墙时，此法ping不通，而第三法可以ping通。
	public static boolean pingSimple(String ipAddress) {
		try {
			int timeOut = 3000; // 超时应该在3钞以上
			boolean status = InetAddress.getByName(ipAddress).isReachable(timeOut); // 当返回值是true时，说明host是可用的，false则不可。
			return status;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	// 第三种方法:也是使用java调用控制台的ping命令，这个比较可靠，还通用，使用起来方便：传入个ip，设置ping的次数和超时，就可以根据返回值来判断是否ping通。
	public static boolean pingComplex(String ipAddress) {
		int pingTimes = 2;
		int timeOut = 3000;
		BufferedReader in = null;
		Runtime r = Runtime.getRuntime(); // 将要执行的ping命令,此命令是windows格式的命令
		String osName = System.getProperty("os.name");
		String pingCommand = "";
		if (osName.toLowerCase().contains("linux")) {
			pingCommand = "ping -c " + pingTimes + " -i 0 " + ipAddress;
		} else {
			pingCommand = "ping " + ipAddress + " -n " + pingTimes + " -w " + timeOut;
		}
		try { // 执行命令并获取输出
			Process p = r.exec(pingCommand);
			if (p == null) {
				return false;
			}
			in = new BufferedReader(new InputStreamReader(p.getInputStream())); // 逐行检查输出,计算类似出现=23ms TTL=62字样的次数
			int connectedCount = 0;
			String line = null;
			while ((line = in.readLine()) != null) {
                if (line.toString().toUpperCase().indexOf("TTL") > 0) {     
                    // 网络畅通      
                	connectedCount++;
                }
			}
			return connectedCount == pingTimes;
		} catch (Exception ex) {
			ex.printStackTrace(); // 出现异常则返回假
			return false;
		} finally {
			try {
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
   
}
