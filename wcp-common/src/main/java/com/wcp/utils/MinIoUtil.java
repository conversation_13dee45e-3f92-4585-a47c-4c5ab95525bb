package com.wcp.utils;

import java.text.SimpleDateFormat;

/**
 * 1. @Description 操作minio文件工具类
 * 2. <AUTHOR>
 * 3. @Date 2025/2/25 17:41
 */

public class MinIoUtil {

    /**
     * 将文件大小转换为友好格式（KB、MB、GB）
     *
     * @param size 文件大小（字节）
     * @return 友好格式的文件大小
     */
    public static String formatSize(long size) {
        if (size < 1024) {
            return size + " bytes";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 将时间戳转换为格式化日期字符串
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化日期字符串
     */
    public static String formatDate(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(timestamp);
    }

    /**
     * 获取文件后缀
     *
     * @param fileName 文件名
     * @return 文件后缀
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }
}
