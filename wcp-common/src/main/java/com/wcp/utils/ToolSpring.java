package com.wcp.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * Description: 用于不使用注解获取yml文件配置
 * Author: qianchao
 * Date: 2024/3/11 11:35
 */
@Component
public final class ToolSpring implements ApplicationContextAware {
    private static ApplicationContext applicationContext = null;

    public static Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (ToolSpring.applicationContext == null) {
            ToolSpring.applicationContext = applicationContext;
        }
    }

    public static <T> Collection<T> getBeansOfType(Class<T> clazz) {
        return applicationContext.getBeansOfType(clazz).values();
    }
}
