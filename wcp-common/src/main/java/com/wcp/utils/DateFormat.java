package com.wcp.utils;

import java.text.FieldPosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间格式类
 *
 * <AUTHOR>
 * @date 2011.4.19
 * @explain 在传入格式时不能用“在这里替换旬”,旬的格式为xx，大小写都行
 */
public class DateFormat extends SimpleDateFormat {
    private static final long serialVersionUID = 1L;

    private String dateFormat;
    private SimpleDateFormat simpleDateFormat;

    public DateFormat(String dateFormat) {
        if (dateFormat.indexOf("xx") >= 0 || dateFormat.indexOf("XX") >= 0 || dateFormat.indexOf("xX") >= 0 || dateFormat.indexOf("Xx") >= 0) {
            dateFormat = dateFormat.replace("XX", "在这里替换旬");
            dateFormat = dateFormat.replace("xx", "在这里替换旬");
            dateFormat = dateFormat.replace("xX", "在这里替换旬");
            dateFormat = dateFormat.replace("Xx", "在这里替换旬");
        }
        if (dateFormat.indexOf("nn") >= 0 || dateFormat.indexOf("NN") >= 0 || dateFormat.indexOf("nN") >= 0 || dateFormat.indexOf("Nn") >= 0) {
            dateFormat = dateFormat.replace("NN", "在这里替换季");
            dateFormat = dateFormat.replace("nn", "在这里替换季");
            dateFormat = dateFormat.replace("nN", "在这里替换季");
            dateFormat = dateFormat.replace("Nn", "在这里替换季");
        }
        if (dateFormat.indexOf("w") >= 0 || dateFormat.indexOf("ww") >= 0) {
            dateFormat = dateFormat.replace("ww", "在这里替换周");
            dateFormat = dateFormat.replace("w", "在这里替换周");
        }
        this.dateFormat = dateFormat;
        simpleDateFormat = new SimpleDateFormat(this.dateFormat);
    }

    @Override
    public StringBuffer format(Date date, StringBuffer toAppendTo,
                               FieldPosition pos) {
        StringBuffer tempBuffer = simpleDateFormat.format(date, toAppendTo, pos);
        if (dateFormat.indexOf("在这里替换旬") >= 0
                || dateFormat.indexOf("在这里替换周") >= 0
                || dateFormat.indexOf("在这里替换季") >= 0) {
            return new StringBuffer(otherFormat(date));
        }
        return tempBuffer;
    }


    public String otherFormat(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        dateFormat = simpleDateFormat.format(date);
        String cloneStr = dateFormat;
        if (dateFormat.indexOf("在这里替换旬") >= 0) {
            String xun = null;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            if (0 < day && day < 11) {
                xun = "上";
            } else if (day > 20) {
                xun = "下";
            } else {
                xun = "中";
            }
            cloneStr = new String(dateFormat);
            cloneStr = cloneStr.replace("在这里替换旬", xun);
        }
        if (dateFormat.indexOf("在这里替换季") >= 0) {
            String season = null;
            int month = calendar.get(Calendar.MONTH);
            season = "" + (month / 3 + 1);
            cloneStr = new String(dateFormat);
            cloneStr = cloneStr.replace("在这里替换季", season);
        }
        if (dateFormat.indexOf("在这里替换周") >= 0) {
            String week = "" + DateUtil.getWeek(calendar);
            cloneStr = new String(dateFormat);
            cloneStr = cloneStr.replace("在这里替换周", week);
        }
        return cloneStr;
    }


    public String otherFormat(Date date, boolean returnNumber) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        dateFormat = simpleDateFormat.format(date);
        String cloneStr = dateFormat;
        if (dateFormat.indexOf("在这里替换旬") >= 0) {
            String xun = null;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            if (0 < day && day < 11) {
                if (returnNumber)
                    xun = "1";
                else
                    xun = "上";
            } else if (day > 20) {
                if (returnNumber)
                    xun = "3";
                else
                    xun = "下";
            } else {
                if (returnNumber)
                    xun = "2";
                else
                    xun = "中";
            }
            cloneStr = new String(dateFormat);
            cloneStr = cloneStr.replace("在这里替换旬", xun);
        }
        if (dateFormat.indexOf("在这里替换季") >= 0) {
            String season = null;
            int month = calendar.get(Calendar.MONTH);
            season = (month / 3 + 1) + "季";
            cloneStr = new String(dateFormat);
            cloneStr = cloneStr.replace("在这里替换季", season);
        }
        return cloneStr;
    }
}
