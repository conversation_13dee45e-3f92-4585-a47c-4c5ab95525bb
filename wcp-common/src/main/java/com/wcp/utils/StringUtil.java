package com.wcp.utils;

import cn.hutool.core.text.StrFormatter;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.Objects.isNull;

public class StringUtil {
    /**
     * 空字符串
     */
    private static final String NULLSTR = "";
    /**
     * 字符串是否包含中文(true 包含中文字符 false 不包含中文字符)
     *
     * @param str
     * @return
     */
    public static boolean isContainChinese(String str) {
        if (!isNullOrEmpty(str)) {
            Pattern pattern = Pattern.compile("[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】]");
            Matcher matcher = pattern.matcher(str);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判定输入的是否是汉字
     * <p>
     * true代表是汉字
     */
    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {
            return true;
        }
        return false;
    }

    /**
     * * 判断一个对象数组是否非空
     *
     * @param objects 要判断的对象数组
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Object[] objects)
    {
        return !isEmpty(objects);
    }

    /**
     * * 判断一个对象数组是否为空
     *
     * @param objects 要判断的对象数组
     ** @return true：为空 false：非空
     */
    public static boolean isEmpty(Object[] objects)
    {
        return isNull(objects) || (objects.length == 0);
    }


    /**
     * 格式化文本, {} 表示占位符<br>
     * 此方法只是简单将占位符 {} 按照顺序替换为参数<br>
     * 如果想输出 {} 使用 \\转义 { 即可，如果想输出 {} 之前的 \ 使用双转义符 \\\\ 即可<br>
     * 例：<br>
     * 通常使用：format("this is {} for {}", "a", "b") -> this is a for b<br>
     * 转义{}： format("this is \\{} for {}", "a", "b") -> this is \{} for a<br>
     * 转义\： format("this is \\\\{} for {}", "a", "b") -> this is \a for b<br>
     *
     * @param template 文本模板，被替换的部分用 {} 表示
     * @param params 参数值
     * @return 格式化后的文本
     */
    public static String format(String template, Object... params)
    {
        if (isEmpty(params) || isEmpty(template))
        {
            return template;
        }
        return StrFormatter.format(template, params);
    }


    /**
     * 判断字符串不为空
     *
     * @return
     */
    public static boolean isNotEmpty(String str) {
        return !StringUtil.isEmpty(str);
    }

    /**
     * 判断多个字符串是否为空
     * @param strings
     * @return
     */
    public static boolean isEmptyAll(String... strings) {
        boolean a = false;
        for (String str : strings) {
            if (str != null && str.trim().length() != 0) {
                continue;
            }
            a = true;
            break;
        }
        return a;
    }

    /**
     * 判断字符串为空
     *
     * @return
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().length() == 0;
    }

    /**
     * 判断字符串能否转换为int
     * @param str
     * @return
     */
    public static boolean isInteger(String str) {
        try {
            Integer.parseInt(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 创建SQL语句占位符字符串
     *
     * @param count 占位符个数
     * @return
     */
    public static String createSqlPlaceHolder(int count) {
        String sqlStr = "";
        for (int i = 0; i < count; i++) {
            sqlStr += (i == 0 ? "" : ",") + "?";
        }
        return sqlStr;
    }
	
	/*
	将NUll转换为EMPTY
	将empty装欢为NUll
	判断是否为null或empty
	首字母大写
	首字母小写
	手机号验证 
	电话号码验证
	ava验证IP地址
	验证邮箱
	*/

    /**
     * 获取32位UUID
     *
     * @return
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 将NUll转换为EMPTY
     *
     * @param string
     * @return
     */
    public static String nullToEmpty(String string) {
        return (string == null) ? "" : string;
    }

    /**
     * 将empty装欢为NUll
     *
     * @param string
     * @return
     */
    public static String emptyToNull(String string) {
        return isNullOrEmpty(string) ? null : string;
    }


    /**
     * 判断是否为null或empty
     *
     * @param string
     * @return
     */
    public static boolean isNullOrEmpty(String string) {
        return string == null || string.length() == 0 || "null".equals(string);
    }


    /**
     * 首字母大写
     */
    public static String firstUpperCase(String str) {
        char[] ch = str.toCharArray();
        if (ch[0] >= 'a' && ch[0] <= 'z') {
            ch[0] = (char) (ch[0] - 32);
        }
        return new String(ch);

    }

    /**
     * 首字母小写
     *
     * @param str
     * @return
     */
    public static String firstLowerCase(String str) {
        Character ch = str.charAt(0);
        char[] array = str.toCharArray();
        array[0] = Character.toLowerCase(ch);
        return String.valueOf(array);
    }
    /**
     * 截取字符串
     *
     * @param str   字符串
     * @param start 开始
     * @param end   结束
     * @return 结果
     */
    public static String substring(final String str, int start, int end) {
        if (str == null) {
            return NULLSTR;
        }

        if (end < 0) {
            end = str.length() + end;
        }
        if (start < 0) {
            start = str.length() + start;
        }

        if (end > str.length()) {
            end = str.length();
        }

        if (start > end) {
            return NULLSTR;
        }

        if (start < 0) {
            start = 0;
        }
        if (end < 0) {
            end = 0;
        }

        return str.substring(start, end);
    }


    /**
     * 手机号验证
     *
     * @param str
     * @return 验证通过返回true
     */
    public static boolean isMobile(String str) {
        Pattern p = null;
        Matcher m = null;
        boolean b = false;
        p = Pattern.compile("^[1][3,4,5,8][0-9]{9}$"); // 验证手机号  
        m = p.matcher(str);
        b = m.matches();
        return b;
    }

    /**
     * 电话号码验证
     *
     * @param str
     * @return 验证通过返回true
     */
    public static boolean isPhone(String str) {
        Pattern p1 = null, p2 = null;
        Matcher m = null;
        boolean b = false;
        p1 = Pattern.compile("^[0][1-9]{2,3}-[0-9]{5,10}$");  // 验证带区号的  
        p2 = Pattern.compile("^[1-9]{1}[0-9]{5,8}$");         // 验证没有区号的  
        if (str.length() > 9) {
            m = p1.matcher(str);
            b = m.matches();
        } else {
            m = p2.matcher(str);
            b = m.matches();
        }
        return b;
    }


    /**
     * @param email
     * @return boolean
     * @throws
     * @Title: isEmail
     * @Description: 验证邮箱
     */
    public static boolean isEmail(String email) {
        Pattern pattern = Pattern.compile("^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$");
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }


    /**
     * 获取随机字母数字组合
     *
     * @param length 字符串长度
     * @return
     */
    public static String getRandomCharAndNumr(Integer length) {
        StringBuilder sb = new StringBuilder();
        Random rand = new Random();//随机用以下三个随机生成器
        Random randdata = new Random();
        int data = 0;
        for (int i = 0; i < length; i++) {
            int index = rand.nextInt(3);
            //目的是随机选择生成数字，大小写字母
            switch (index) {
                case 0:
                    data = randdata.nextInt(10);//仅仅会生成0~9
                    sb.append(data);
                    break;
                case 1:
                    data = randdata.nextInt(26) + 65;//保证只会产生65~90之间的整数
                    sb.append((char) data);
                    break;
                case 2:
                    data = randdata.nextInt(26) + 97;//保证只会产生97~122之间的整数
                    sb.append((char) data);
                    break;
            }
        }
        String result = sb.toString();
        return result;
    }

    /**
     * 验证随机字母数字组合是否纯数字与纯字母
     *
     * @param str
     * @return true 是 ， false 否
     */
    public static boolean isRandomUsable(String str) {
        // String regExp =
        // "^[A-Za-z]+(([0-9]+[A-Za-z0-9]+)|([A-Za-z0-9]+[0-9]+))|[0-9]+(([A-Za-z]+[A-Za-z0-9]+)|([A-Za-z0-9]+[A-Za-z]+))$";
        String regExp = "^([0-9]+)|([A-Za-z]+)$";
        Pattern pat = Pattern.compile(regExp);
        Matcher mat = pat.matcher(str);
        return mat.matches();
    }


    /**
     * 获取指定字符串出现的次数
     *
     * @param srcText  源字符串
     * @param findText 要查找的字符串
     * @return
     */
    public static int appearNumber(String srcText, String findText) {
        int count = 0;
        Pattern p = Pattern.compile(findText);
        Matcher m = p.matcher(srcText);
        while (m.find()) {
            count++;
        }
        return count;
    }

    public static List<String> split(String str, String regex) {
        if (str != null && !"".equals(str.trim())) {
            String[] strArr = str.trim().split(",");
            return Arrays.asList(strArr);
        }
        return new ArrayList<String>();
    }

    public static String splicing(String[] list, String regex) {
        String temp = "";
        if (list == null) return temp;
        for (int i = 0; i < list.length; i++) {
            temp += i == 0 ? list[i] : regex + list[i];
        }
        return temp;
    }

    public static String splicing(List<String> list, String regex) {
        String temp = "";
        if (list == null) return temp;
        for (int i = 0; i < list.size(); i++) {
            temp += i == 0 ? list.get(i) : regex + list.get(i);
        }
        return temp;
    }

    /**
     * 匹配是否包含数字
     *
     * @param str 可能为中文，也可能是-19162431.1254，不使用BigDecimal的话，变成-1.91624311254E7
     * @return
     */
    public static boolean isNumeric(String str) {
        // 该正则表达式可以匹配所有的数字 包括负数
        Pattern pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
        String bigStr;
        try {
            bigStr = new BigDecimal(str).toString();
        } catch (Exception e) {
            //异常 说明包含非数字。
            return false;
        }
        //matcher是全匹配
        Matcher isNum = pattern.matcher(bigStr);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 匹配是否包含数字
     *
     * @param str          可能为中文，也可能是-19162431.1254，不使用BigDecimal的话，变成-1.91624311254E7
     * @param existDecimal 是否存在小数位
     * @return
     */
    public static boolean isNumeric(String str, boolean existDecimal) {
        // 该正则表达式可以匹配所有的数字 包括负数
        Pattern pattern = null;
        if (existDecimal) {
            pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
        } else {
            pattern = Pattern.compile("-?[0-9]+");
        }
        String bigStr;
        try {
            bigStr = new BigDecimal(str).toString();
        } catch (Exception e) {
            //异常 说明包含非数字。
            return false;
        }
        //matcher是全匹配
        Matcher isNum = pattern.matcher(bigStr);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 按":"把多个key组合成一个key
     *
     * @param subKeys
     * @return
     */
    public static String composeKey(String... subKeys) {
        String key = "";
        for (int i = 0; i < subKeys.length; i++) {
            if (i == 0) {
                key += subKeys[i];
            } else {
                key += ":" + subKeys[i];
            }
        }
        return key;
    }

    /**
     * 密码字符匹配，必须是包含大写字母、小写字母、数字、特殊符号（不是字母，数字，下划线，汉字的字符）的8位以上组合
     *
     * @param matchStr
     * @return
     */
    public static boolean matchPassword(String matchStr) {
        String regular = "^(?![A-Za-z0-9]+$)(?![a-z0-9\\W]+$)(?![A-Za-z\\W]+$)(?![A-Z0-9\\W]+$)[a-zA-Z0-9\\W]{8,}$";
        return matchStr.matches(regular);
    }


    public static String[] splitString(String str, String matchStr, String[][] excludeStr) {
        List<String> resultList = new ArrayList<String>();
        int addStrSize = matchStr.length();
        int length = str.length();
        int lastIdx = 0;
        boolean unExclude = true;
        List<int[]> rangeIdxs = new ArrayList<int[]>();
        List<Integer> unjudegPos = new ArrayList<Integer>();
        for (int i = 0; i < length; i++) {
            String afterStr = str.substring(i);
            String escapeStr = str.substring(i, i + 1);
            if (afterStr.startsWith(matchStr) && unExclude && rangeIdxs.size() == 0) {
                resultList.add(str.substring(lastIdx, i));
                lastIdx = i + addStrSize;
                i += addStrSize - 1;
            }
            if (excludeStr != null && unExclude && !unjudegPos.contains(i)) {
                for (int j = 0; j < excludeStr.length; j++) {
                    if (afterStr.startsWith(excludeStr[j][0])) {
                        rangeIdxs.add(new int[]{j, i});
                        break;
                    }
                }
            }
            if (excludeStr != null && rangeIdxs.size() > 0 && !unjudegPos.contains(i) && unExclude) {
                if (afterStr.startsWith(excludeStr[rangeIdxs.get(rangeIdxs.size() - 1)[0]][1])) {
                    rangeIdxs.remove(rangeIdxs.size() - 1);
                }
            }
            if (unExclude && escapeStr.equals("\\")) {
                unExclude = false;
            } else if (!unExclude) {
                unExclude = true;
            }
            if (i == length - 1 && rangeIdxs.size() > 0) {
                i = rangeIdxs.get(rangeIdxs.size() - 1)[1];
                rangeIdxs.remove(rangeIdxs.size() - 1);
                unExclude = true;
            }
        }
        resultList.add(str.substring(lastIdx));
        return resultList.toArray(new String[0]);
    }

    /**
     * 数组转换成字符串
     *
     * @param array
     * @return
     */
    public static String arrayToString(Object array) {
        if (array == null) return "";
        String tempStr = "[";
        if (array.getClass().isArray()) {
            int length = Array.getLength(array);
            for (int i = 0; i < length; i++) {
                if (i > 0) tempStr += ",";
                Object obj = Array.get(array, i);
                if (obj == null) {
                    tempStr += "null";
                } else if (obj.getClass().isArray()) {
                    tempStr += arrayToString(obj);
                } else {
                    tempStr += obj.toString();
                }
            }
        }
        tempStr += "]";
        return tempStr;
    }

    /**
     * 随机一个字符
     *
     * @return
     */
    public static char randChar() {
        double dx = Math.random();
        if (dx <= 0.35) {
            return (char) (Math.random() * 26 + 'a');
        } else if (dx <= 0.7) {
            return (char) (Math.random() * 26 + 'A');
        } else {
            return (char) (Math.random() * 10 + '0');
        }
    }

    /**
     * 随机生成指定长度的字符串，包含大小写字母、数库
     *
     * @param num
     * @return
     */
    public static String randChar(int num) {
        String tempStr = "";
        for (int i = 0; i < num; i++) {
            tempStr += randChar();
        }
        return tempStr;
    }

    /**
     * 去掉url路径的文件路径前缀
     * "file:/home/<USER>/cn/fh" -> "/home/<USER>/cn/fh"
     * "jar:file:/home/<USER>/foo.jar!cn/fh" -> "/home/<USER>/foo.jar"
     *
     * @param url url地址
     * @return
     */
    public static String getRootPath(URL url) {
        String fileUrl = url.getFile();
        int pos = fileUrl.indexOf('!');
        if (-1 == pos) {
            return fileUrl;
        }
        return fileUrl.substring(5, pos);
    }

    /**
     * 替换的包的连接点号为对象反斜杠连接
     * "cn.fh.lightning" -> "cn/fh/lightning"
     *
     * @param name 路径
     * @return
     */
    public static String dotToSplash(String name) {
        return name.replaceAll("\\.", "/");
    }

    /**
     * 去掉文件的后缀
     *
     * @param name
     * @return
     */
    public static String trimExtension(String name) {
        int pos = name.indexOf('.');
        if (-1 != pos) {
            return name.substring(0, pos);
        }
        return name;
    }

    /**
     * 截取URL路长最后一级
     * /application/home -> /home
     *
     * @param uri
     * @return
     */
    public static String trimURI(String uri) {
        String trimmed = uri.substring(1);
        int splashIndex = trimmed.indexOf('/');
        return trimmed.substring(splashIndex);
    }

    /**
     * 判断字符集是否为空
     *
     * @param cs
     * @return
     */
    public static boolean isNotEmpty(CharSequence cs) {
        return cs != null && cs.length() > 0;
    }
//    public static void main(String[] args) {
//		String tempString = "1231,[[11,[22,22,99],[22,1231,1231],33]]]";
//		System.out.println(tempString.length());
//		System.out.println(Arrays.asList(splitString(tempString, ",", new String[][] {{"[","]"}})));
//		//String tempString2 = tempString.substring(-1, 0);
//		System.out.println("aa" + tempString.substring(0, 1));
//	}

    /**
     * 获取异常信息
     */
    public static String getStackTraceInfo(Exception e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        try {
            e.printStackTrace(pw);
            pw.flush();
            sw.flush();
            return sw.toString();
        } catch (Exception ex) {
            return "异常信息转换错误";
        } finally {
            try {
                pw.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            try {
                sw.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    /**
     * 生成规则的SQL语句，采用固定和浮动索引
     * @param fixed   固定字符串
     * @param bidx    浮动开始索引
     * @param eidx    浮动结束索引
     * @return
     */
    public static String generatorRegularSql(String fixed, int bidx, int eidx) {
        String sqlStr = "";
        for (int i = bidx; i <= eidx; i++) {
            sqlStr += fixed + i + (i == eidx ? "" : ",");
        }
        return sqlStr;
    }
    /**
     * * 判断一个对象是否非空
     *
     * @param object Object
     * @return true：非空 false：空
     */
    public static boolean isNotNull(Object object) {
        return !isNull(object);
    }

    /**
     * 判断子字符串在父字符串中出现的个数
     * @param mainString 父字符串
     * @param subString 子字符串
     * @return 数量
     */
    public static int countSubstringOccurrences(String mainString, String subString) {
        // Handle null or empty strings to avoid null pointer or index out of bounds exceptions
        if (mainString == null || subString == null || mainString.isEmpty() || subString.isEmpty()) {
            return 0;
        }
        int count = 0;
        int fromIndex = 0;

        // Use indexOf to find occurrences of the substring
        while ((fromIndex = mainString.indexOf(subString, fromIndex)) != -1) {
            count++;
            fromIndex += subString.length(); // Move past this occurrence to continue search
        }
        return count;
    }

    /**
     * 替换select和from中间的值
     */
    public static String replaceBetweenSelectAndFrom(String sql, String replaceStr) {
        // 正则表达式匹配从SELECT开始到FROM结束的所有内容
        String newSql = sql;
        Pattern pattern = Pattern.compile("(?i)SELECT\\s+.*?\\s+FROM");
        Matcher matcher = pattern.matcher(newSql);
        if (matcher.find()) {
            String selectedPart = matcher.group();
            // 替换子串为COUNT(0)，但要保留SELECT和FROM关键字
            selectedPart = selectedPart.substring(selectedPart.indexOf("SELECT") + "SELECT".length());
            selectedPart = selectedPart.substring(0, selectedPart.indexOf("FROM"));
            newSql = newSql.replace(selectedPart, replaceStr);
        }
        return newSql;
    }

    /**
     * 增加select和from中间的值
     */
    public static String addBetweenSelectAndFrom(String sql, String add) {
        // 查找SELECT开始的位置和FROM结束的位置
        int start = sql.indexOf("SELECT") + "SELECT".length();
        int end = sql.indexOf("FROM", start);

        if (start != -1 && end != -1) {
            // 提取SELECT到FROM之间的子串
            String selectedPart = sql.substring(start, end);
            // 在提取出的部分后面添加内容并替换原SQL中的对应部分
            return sql.substring(0, start) + selectedPart + add + sql.substring(end);
        } else {
            // 如果没有找到SELECT或FROM，则返回原始SQL（也可以选择抛出异常）
            return sql;
        }
    }

    public static boolean containsZero(int[] array) {
        for (int i : array) {
            if (i == 0) {
                return false;
            }
        }
        return true;
    }
}
