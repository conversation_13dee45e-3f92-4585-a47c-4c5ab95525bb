package com.wcp.utils;

import org.gdal.gdal.Band;
import org.gdal.gdal.Dataset;
import org.gdal.gdal.gdal;
import org.gdal.ogr.*;
import org.gdal.osr.SpatialReference;

import java.util.Vector;

/**
 * 1. @Description GIs相关文件互相转换
 * 2. <AUTHOR>
 * 3. @Date 2024/8/13 10:40
 */

public class GisUtil {
    static {
        // 注册所有gdal驱动
        gdal.AllRegister();
        ogr.RegisterAll();
        gdal.SetConfigOption("GDAL_FILENAME_IS_UTF8","YES");//支持中文路径
    }

    /**
     * tif转geojson
     * @param tifPath
     * @param geoJsonPath
     * @throws Exception
     */
    public static void  tifToGeo(String tifPath,String geoJsonPath) throws Exception {
        Dataset dataset = gdal.Open(tifPath);
        Band band = dataset.GetRasterBand(1);
        Double[] noDataValueArray = new Double[1];
        band.GetNoDataValue(noDataValueArray);
        // 获取栅格数据的宽度和高度
        int width = dataset.getRasterXSize();
        int height = dataset.getRasterYSize();
        // 获取栅格数据
        float[] elevationData = new float[width * height];
        dataset.GetRasterBand(1).ReadRaster(0, 0, width, height, elevationData);
        Driver driver=ogr.GetDriverByName("GeoJSON");
        DataSource dataSource=driver.CreateDataSource(geoJsonPath);
        SpatialReference spatialRef   = new SpatialReference(dataset.GetProjectionRef());
        Vector<String> options = new Vector<>();
        options.add("ENCODING=UTF-8");
        Layer layer=dataSource.CreateLayer("polygonized",spatialRef,ogr.wkbPolygon,options);
        layer.CreateField(new org.gdal.ogr.FieldDefn("ID", ogr.OFTInteger));
        layer.CreateField(new org.gdal.ogr.FieldDefn("Elevation", ogr.OFTReal));
        gdal.Polygonize(band, null, layer, 0);
        dataset.delete();
        dataSource.delete();
    }

    /**
     * tif转shp
     * @param tifPath
     * @param shpfilePath
     * @throws Exception
     */
    public static void  tifToShpfile(String tifPath,String shpfilePath) throws Exception {
        Dataset dataset = gdal.Open(tifPath);
        Band band = dataset.GetRasterBand(1);
        Double[] noDataValueArray = new Double[1];
        band.GetNoDataValue(noDataValueArray);
        // 获取栅格数据的宽度和高度
        int width = dataset.getRasterXSize();
        int height = dataset.getRasterYSize();
        // 获取栅格数据
        float[] elevationData = new float[width * height];
        dataset.GetRasterBand(1).ReadRaster(0, 0, width, height, elevationData);
        Driver driver=ogr.GetDriverByName("ESRI Shapefile");
        DataSource dataSource=driver.CreateDataSource(shpfilePath);
        SpatialReference spatialRef   = new SpatialReference(dataset.GetProjectionRef());
        Vector<String> options = new Vector<>();
        options.add("ENCODING=UTF-8");
        Layer layer=dataSource.CreateLayer("polygonized",spatialRef,ogr.wkbPolygon,options);
        layer.CreateField(new org.gdal.ogr.FieldDefn("ID", ogr.OFTInteger));
        layer.CreateField(new org.gdal.ogr.FieldDefn("Elevation", ogr.OFTReal));
        gdal.Polygonize(band, null, layer, 0);
        dataset.delete();
        dataSource.delete();
    }

    /**
     * shp转Geo
     * @param shpfilePath
     * @param geoJsonPath
     * @throws Exception
     */
    public static void shpToGeo(String shpfilePath,String geoJsonPath) throws Exception {
        Driver driver=ogr.GetDriverByName("ESRI Shapefile");
        DataSource shpDataSource = driver.Open(shpfilePath);
        Driver geojson_driver = ogr.GetDriverByName("GeoJSON");
        //geojson_driver.DeleteDataSource(geoJsonPath);
        DataSource geoJsonDataSource =geojson_driver.CreateDataSource(geoJsonPath);
        Layer shpLayer =shpDataSource.GetLayer(0);
        SpatialReference spatialRef = new SpatialReference(shpLayer.GetSpatialRef().ExportToWkt());
        Layer geoJsonLayer = geoJsonDataSource.CreateLayer(
                shpLayer.GetName(),
                spatialRef,
                shpLayer.GetGeomType()
        );
        // 复制字段
        for (int i = 0; i < shpLayer.GetLayerDefn().GetFieldCount(); i++) {
            geoJsonLayer.CreateField(shpLayer.GetLayerDefn().GetFieldDefn(i));
        }

        // 复制要素
        Feature feature;
        while ((feature = shpLayer.GetNextFeature()) != null) {
            geoJsonLayer.CreateFeature(feature);
        }
        // 清理
        shpDataSource.delete();
        geoJsonDataSource.delete();
    }

    /**
     * geo 转shp
     * @param geofilePath
     * @param shpPath
     * @throws Exception
     */
    public static void geoToShp(String geofilePath,String shpPath) throws Exception {
        Driver driver=ogr.GetDriverByName("GeoJSON");
        DataSource shpDataSource = driver.Open(geofilePath);
        Driver geojson_driver = ogr.GetDriverByName("ESRI Shapefile");
        //geojson_driver.DeleteDataSource(geoJsonPath);
        DataSource geoJsonDataSource =geojson_driver.CreateDataSource(shpPath);
        Layer shpLayer =shpDataSource.GetLayer(0);
        SpatialReference spatialRef = new SpatialReference(shpLayer.GetSpatialRef().ExportToWkt());
        Vector<String> options = new Vector<>();
        options.add("ENCODING=UTF-8");
        Layer geoJsonLayer = geoJsonDataSource.CreateLayer(
                shpLayer.GetName(),
                spatialRef,
                shpLayer.GetGeomType(),
                options
        );
        // 复制字段
        for (int i = 0; i < shpLayer.GetLayerDefn().GetFieldCount(); i++) {
            geoJsonLayer.CreateField(shpLayer.GetLayerDefn().GetFieldDefn(i));
        }
        // 复制要素
        Feature feature;
        while ((feature = shpLayer.GetNextFeature()) != null) {
            geoJsonLayer.CreateFeature(feature);
        }
        // 清理
        shpDataSource.delete();
        geoJsonDataSource.delete();
    }

    public static void main(String[] args) {
        try {
            geoToShp("E:\\WeChat Files\\wxid_wuy3spn6hwci22\\FileStorage\\File\\2024-09\\output.geojson","C:\\Users\\<USER>\\Desktop\\wep\\aa.shp");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
