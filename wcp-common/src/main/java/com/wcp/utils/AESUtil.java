package com.wcp.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * 项目前后台数据传输
 */
public class AESUtil {
    private static final String SECRET_KEY_ALGORITHM = "AES";
    private static final String CIPHER_TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final String key="666adf3247506434";
    private static final String iv="666adf3247506434";

    /**
     * 加密
     * @param data
     * @return
     */
    public static String encrypt(String data) {
        byte[] encrypted = new byte[0];
        try {
            encrypted = crypt(data.getBytes(), key.getBytes(), iv.getBytes(), Cipher.ENCRYPT_MODE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * 解密
     * @param data
     * @return
     */
    public static String decrypt(String data)  {
        byte[] decrypted = new byte[0];
        try {
            decrypted = crypt(Base64.getDecoder().decode(data), key.getBytes(), iv.getBytes(), Cipher.DECRYPT_MODE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new String(decrypted);
    }

    private static byte[] crypt(byte[] data, byte[] key, byte[] iv, int mode) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(key, SECRET_KEY_ALGORITHM);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance(CIPHER_TRANSFORMATION);
        cipher.init(mode, keySpec, ivSpec);

        return cipher.doFinal(data);
    }
    public static void main(String[] args) {
        //CE61CDABA2DEAE9DCCCC620657AF32B9
        //System.out.println(decrypt("1j8VSD1mGiQIKTLjxPtrOQ=="));
        System.out.println(encrypt("admin@123"));
    }
}
