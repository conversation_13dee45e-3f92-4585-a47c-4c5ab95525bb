package com.wcp.utils;

import org.apache.log4j.Logger;

import java.lang.reflect.Array;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class ArrayUtil {
    private static Logger logger = Logger.getLogger(ArrayUtil.class);

    public static boolean arrayContains(String[] array, String tempStr) {
        boolean isContains = false;
        if (!StringUtil.isNullOrEmpty(tempStr)) {
            for (int i = 0; i < array.length; i++) {
                if (tempStr.equals(array[i])) {
                    isContains = true;
                    break;
                }
            }
        }
        return isContains;
    }

    public static boolean contains(String[] array, String temp) {
        for (int i = 0; i < array.length; i++) {
            if (array[i].equals(temp)) return true;
        }
        return false;
    }

    public static int[] subArray(int[] array, int binddex, int eindex) {
        int[] tempArray = new int[eindex - binddex];
        for (int i = binddex; i < eindex; i++) {
            tempArray[i - binddex] = array[i];
        }
        return tempArray;
    }

    public static double[] subArray(double[] array, int binddex, int eindex) {
        double[] tempArray = new double[eindex - binddex];
        for (int i = binddex; i < eindex; i++) {
            tempArray[i - binddex] = array[i];
        }
        return tempArray;
    }

    public static String[] format(Double[] tempArray, String pattern) {
        if (tempArray == null) return null;
        DecimalFormat format = new DecimalFormat(pattern);
        String[] temp = new String[tempArray.length];
        for (int i = 0; i < temp.length; i++) {
            if (tempArray[i] == null) {
                temp[i] = "";
            } else {
                temp[i] = format.format(tempArray[i]);
            }
        }
        return temp;
    }



    /**
     * 把数组2的值加在数组1上
     *
     * @param value1
     * @param value2
     * @return
     */
    public static void arrayCoverAddition(double[] value1, double[] value2) {
        if (value1 == null || value2 == null || value1.length != value2.length)
            return;
        for (int i = 0; i < value1.length; i++) {
            value1[i] += value2[i];
        }
    }

    /**
     * 由value2的值按顺序填充value1，覆盖value1
     *
     * @param value1
     * @param value2
     * @return
     */
    public static void arrayCoverFill(double[] value1, double[] value2) {
        if (value1 == null || value2 == null)
            return;
        for (int i = 0; i < value2.length; i++) {
            if (i >= value1.length) {
                break;
            }
            value1[i] = value2[i];
        }
    }

    @SuppressWarnings("unchecked")


    /**
     * 计算数组指定区间的和
     *
     * @param values
     * @param begIndex
     * @param endIndex
     * @return
     */
    public static double getSum(double[] values, int begIndex, int endIndex) {
        if (begIndex > endIndex)
            return -99.0;
        double sum = 0.0;
        for (int i = begIndex; i <= endIndex; i++) {
            sum += values[i];
        }
        return sum;
    }

    public static int indexOf(String[] values, String obj) {
        if (values == null || values.length == 0) return -1;
        for (int i = 0; i < values.length; i++) {
            if (obj.equals(values[i])) return i;
        }
        return -1;
    }

    public static int indexOf(int[] values, int obj) {
        if (values == null || values.length == 0) return -1;
        for (int i = 0; i < values.length; i++) {
            if (obj == values[i]) return i;
        }
        return -1;
    }

    public static double getSum(double[] values) {
        if (values == null) return 0.0;
        double sum = 0.0;
        for (int i = 0; i < values.length; i++) {
            sum += values[i];
        }
        return sum;
    }

    public static int getSum(int[] values) {
        int sum = 0;
        for (int i = 0; i < values.length; i++) {
            sum += values[i];
        }
        return sum;
    }

    public static double getAbsSum(double[] values) {
        double sum = 0.0;
        for (int i = 0; i < values.length; i++) {
            sum += Math.abs(values[i]);
        }
        return sum;
    }

    public static double getAbsSum(Object values) {
        if (values == null || !values.getClass().isArray()) return 0.0;
        double sum = 0.0;
        for (int i = 0; i < Array.getLength(values); i++) {
            sum += Math.abs(Array.getDouble(values, i));
        }
        return sum;
    }

    public static double getSum(Array array) {
        if (Array.getLength(array) == 0 || !array.getClass().getName().equals("[D"))
            return 0.0;
        double sum = 0.0;
        for (int i = 0; i < Array.getLength(array); i++) {
            sum += Array.getDouble(array, i);
        }
        return sum;
    }

    public static double getSum(Object array) {
        if (!array.getClass().isArray() || Array.getLength(array) == 0
                || !array.getClass().getName().equals("[D"))
            return 0.0;
        double sum = 0.0;
        for (int i = 0; i < Array.getLength(array); i++) {
            sum += Array.getDouble(array, i);
        }
        return sum;
    }

    public static double getSum(Object array1, Object array2) {
        if (array1 == null && array2 == null) {
            return 0.0;
        } else if (array1 == null && array2 != null) {
            return getSum(array2);
        } else if (array1 != null && array2 == null) {
            return getSum(array1);
        } else {
            return getSum(array1) + getSum(array2);
        }
    }


    /**
     * 计算数组指定区间的平均值
     *
     * @param values
     * @param begIndex
     * @param endIndex
     * @return
     */
    public static double getAvg(double[] values, int begIndex, int endIndex) {
        if (begIndex > endIndex)
            return -99.0;
        double sum = 0.0;
        for (int i = begIndex; i <= endIndex; i++) {
            sum += values[i];
        }
        sum /= (endIndex - begIndex + 1);
        return sum;
    }

    public static double getAvg(double[] values) {
        if (values.length == 0)
            return 0.0;
        double sum = 0.0;
        for (int i = 0; i < values.length; i++) {
            sum += values[i];
        }
        return sum / values.length;
    }

    /*
    排除0
     */
    public static double getAvg2(double[] values) {
        if (values.length == 0)
            return 0.0;
        double sum = 0.0;
        int num = 1;
        for (int i = 0; i < values.length; i++) {
            sum += values[i];
            if (values[i] > 0){
                num++;
            }
        }
        return sum / num;
    }

    public static double getAvg(double[] values, double maxValue, double minValue) {
        if (values.length == 0)
            return 0.0;
        double sum = 0.0;
        int count = 0;
        for (int i = 0; i < values.length; i++) {
            if (values[i] >= minValue && values[i] <= maxValue) {
                sum += values[i];
                count++;
            }
        }
        if (count > 0) {
            sum /= count;
        }
        return sum;
    }

    public static double getAvg(List<Double> values) {
        if (values == null || values.size() == 0)
            return 0.0;
        double sum = 0.0;
        int num = 0;
        for (int i = 0; i < values.size(); i++) {
            if (values.get(i) != null) {
                sum += values.get(i);
                num++;
            }
        }
        if (num > 0) {
            return sum / num;
        } else {
            return 0.0;
        }
    }

    public static double getAvg(Array array) {
        if (Array.getLength(array) == 0 || !array.getClass().getName().equals("[D"))
            return 0.0;
        double sum = 0.0;
        for (int i = 0; i < Array.getLength(array); i++) {
            sum += Array.getDouble(array, i);
        }
        return sum / Array.getLength(array);
    }

    public static double getMin(double value1, double value2) {
        if (value1 < value2) {
            return value1;
        } else {
            return value2;
        }
    }

    public static double getMin(double[] values) {
        if (values.length == 0)
            return 0.0;
        double tempValue = values[0];
        for (int i = 1; i < values.length; i++) {
            if (tempValue > values[i])
                tempValue = values[i];
        }
        return tempValue;
    }

    public static int getMinIndex(double[] values) {
        if (values.length == 0)
            return 0;
        int index = 0;
        for (int i = 1; i < values.length; i++) {
            if (values[i] < values[index]) {
                index = i;
            }
        }
        return index;
    }

    public static double getMin(double[] values, int begIdx, int endIdx) {
        if (values.length == 0 || values.length < begIdx || values.length < endIdx || endIdx < begIdx) {
            return 0.0;
        }
        double tempValue = values[begIdx];
        for (int i = begIdx + 1; i < endIdx + 1; i++) {
            if (tempValue > values[i])
                tempValue = values[i];
        }
        return tempValue;
    }

    public static double getMin(Array array) {
        if (Array.getLength(array) == 0 || !array.getClass().getName().equals("[D"))
            return 0.0;
        double tempValue = Array.getDouble(array, 0);
        for (int i = 1; i < Array.getLength(array); i++) {
            if (tempValue > Array.getDouble(array, i))
                tempValue = Array.getDouble(array, i);
        }
        return tempValue;
    }

    public static double getMin(Object array) {
        if (!array.getClass().isArray() || Array.getLength(array) == 0
                || !array.getClass().getName().equals("[D"))
            return 0.0;
        double tempValue = Array.getDouble(array, 0);
        for (int i = 1; i < Array.getLength(array); i++) {
            if (tempValue > Array.getDouble(array, i))
                tempValue = Array.getDouble(array, i);
        }
        return tempValue;
    }

    public static double getMin(Object array1, Object array2) {
        if (array1 == null && array2 == null) {
            return 0.0;
        } else if (array1 == null && array2 != null) {
            return getMin(array2);
        } else if (array1 != null && array2 == null) {
            return getMin(array1);
        } else if (Array.getLength(array1) == 0) {
            return getMin(array2);
        } else if (Array.getLength(array2) == 0) {
            return getMin(array1);
        } else {
            return getMin(getMin(array1), getMin(array2));
        }
    }

    public static double[] getMinArray(double[] values1, double[] values2) {
        if (values1 == null) return values2;
        else if (values2 == null) return values1;
        if (values1.length != values2.length) return null;
        double[] values = new double[values1.length];
        for (int i = 0; i < values.length; i++) {
            values[i] = getMin(values1[i], values2[i]);
        }
        return values;
    }

    public static double[] getMaxArray(double[] values1, double[] values2) {
        if (values1 == null) return values2;
        else if (values2 == null) return values1;
        if (values1.length != values2.length) return null;
        double[] values = new double[values1.length];
        for (int i = 0; i < values.length; i++) {
            values[i] = getMax(values1[i], values2[i]);
        }
        return values;
    }

    public static double[] getMinArrayGreaterZero(double[] values1, double[] values2) {
        if (values1 == null) return values2;
        else if (values2 == null) return values1;
        if (values1.length != values2.length) return null;
        double[] values = new double[values1.length];
        for (int i = 0; i < values.length; i++) {
            if (values1[i] <= 0.00001 && values2[i] <= 0.00001) {
                values[i] = 0;
            } else if (values1[i] <= 0.00001) {
                values[i] = values2[i];
            } else if (values2[i] <= 0.00001) {
                values[i] = values1[i];
            } else {
                values[i] = getMin(values1[i], values2[i]);
            }
        }
        return values;
    }

    public static double getMax(double value1, double value2) {
        if (value1 > value2) {
            return value1;
        } else {
            return value2;
        }
    }

    public static int getMax(Integer value1, Integer value2) {
        if (value1 > value2) {
            return value1;
        } else {
            return value2;
        }
    }

    public static double getMax(double[] values, int begIdx, int endIdx) {
        if (values.length == 0 || values.length < begIdx || values.length < endIdx || endIdx < begIdx) {
            return 0.0;
        }
        double tempValue = values[begIdx];
        for (int i = begIdx + 1; i < endIdx + 1; i++) {
            if (tempValue < values[i])
                tempValue = values[i];
        }
        return tempValue;
    }

    public static double getMax(double[] values) {
        if (values.length == 0)
            return 0.0;
        double tempValue = values[0];
        for (int i = 1; i < values.length; i++) {
            if (tempValue < values[i])
                tempValue = values[i];
        }
        return tempValue;
    }

    //得到整形数的最大值
    public static int getMax(int[] values) {
        if (values.length == 0)
            return 0;
        int tempValue = values[0];
        for (int i = 1; i < values.length; i++) {
            if (tempValue < values[i])
                tempValue = values[i];
        }
        return tempValue;
    }

    public static int getMaxIndex(double[] values) {
        if (values.length == 0)
            return 0;
        int index = 0;
        for (int i = 1; i < values.length; i++) {
            if (values[i] > values[index]) {
                index = i;
            }
        }
        return index;
    }

    public static int getMaxIndex(Object values) {
        if (values == null) {
            return 0;
        }
        int index = 0;
        if (values instanceof double[]) {
            double[] vs = (double[]) values;
            if (vs.length == 0) return 0;
            for (int i = 1; i < vs.length; i++) {
                if (vs[i] > vs[index]) {
                    index = i;
                }
            }
        }
        return index;
    }


    public static int getMinIndex(Object values) {
        if (values == null) {
            return 0;
        }
        int index = 0;
        if (values instanceof double[]) {
            double[] vs = (double[]) values;
            if (vs.length == 0) return 0;
            for (int i = 1; i < vs.length; i++) {
                if (vs[i] < vs[index]) {
                    index = i;
                }
            }
        }
        return index;
    }

    public static double getMax(Array array) {
        if (Array.getLength(array) == 0 && !array.getClass().getName().equals("[D"))
            return 0.0;
        double tempValue = Array.getDouble(array, 0);
        for (int i = 1; i < Array.getLength(array); i++) {
            if (tempValue < Array.getDouble(array, i))
                tempValue = Array.getDouble(array, i);
        }
        return tempValue;
    }

    public static double getMax(Object array) {
        if (!array.getClass().isArray() || Array.getLength(array) == 0
                || !array.getClass().getName().equals("[D"))
            return 0.0;
        double tempValue = Array.getDouble(array, 0);
        for (int i = 1; i < Array.getLength(array); i++) {
            if (tempValue < Array.getDouble(array, i))
                tempValue = Array.getDouble(array, i);
        }
        return tempValue;
    }

    public static double getMax(Object array1, Object array2) {
        if (array1 == null && array2 == null) {
            return 0.0;
        } else if (array1 == null && array2 != null) {
            return getMax(array2);
        } else if (array1 != null && array2 == null) {
            return getMax(array1);
        } else if (Array.getLength(array1) == 0) {
            return getMax(array2);
        } else if (Array.getLength(array2) == 0) {
            return getMax(array1);
        } else {
            return getMax(getMax(array1), getMax(array2));
        }
    }

    public static double getSum(double value1, double value2) {
        return value1 + value2;
    }

    /**
     * 返回value1减去value2的值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static int minus(int value1, int value2) {
        return value1 - value2;
    }

    /**
     * 返回value1加上value2的值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static int plus(int value1, int value2) {
        return value1 + value2;
    }

    /**
     * 查看两列表是否有数据相等
     *
     * @param values1
     * @param values2
     * @return
     */
    public static boolean existIntersection(List<Integer> values1, List<Integer> values2) {
        if (values1 == null || values2 == null || values1.size() == 0 || values2.size() == 0)
            return false;
        for (int i = 0; i < values1.size(); i++) {
            if (values2.indexOf(values1.get(i)) > -1) {
                return true;
            }
        }
        return false;
    }

    public static double absoluteErrorSum(double[] array1, double[] array2) {
        if (array1 == null || array1 == null || array1.length != array2.length
                || array1.length == 0 || array2.length == 0) {
            return Double.MAX_VALUE;
        }
        double aes = 0.0;
        for (int i = 0; i < array1.length; i++) {
            aes += Math.abs(array1[i] - array2[i]);
        }
        return aes;
    }

    /**
     * 由value2的值按顺序填充value1,并生生成新的与value1同维数的数组
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayFill(double[] value1, double[] value2) {
        if (value1 == null)
            return null;
        double[] tempValue = new double[value1.length];
        if (value2 != null) {
            for (int i = 0; i < value2.length; i++) {
                if (i == value1.length) {
                    break;
                }
                tempValue[i] = value2[i];
            }
        }
        return tempValue;
    }

    /**
     * 数组除于一个值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayDivide(double[] value1, double value2) {
        if (value1 == null || value1.length == 0 || value2 == 0.0)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] / value2;
        }
        return tempValue;
    }

    /**
     * 数组除于一个值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayDivide(double[] value1, int value2) {
        if (value1 == null || value1.length == 0 || value2 == 0)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] / value2;
        }
        return tempValue;
    }

    /**
     * 数组除于一个数组
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayDivide(double[] value1, double[] value2) {
        if (value1 == null || value1.length == 0 || value2 == null || value2.length == 0
                || value1.length != value2.length)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            if (value2[i] != 0.0) {
                tempValue[i] = value1[i] / value2[i];
            } else {
                tempValue[i] = 0.0;
            }
        }
        return tempValue;
    }
    /**
     * 放数组后面追加一个字符串
     *
     * @param value1
     * @param value2
     * @return
     */
    public static String[] arrayAppend(String[] value1, String value2) {
        String[] tempArray = new String[value1 == null ? 1 : (value1.length + 1)];
        if(value1 != null) {
            for (int i = 0; i < value1.length; i++) {
                tempArray[i] = value1[i];
            }
        }
        tempArray[tempArray.length - 1] = value2;
        return tempArray;
    }
    /**
     * 数组加一个值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayAddition(double[] value1, double value2) {
        if (value1 == null)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] + value2;
        }
        return tempValue;
    }

    /**
     * 数组加一个值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayAddition(double[] value1, int value2) {
        if (value1 == null)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] + value2;
        }
        return tempValue;
    }

    /**
     * 数组加一个值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayAddition(double[] value1, double[] value2) {
        if (value1 == null || value2 == null || value1.length != value2.length)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] + value2[i];
        }
        return tempValue;
    }

    /**
     * 数组乘以一个值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayMultiply(double[] value1, double value2) {
        if (value1 == null || value2 == 0.0)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] * value2;
        }
        return tempValue;
    }

    public static double[] arrayMultiply(double[] value1, double[] value2) {
        if (value1 == null || value2 == null || value1.length != value2.length)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] * value2[i];
        }
        return tempValue;
    }


    /**
     * 数组相减
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayMinus(double[] value1, double[] value2) {
        if (value1 == null || value2 == null || value1.length != value2.length)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] - value2[i];
        }
        return tempValue;
    }

    /**
     * 数组减去一个数
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayMinus(double[] value1, double value2) {
        if (value1 == null)
            return value1;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = value1[i] - value2;
        }
        return tempValue;
    }

    /**
     * 数组相减
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayMinusIgnoreNegative(double[] value1, double[] value2) {
        double[] tempValue = arrayMinus(value1, value2);
        for (int i = 0; i < tempValue.length; i++) {
            if (tempValue[i] < 0) {
                tempValue[i] = 0;
            }
        }
        return tempValue;
    }

    /**
     * 求两个数据的平均值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static double[] arrayAve(double[] value1, double[] value2) {
        if (value1 == null || value2 == null || value2.length != value1.length)
            return null;
        double[] tempValue = new double[value1.length];
        for (int i = 0; i < value1.length; i++) {
            tempValue[i] = (value1[i] + value2[i]) / 2;
        }
        return tempValue;
    }

    /**
     * 返回数组中的最后一个数据
     */
    public static double getLastValue(double[] values) {
        if (values.length == 0)
            return 0.0;
        else {
            return values[values.length - 1];
        }
    }


    /**
     * 返回(Double)value1减去(Double)value2的值
     *
     * @return
     */
    public static Double minus(double value1, double value2) {
        return value1 - value2;
    }

    /**
     * 返回(Double)value1加(Double)value2的值
     *
     * @return
     */
    public static Double plus(double value1, double value2) {
        return value1 + value2;
    }

    /**
     * 返回(Double)value1除以(Double)value2的值
     *
     * @return
     */
    public static Double divide(double value1, double value2) {
        if (value2 != 0) return value1 / value2;
        else return 0.0;
    }

    /**
     * 返回(Double)value1除以(int)value2的值
     *
     * @return
     */
    public static Double divide(double value1, int value2) {
        if (value2 != 0) return value1 / value2;
        else return 0.0;
    }

    /**
     * 返回(int)value1除以(int)value2的值
     *
     * @return
     */
    public static int divide(int value1, int value2) {
        if (value2 != 0) return value1 / value2;
        else return 0;
    }

    /**
     * 判断value是否于小standard，如果小于则返回空，或者还回value
     *
     * @param value
     * @param standard
     * @return
     */
    public static Object judgeSmaller(Double value, double standard) {
        if (value == null) return null;
        if (value < standard) return null;
        else return value;
    }

    /**
     * 判断一个整形数组中是否包含一个整形元素
     *
     * @param value
     * @param valueList
     * @return
     */
    public static Boolean containElement(Integer value, int[] valueList) {
        if (value == null || valueList == null) return false;
        for (int i = 0; i < valueList.length; i++) {
            if (value == valueList[i]) {
                return true;
            }
        }
        return false;
    }

    /**
     * 合并两个数组
     *
     * @param a
     * @param b
     * @return
     */
    public static double[] concat(double[] a, double[] b) {
        final int alen = a.length;
        final int blen = b.length;
        if (alen == 0) {
            return b;
        }
        if (blen == 0) {
            return a;
        }
        double[] result = new double[alen + blen];
        for (int i = 0; i < alen; i++) {
            result[i] = a[i];
        }
        for (int i = alen; i < alen + blen; i++) {
            result[i] = b[i - alen];
        }
        return result;
    }

    public static Integer mod(int a, int b) {
        return a % b;
    }

    /**
     * 将数组Array中所有小于0的数变为0
     *
     * @param array
     * @return
     */
    public static double[] nonZero(double[] array) {
        if (array == null) return null;
        for (int i = 0; i < array.length; i++)
            if (array[i] < 0)
                array[i] = 0;
        return array;
    }

    /**
     * 取数据的绝对值
     *
     * @param array
     * @return
     */
    public static double[] abs(double[] array) {
        if (array == null) return null;
        for (int i = 0; i < array.length; i++)
            array[i] = Math.abs(array[i]);
        return array;
    }

    public static Integer multiply(int a, int b) {
        return a * b;
    }

    public static Double multiply(double a, double b) {
        return a * b;
    }

    public static Integer long2int(long l) {
        return (int) l;
    }

    /**
     * 判断value是否于小standard，如果小于则返standard，或者还回value
     * @param value
     * @param standard
     * @return
     */
	/*public static double judgeSetSmaller(Double value, double standard)
	{
		if(value == null) return standard;
		if(value < standard) return standard;
		else return value;
	}*/

    /**
     * 通过判断返回指定的值
     */
    public static Object getJudgeValue(Boolean bool, Integer value1, Integer value2) {
        if (bool == null) {
            return null;
        } else if (bool) {
            return value1;
        } else {
            return value2;
        }
    }

    /**
     * 合并两boolean数组
     *
     * @param tempValue1
     * @param tempValue2
     * @param judge      1:全部为true时为true,否者为false;
     *                   2:有一个为true时为true,否者为false
     * @return
     */
    public static boolean[] merge(boolean[] tempValue1, boolean[] tempValue2, int judge) {
        if (tempValue1 == null || tempValue2 == null || tempValue1.length != tempValue2.length) {
            return null;
        }
        boolean[] tempValue = new boolean[tempValue1.length];
        for (int i = 0; i < tempValue1.length; i++) {
            if (judge == 1) {
                tempValue[i] = tempValue1[i] && tempValue2[i];
            } else if (judge == 2) {
                tempValue[i] = tempValue1[i] || tempValue2[i];
            }
        }
        return tempValue;
    }

    /**
     * 随机产生一个在最小值和最大值范围内的双精度型数组
     *
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param num      长度
     * @return
     */
    public static double[] randDoubleArray(double minValue, double maxValue, int num) {
        double[] tempValue = new double[num];
        for (int i = 0; i < tempValue.length; i++) {
            tempValue[i] = minValue + Math.random() * (maxValue - minValue);
        }
        return tempValue;
    }

    /**
     * 随机产生一个在最小值和最大值范围内的所有值都一样的双精度型数组
     *
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param num      长度
     * @return
     */
    public static double[] randDoubleSameArray(double minValue, double maxValue, int num) {
        double[] tempValue = new double[num];
        double value = minValue + Math.random() * (maxValue - minValue);
        Arrays.fill(tempValue, value);
        return tempValue;
    }

    /**
     * 填充浮点型数据并生成一个新的数组
     *
     * @param value
     * @param num
     * @return
     */
    public static double[] fillNewDoubleArray(double value, int num) {
        double[] tempValue = new double[num];
        Arrays.fill(tempValue, value);
        return tempValue;
    }

    /**
     * 查找指定值在指定范围的索引
     *
     * @param value
     * @param ranges
     * @return
     */
    public static int searchIndexByRanges(double value, List<Double> ranges) {
        if (ranges.size() == 0) return -1;
        if (value < ranges.get(0)) return 0;
        for (int i = 1; i < ranges.size(); i++) {
            if (value >= ranges.get(i - 1) && value < ranges.get(i)) {
                return i;
            }
        }
        return ranges.size();
    }

    /**
     * 长精度数组复制
     *
     * @param array
     * @return
     */
    public static double[] doubleArrayCopy(double[] array) {
        double[] temp = new double[array.length];
        for (int i = 0; i < array.length; i++) {
            temp[i] = array[i];
        }
        return temp;
    }

    /**
     * 复制第一个数组到第二个，两数组必须相等
     * @param source
     * @param target
     */
    public static void doubleArrayCopy(double[] source, double[] target) {
        if(source == null || target == null || source.length != target.length) {
            return;
        }
        for (int i = 0; i < source.length; i++) {
            target[i] = source[i];
        }
    }

    /**
     * 复制一个范围到新的数组，两数组长度相等
     * @param source
     * @param bidx
     * @param eidx
     * @return
     */
    public static double[] doubleArrayCopy(double[] source, int bidx, int eidx) {
        double[] target = new double[source.length];
        for (int i = bidx; i <= eidx; i++) {
            target[i] = source[i];
        }
        return target;
    }

    /**
     * 把其它类型的数组转换成对象数组,供查询条件使用
     * @param array
     * @return
     */
    public static Object[] arrayToObjectArray(Object array) {
        if(array == null || !array.getClass().isArray()) {
            return new Object[0];
        }
        int length = Array.getLength(array);
        Object[] temp = new Object[length];
        for (int i = 0; i < length; i++) {
            temp[i] = Array.get(array, i);
        }
        return temp;
    }

    /**
     * 从查询的数据列表找出映射关系
     * @param tempList
     * @param key
     * @param value
     * @return
     */
    public static Map<String, String> tranMappingFromListMap(List<Map<String, Object>> tempList, String key, String value) {
        Map<String, String> tempMap = new LinkedHashMap<String, String>();
        for (int i = 0; i < tempList.size(); i++) {
            Map<String, Object> temp = tempList.get(i);
            String tempKey = (String)temp.get(key);
            String tempValue = (String)temp.get(value);
            tempMap.put(tempKey, tempValue);
        }
        return tempMap;
    }
    /**
     * 把数组传换成字符串
     * @param array
     * @return
     */
    public static String arrayToString(Object array) {
        if(array == null) return "";
        if(array.getClass().isArray()) {
            int len = Array.getLength(array);
            Object[] tempArray = new Object[len];
            for (int i = 0; i < len; i++) {
                tempArray[i] = arrayToString(Array.get(array, i));
            }
            return Arrays.toString(tempArray);
        } else {
            return array.toString();
        }
    }

    /**
     * 对零值进行插值
     * @param array
     */
    public static void insertZeroArray(double[] array) {
        // 如果数据是全零或者全不为零，则不需要处理，直接返回
        boolean allZero = true;
        boolean allNotZero = true;
        double beforeValue = array[0];
        double afterValue = array[0];
        for (int i = 0; i < array.length; i++) {
            if(array[i] != 0.0) {
                allZero = false;
                afterValue = array[i];
            }
            if(array[i] == 0.0) {
                allNotZero = false;
            }
            if(beforeValue == 0.0) {
                beforeValue = array[i];
            }
        }
        if(allZero || allNotZero) return;
        // 如果数据头部是零或尾部是零，则直接取最近的值
        boolean beforeNotZero = false;
        boolean afterNotZero = false;
        for (int i = 0; i < array.length; i++) {
            if(array[i] == 0.0 && !beforeNotZero) {
                array[i] = beforeValue;
            } else {
                beforeNotZero = true;
            }
            if(array[array.length - i - 1] == 0.0 && !afterNotZero) {
                array[array.length - i - 1] = afterValue;
            } else {
                afterNotZero = true;
            }
        }
        // 对中间的零进行插值
        double lastValue = array[0];
        int lastIdx = 0;
        double nextValue = 0.0;
        int ndexIdx = -1;
        boolean needInsert = false;
        for (int i = 1; i < array.length; i++) {
            if(!needInsert && array[i] != 0.0) {
                lastValue = array[i];
                lastIdx = i;
            } else if(needInsert && array[i] != 0.0) {
                nextValue = array[i];
                ndexIdx = i;
                for (int j = lastIdx + 1; j < i; j++) {
                    array[j] = lastValue + (nextValue - lastValue) * (j - lastIdx) / (ndexIdx - lastIdx);
                }
                needInsert = false;
            } else {
                needInsert = true;
            }
        }
    }
}
