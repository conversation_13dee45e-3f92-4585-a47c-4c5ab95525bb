package com.wcp.utils;


import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2018.7.12
 */
public class DateUtil {

    public static DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    public static DateTimeFormatter secondFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static DateTimeFormatter timeFormat = DateTimeFormatter.ofPattern("HH:mm:ss");
    public final static SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
    public final static SimpleDateFormat ymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static SimpleDateFormat longSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    /**
     * 从field字段开始后得到向后的规整时间
     *
     * @param field    需规整的时间段
     * @param calendar 需要规整的时间
     * @return
     */
    public static Calendar getFeasibleCalendar(int field, Calendar calendar) {
        if (calendar != null) {
            calendar = (Calendar) calendar.clone();
            Calendar calendar1 = Calendar.getInstance();
            calendar1.set(Calendar.MILLISECOND, 0);
            calendar1.clear();
            if (field == Calendar.YEAR) {
                calendar1.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
            } else if (field == 0) {
                int month = calendar.get(Calendar.MONTH);
                if (0 <= month && month < 3) {
                    calendar1.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
                } else if (3 <= month && month < 6) {
                    calendar1.set(calendar.get(Calendar.YEAR), 3, 1, 0, 0, 0);
                } else if (6 <= month && month < 9) {
                    calendar1.set(calendar.get(Calendar.YEAR), 6, 1, 0, 0, 0);
                } else if (9 <= month && month < 12) {
                    calendar1.set(calendar.get(Calendar.YEAR), 9, 1, 0, 0, 0);
                }
            } else if (field == 17) {
                int month = calendar.get(Calendar.MONTH);
                if (0 <= month && month < 3) {
                    calendar1.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
                } else if (3 <= month && month < 6) {
                    calendar1.set(calendar.get(Calendar.YEAR), 3, 1, 0, 0, 0);
                } else if (6 <= month && month < 9) {
                    calendar1.set(calendar.get(Calendar.YEAR), 6, 1, 0, 0, 0);
                } else if (9 <= month && month < 12) {
                    calendar1.set(calendar.get(Calendar.YEAR), 9, 1, 0, 0, 0);
                }
            } else if (field == Calendar.MONTH) {
                calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
            } else if (field == 18) {
                int day = calendar.get(Calendar.DAY_OF_MONTH);
                if (day < 11) {
                    calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
                } else if (day > 20) {
                    calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 21, 0, 0, 0);
                } else {
                    calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 11, 0, 0, 0);
                }

            } else if (field == Calendar.WEEK_OF_YEAR) {
                calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
                //如果是星期天必须把该周归位上一周
                if (calendar1.get(Calendar.DAY_OF_WEEK) == 1) {
                    calendar1.add(Calendar.WEEK_OF_YEAR, -1);
                }
                //把时间调整到周一做为起始时间
                calendar1.set(Calendar.DAY_OF_WEEK, 2);
            } else if (field == Calendar.DAY_OF_MONTH) {
                calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
            } else if (field == Calendar.HOUR_OF_DAY) {
                calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), calendar.get(Calendar.HOUR_OF_DAY), 0, 0);
            } else if (field == Calendar.MINUTE) {
                calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                        calendar.get(Calendar.DAY_OF_MONTH), calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), 0);
            } else if (field == Calendar.SECOND) {
                calendar1.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                        calendar.get(Calendar.DAY_OF_MONTH), calendar.get(Calendar.HOUR_OF_DAY),
                        calendar.get(Calendar.MINUTE), calendar.get(Calendar.SECOND));
            } else {
                calendar1 = calendar;
            }
            return calendar1;
        } else {
            return null;
        }
    }

    /**
     * 获取前几天的开始时间
     *
     * @param calendar 时间
     * @return
     */
    public static Calendar getStartOfDay(Calendar calendar, int day) {
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - day);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar;
    }

    /**
     * 获取前几天的结束时间
     *
     * @param calendar 时间
     * @return
     */
    public static Calendar getEndOfDay(Calendar calendar, int day) {
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - day);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar;
    }

    /**
     * 比较时间是不是在两个时间范围内的
     *
     * @param nowTime   当前时间
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public static boolean belongCalendar(Calendar nowTime, Calendar beginTime, Calendar endTime) {
        return nowTime.after(beginTime) && nowTime.before(endTime) || beginTime.equals(nowTime) || endTime.equals(nowTime);
    }

    public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
        return nowTime.after(beginTime) && nowTime.before(endTime) || beginTime.equals(nowTime) || endTime.equals(nowTime);
    }

    /**
     * 前几个小时
     *
     * @return
     */
    public static Calendar getCalendarBeforeByHours(Calendar d, int hours) {
        d.set(Calendar.HOUR, d.get(Calendar.HOUR) - hours);
        return d;
    }

    /**
     * 后几个小时的数据
     *
     * @return
     */
    public static Calendar getDateAfterByHours(Calendar d, int hours) {
        d.set(Calendar.HOUR, d.get(Calendar.HOUR) + hours);
        return d;
    }

    /**
     * 获取小时的开始时间
     *
     * @return
     */
    public static Calendar getHoursStart(Calendar d) {
        String s = calendarToString(d, "yyyy-MM-dd HH");
        return stringToCalendar(s + ":00:00");
    }

    /**
     * 获取小时的结束时间
     *
     * @return
     */
    public static Calendar getHoursEnd(Calendar d) {
        String s = calendarToString(d, "yyyy-MM-dd HH");
        return stringToCalendar(s + ":59:59");
    }


    /**
     * 得到几天前的时间
     *
     * @param d
     * @param day
     * @return
     */
    public static Calendar getCalendarBeforeByDay(Calendar d, int day) {
        d.set(Calendar.DATE, d.get(Calendar.DATE) - day);
        return d;
    }

    /**
     * 得到几天后的时间
     *
     * @param d
     * @param day
     * @return
     */
    public static Calendar getDateAfterByDay(Calendar d, int day) {
        d.set(Calendar.DATE, d.get(Calendar.DATE) + day);
        return d;
    }

    /**
     * 转换不同时段类型的长度
     *
     * @param periodType         转换之前时段类型
     * @param periodTypeNum      转换之前时段类型长度
     * @param periodNum          转换之前时段长度
     * @param intervalPeriodType 转换目的时段类型
     * @param discardExtraTime   是否舍弃多余不完整时段
     * @return
     */
    public static int getIntervalSpan(int periodType, int periodTypeNum, int periodNum, int intervalPeriodType, boolean discardExtraTime) {
        int tempSecond = periodNumToSecond(periodNum, periodType, periodTypeNum);
        return secondToPeriodNum(intervalPeriodType, 1, tempSecond, discardExtraTime);
    }

    public static int getIntervalSpan(int periodType, int periodTypeNum, Calendar btime, Calendar etime) {
        int tempSecond = periodNumToSecond(1, periodType, periodTypeNum);
        int span = (int) ((etime.getTimeInMillis() - btime.getTimeInMillis()) / 1000 / tempSecond);
        int tempSpan = span - 1;
        if (tempSpan < 0) tempSpan = 0;
        Calendar tempTime = btime;
        while (tempTime.compareTo(etime) < 0) {
            tempTime = add(periodType, ++tempSpan * periodTypeNum, btime);
        }
        return tempSpan;
    }

    public static int getIntervalSpanContainEnd(int periodType, int periodTypeNum, Calendar btime, Calendar etime) {
        if (btime.after(etime)) return 0;
        Calendar tempTime = btime;
        int tempSpan = 0;
        while (tempTime.compareTo(etime) <= 0) {
            tempTime = add(periodType, periodTypeNum, tempTime);
            tempSpan++;
        }
        return tempSpan;
    }

    /**
     * 为给定的日历字段添加或减去指定的时间量
     *
     * @param periodType 为给定的日历字段
     * @param calendar   添加或减去前的原始时间
     * @return 重新创建的Calendar类型
     * //18==旬
     * //5==天
     * //1==年
     * //2==月
     * //11=小时
     */
    public static Calendar add(int periodType, int amount, Calendar calendar) {

        Calendar calendar1 = Calendar.getInstance();
        calendar1.clear();
        calendar1 = (Calendar) calendar.clone();
        if (periodType == 18) {
            // 添加或减去的月数
            int numberMonth = amount / 3;
            // 还剩余多少旬要添加或减去
            int numberXun = amount % 3;
            calendar1.add(Calendar.MONTH, numberMonth);
            if (numberXun > 0) {
                // 1到21号加10天就行
                if (calendar1.get(Calendar.DAY_OF_MONTH) > 0 && calendar1.get(Calendar.DAY_OF_MONTH) < 21) {
                    calendar1.add(Calendar.DAY_OF_MONTH, 10);
                } else {
                    int lastXunDays = calendar1.getActualMaximum(Calendar.DAY_OF_MONTH) - 20;
                    calendar1.add(Calendar.DAY_OF_MONTH, lastXunDays);
                }

            }
            if (numberXun > 1) {
                // 1到21号加10天就行
                if (calendar1.get(Calendar.DAY_OF_MONTH) > 0 && calendar1.get(Calendar.DAY_OF_MONTH) < 21) {
                    calendar1.add(Calendar.DAY_OF_MONTH, 10);
                } else {
                    int lastXunDays = calendar1.getActualMaximum(Calendar.DAY_OF_MONTH) - 20;
                    calendar1.add(Calendar.DAY_OF_MONTH, lastXunDays);
                }
            }
            if (numberXun < 0) {
                // 1到21号加10天就行
                if (calendar1.get(Calendar.DAY_OF_MONTH) > 10) {
                    calendar1.add(Calendar.DAY_OF_MONTH, -10);
                } else {
                    // 减旬时，应该用上一个旬的天数
                    Calendar tempCalendar = (Calendar) calendar1.clone();
                    tempCalendar.add(Calendar.MONTH, -1);
                    int lastXunDays = 20 - tempCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                    calendar1.add(Calendar.DAY_OF_MONTH, lastXunDays);
                }

            }
            if (numberXun < -1) {
                // 1到21号加10天就行
                if (calendar1.get(Calendar.DAY_OF_MONTH) > 10) {
                    calendar1.add(Calendar.DAY_OF_MONTH, -10);
                } else {
                    // 减旬时，应该用上一个旬的天数
                    Calendar tempCalendar = (Calendar) calendar1.clone();
                    tempCalendar.add(Calendar.MONTH, -1);
                    int lastXunDays = 20 - tempCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                    calendar1.add(Calendar.DAY_OF_MONTH, lastXunDays);
                }
            }
        } else {
            calendar1.add(periodType, amount);
        }
        return calendar1;
    }

    /**
     * 为给定的日历字段設置指定的时间量
     *
     * @param periodType 为给定的日历字段
     * @param calendar   添加或减去前的原始时间
     * @return 重新创建的Calendar类型
     */
    public static Calendar set(int periodType, int value, Calendar calendar) {
        Calendar calendar1 = (Calendar) calendar.clone();
        if (periodType == 18) {
            // 还剩余多少旬要添加或减去
            int numberDay = value % 3 * 10 + 1;
            calendar1.set(Calendar.DAY_OF_MONTH, numberDay);
        } else {
            calendar1.set(periodType, value);
        }
        return calendar1;
    }


    /**
     * String字符串往Calendar转换
     *
     * @param timeStr
     * @return
     */
    public static Calendar stringToCalendar(String timeStr) {
        if (!StringUtil.isNullOrEmpty(timeStr)) {
            SimpleDateFormat formatter = null;
            if (timeStr.contains("-") || timeStr.trim().length() == 4) {
                switch (timeStr.trim().length()) {
                    case 19:
                        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        break;
                    case 13:
                        formatter = new SimpleDateFormat("yyyy-MM-dd HH");
                        break;
                    case 16:
                        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                        break;
                    case 10:
                        formatter = new SimpleDateFormat("yyyy-MM-dd");
                        break;
                    case 7:
                        formatter = new SimpleDateFormat("yyyy-MM");
                        break;
                    case 4:
                        formatter = new SimpleDateFormat("yyyy");
                        break;
                    default:
                        break;
                }
            } else if (timeStr.contains("/")) {
                switch (timeStr.trim().length()) {
                    case 19:
                        formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                        break;
                    case 13:
                        formatter = new SimpleDateFormat("yyyy/MM/dd HH");
                        break;
                    case 16:
                        formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm");
                        break;
                    case 10:
                        formatter = new SimpleDateFormat("yyyy/MM/dd");
                        break;
                    case 7:
                        formatter = new SimpleDateFormat("yyyy/MM");
                        break;
                    case 4:
                        formatter = new SimpleDateFormat("yyyy");
                        break;
                    default:
                        break;
                }
            }
            try {
                Calendar calendar = Calendar.getInstance();
                Date date = formatter.parse(timeStr.trim());
                calendar.setTime(date);
                return calendar;
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * String字符串往Calendar转换
     *
     * @param time
     * @param format
     * @return
     */
    public static Calendar stringToCalendar(String time, String format) {
        //	if(time.equals("null") || format == null  ||  time.equals(""))
        //return null;
        if (time != null && !time.equals("") && format != null && !time.equals("null")) {
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            Calendar calendar = Calendar.getInstance();
            try {
                Date bdate = formatter.parse(time.trim());
                calendar.setTime(bdate);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return calendar;
        } else {
            return null;
        }

    }

    /**
     * String字符串往Date转换
     *
     * @param time
     * @param format
     * @return
     */
    public static Date stringToDate(String time, String format) {
        if (time == null || format == null) return null;
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        try {
            Date bdate = formatter.parse(time);
            return bdate;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * String字符串往Timestamp转换
     *
     * @param time
     * @param format
     * @return
     */
    public static Timestamp stringToTimestamp(String time, String format) {
        if (time == null || format == null) return null;
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        try {
            Date bdate = formatter.parse(time);
            Timestamp timestamp = new Timestamp(bdate.getTime());
            return timestamp;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * String字符串往LocalDate转换
     *
     * @param time
     * @param format
     * @return
     */
    public static LocalDate stringToLocalDate(String time, String format) {
        if (time == null || format == null) return null;
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return LocalDate.parse(time, df);
    }

    /**
     * String字符串往LocalDateTime转换
     *
     * @param time
     * @param format
     * @return
     */
    public static LocalDateTime stringToLocalDateTime(String time, String format) {
        if (time == null || format == null) return null;
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(time, df);
    }

    /**
     * String字符串往LocalTime转换
     *
     * @param time
     * @param format
     * @return
     */
    public static LocalTime stringToLocalTime(String time, String format) {
        if (time == null || format == null) return null;
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return LocalTime.parse(time, df);
    }

    /**
     * 旬字符串往Calendar转换
     */
    public static Calendar xunToCalendar(String time) {
        String str = "";
        String year = time.substring(0, time.indexOf("年"));
        String month = time.substring(time.indexOf("年") + 1, time.indexOf("月"));
        String xun = time.substring(time.indexOf("月") + 1);
        str = year + "-" + month + "-";
        if ("上旬".equals(xun)) {
            str += 1;
        } else if ("中旬".equals(xun)) {
            str += 11;
        } else if ("下旬".equals(xun)) {
            str += 21;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        try {
            Date bdate = formatter.parse(str);
            calendar.setTime(bdate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return calendar;
    }

    /**
     * Calendar往字符串String转换
     *
     * @param time
     * @param format
     * @return
     */
    public static String calendarToString(Calendar time, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(time.getTime());
    }

    /**
     * Calendar往字符串String转换
     *
     * @param time
     * @return
     */
    public static String calendarToString(Calendar time) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(time.getTime());
    }


    public static String calendarToCHNString(Calendar time, int type) {
        String pattern = "";
        if (type == Calendar.YEAR) {
            pattern = "yyyy年";
        } else if (type == Calendar.MONTH) {
            pattern = "yyyy年MM月";
        } else if (type == 18) {
            int day = time.get(Calendar.DAY_OF_MONTH);
            int year = time.get(Calendar.YEAR);
            int month = time.get(Calendar.MONTH);
            String formatStr = year + "年" + (month + 1) + "月";
            if (day <= 10) {
                formatStr += "上旬";
            } else if (day <= 20) {
                formatStr += "中旬";
            } else {
                formatStr += "下旬";
            }
            return formatStr;
        } else if (type == Calendar.DAY_OF_MONTH) {
            pattern = "yyyy年MM月dd日";
        } else if (type == Calendar.HOUR_OF_DAY) {
            pattern = "yyyy年MM月dd日HH时";
        } else {
            pattern = "yyyy年MM月dd日HH时mm分";
        }
        DateFormat formatter = new DateFormat(pattern);
        return formatter.format(time.getTime());
    }

    public static String calendarToString(Calendar time, int type) {
        String pattern = "";
        if (type == Calendar.YEAR) {
            pattern = "yyyy年";
        } else if (type == Calendar.MONTH) {
            pattern = "yyyy年MM月";
        } else if (type == 18) {
            pattern = "yyyy年MM月XX旬";
        } else if (type == Calendar.DAY_OF_MONTH) {
            pattern = "yyyy年MM月dd日";
        } else if (type == Calendar.HOUR_OF_DAY) {
            pattern = "yyyy年MM月dd日HH时";
        } else {
            pattern = "yyyy年MM月dd日HH时mm分";
        }
        DateFormat formatter = new DateFormat(pattern);
        return formatter.format(time.getTime());
    }

    public static String calendarToString2(Calendar time, int type) {
        String pattern = "";
        if (type == Calendar.YEAR) {
            pattern = "yyyy";
        } else if (type == Calendar.MONTH) {
            pattern = "yyyy-MM";
        } else if (type == 18) {
            pattern = "yyyy-MM-dd";
        } else if (type == Calendar.DAY_OF_MONTH) {
            pattern = "yyyy-MM-dd";
        } else if (type == Calendar.HOUR_OF_DAY) {
            pattern = "yyyy-MM-dd HH";
        } else {
            pattern = "yyyy-MM-dd HH:mm";
        }
        DateFormat formatter = new DateFormat(pattern);
        return formatter.format(time.getTime());
    }

    public static void main(String[] args) {
        Calendar time3 = stringToCalendar("2021年1月上旬", 18);
        System.out.println(time3.getTime());
        Calendar time4 = stringToCalendar("2021年1月1日", Calendar.DAY_OF_MONTH);
        System.out.println(time4.getTime());
        Calendar time5 = stringToCalendar("2021年1月1日1时", Calendar.HOUR_OF_DAY);
        System.out.println(time5.getTime());
    }

    public static Calendar stringToCalendar(String timeStr, int type) {
        String pattern = "";
        if (type == Calendar.YEAR) {
            pattern = "yyyy";
        } else if (type == Calendar.MONTH) {
            pattern = "yyyy-MM";
        } else if (type == 18) {
            pattern = "yyyy-MM-dd";
        } else if (type == Calendar.DAY_OF_MONTH) {
            pattern = "yyyy-MM-dd";
        } else if (type == Calendar.HOUR_OF_DAY) {
            pattern = "yyyy-MM-dd HH";
        } else {
            pattern = "yyyy-MM-dd HH:mm";
        }
        if (!StringUtil.isNullOrEmpty(timeStr)) {
            if (StringUtil.isContainChinese(timeStr)) {
                StringBuffer sb = new StringBuffer();
                // 用于校验是否为中文
                boolean flag = false;
                char chinese = 0;
                char[] charArray = timeStr.toCharArray();
                // 过滤到中文及中文字符
                for (int i = 0; i < charArray.length; i++) {
                    chinese = charArray[i];
                    flag = StringUtil.isChinese(chinese);
                    if (!flag) {// 不是中日韩文字及标点符号
                        sb.append(chinese);
                    } else {
                        switch (chinese) {
                            case '上':
                                sb.append("01");
                                break;
                            case '中':
                                sb.append("11");
                                break;
                            case '下':
                                sb.append("21");
                                break;
                            case '日':
                                sb.append(" ");
                                break;
                            default:
                                sb.append("-");
                                break;
                        }
                    }
                }
                timeStr = sb.toString();
                timeStr = timeStr.trim();
                if (timeStr.endsWith("-")) {
                    timeStr = timeStr.substring(0, timeStr.length() - 1);
                }
            }
            SimpleDateFormat formatter = new SimpleDateFormat(pattern);
            Calendar calendar = Calendar.getInstance();
            try {
                Date date = formatter.parse(timeStr.trim());
                calendar.setTime(date);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return calendar;
        }
        return null;
    }

    /**
     * Calendar往字符串String转换
     *
     * @param time
     * @param format
     * @return
     */
    public static String dateToString(Date time, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(time);
    }

    /**
     * Timestamp往字符串String转换
     *
     * @param time
     * @param format
     * @return
     */
    public static String timestampString(Timestamp time, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(new Date(time.getTime()));
    }

    /**
     * LocalDate往字符串String转换
     *
     * @param time
     * @param format
     * @return
     */
    public static String localDateToString(LocalDate time, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return formatter.format(time);
    }

    /**
     * LocalDateTime往字符串String转换
     *
     * @param time
     * @param format
     * @return
     */
    public static String localDateTimeToString(LocalDateTime time, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return formatter.format(time);
    }

    /**
     * LocalTime往字符串String转换
     *
     * @param time
     * @param format
     * @return
     */
    public static String localTimeToString(LocalTime time, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return formatter.format(time);
    }

    /**
     * 过滤转换时间类型
     *
     * @param time
     * @return
     */
    public static Object timeToString(Object time) {
        String secondFormat = "yyyy-MM-dd HH:mm:ss";
        String timeFormat = "HH:mm:ss";
        if (time instanceof Calendar) {
            return calendarToString((Calendar) time, secondFormat);
        } else if (time instanceof Date) {
            return dateToString((Date) time, secondFormat);
        } else if (time instanceof java.sql.Date) {
            return dateToString((java.sql.Date) time, secondFormat);
        } else if (time instanceof Timestamp) {
            return timestampString((Timestamp) time, secondFormat);
        } else if (time instanceof LocalDate) {
            return localDateToString((LocalDate) time, secondFormat);
        } else if (time instanceof LocalDateTime) {
            return localDateTimeToString((LocalDateTime) time, secondFormat);
        } else if (time instanceof LocalTime) {
            return localTimeToString((LocalTime) time, timeFormat);
        }
        return time;
    }

    /**
     * 过滤转换时间类型
     *
     * @param time
     * @return
     */
    public static String timeToString2(Object time, String timeFormat) {
        if (time instanceof Calendar) {
            return calendarToString((Calendar) time, timeFormat);
        } else if (time instanceof Date) {
            return dateToString((Date) time, timeFormat);
        } else if (time instanceof java.sql.Date) {
            return dateToString((java.sql.Date) time, timeFormat);
        } else if (time instanceof Timestamp) {
            return timestampString((Timestamp) time, timeFormat);
        } else if (time instanceof LocalDate) {
            return localDateToString((LocalDate) time, timeFormat);
        } else if (time instanceof LocalDateTime) {
            return localDateTimeToString((LocalDateTime) time, timeFormat);
        } else if (time instanceof LocalTime) {
            return localTimeToString((LocalTime) time, timeFormat);
        }
        return null;
    }

    /**
     * 过滤转换时间类型
     *
     * @param time
     * @return
     */
    public static Object stringToTime(Object time, Class<?> clazz) {
        String secondFormat = "yyyy-MM-dd HH:mm:ss";
        String timeFormat = "HH:mm:ss";
        if (time != null && time.getClass().equals(String.class)) {
            if (time.toString().trim().length() < 11) {
                secondFormat = "yyyy-MM-dd";
            } else if (time.toString().trim().length() < 14) {
                secondFormat = "yyyy-MM-dd HH";
            } else if (time.toString().trim().length() < 17) {
                secondFormat = "yyyy-MM-dd HH:mm";
            }
            if (Calendar.class.equals(clazz)) {
                return stringToCalendar((String) time, secondFormat);
            } else if (Date.class.equals(clazz)) {
                return stringToDate((String) time, secondFormat);
            } else if (java.sql.Date.class.equals(clazz)) {
                Date tempDate = stringToDate((String) time, secondFormat);
                return new java.sql.Date(tempDate.getTime());
            } else if (Timestamp.class.equals(clazz)) {
                return stringToTimestamp((String) time, secondFormat);
            } else if (LocalDate.class.equals(clazz)) {
                return stringToLocalDate((String) time, secondFormat);
            } else if (LocalDateTime.class.equals(clazz)) {
                return stringToLocalDateTime((String) time, secondFormat);
            } else if (LocalTime.class.equals(clazz)) {
                return stringToLocalTime((String) time, timeFormat);
            } else {
                return time;
            }
        } else {
            return time;
        }
    }

    /**
     * 秒数转换成周、小时、日、分钟、秒代表的时段数
     *
     * @param periodType       时段类型
     * @param periodTypeNum    时段类型数
     * @param secondNum        秒数
     * @param discardExtraTime 是否舍弃多余不完整时段
     * @return
     */
    public static int secondToPeriodNum(int periodType, int periodTypeNum, int secondNum, boolean discardExtraTime) {
        int periodTypeSpan = periodNumToSecond(1, periodType, periodTypeNum);
        int periodNum = secondNum / periodTypeSpan;
        if (!discardExtraTime) {
            periodNum += secondNum % periodTypeSpan > 0 ? 1 : 0;
        }
        return periodNum;
    }

    /**
     * 周、小时、日、分钟、秒转换成秒
     *
     * @param periodNum     时段长度
     * @param periodType    时段类型
     * @param periodTypeNum 时段类型数
     * @return
     */
    public static int periodNumToSecond(int periodNum, int periodType, int periodTypeNum) {
        if (periodType == Calendar.WEEK_OF_YEAR) {
            return periodNum * periodTypeNum * 3600 * 24 * 7;
        } else if (periodType == Calendar.DAY_OF_MONTH) {
            return periodNum * periodTypeNum * 3600 * 24;
        } else if (periodType == Calendar.HOUR_OF_DAY) {
            return periodNum * periodTypeNum * 3600;
        } else if (periodType == Calendar.MINUTE) {
            return periodNum * periodTypeNum * 60;
        } else if (periodType == Calendar.SECOND) {
            return periodNum * periodTypeNum;
        } else {
            return periodNum;
        }
    }

    /**
     * 小时转换成小时、日、分钟、秒
     *
     * @param hourNum       小时数
     * @param periodType    时段类型
     * @param periodTypeNum 时段类型数
     * @return
     */
    public static int hourToPeriodNum(int periodType, int periodTypeNum, int hourNum) {
        int periodTypeSpan = periodNumToSecond(1, periodType, periodTypeNum);
        return hourNum / periodTypeSpan + (hourNum % periodTypeSpan > 0 ? 1 : 0);
    }

    /**
     * 小时、日、分钟、秒转换成小时
     *
     * @param periodNum     时段数
     * @param periodType    时段类型
     * @param periodTypeNum 时段类型数
     * @return
     */
    public static int periodNumToHour(int periodNum, int periodType, int periodTypeNum) {
        if (periodType == Calendar.HOUR_OF_DAY) {
            return periodNum * periodTypeNum;
        } else if (periodType == Calendar.DAY_OF_MONTH) {
            return periodNum * periodTypeNum * 24;
        } else if (periodType == Calendar.MINUTE) {
            return periodNum * periodTypeNum / 60 + periodNum * periodTypeNum % 60 > 0 ? 1 : 0;
        } else if (periodType == Calendar.SECOND) {
            return periodNum * periodTypeNum / 3600 + periodNum * periodTypeNum % 3600 > 0 ? 1 : 0;
        } else {
            return periodNum;
        }
    }

    public static int getTenDays(Calendar calendar) {
        int num = (calendar.get(Calendar.MONTH)) * 3;
        if (calendar.get(Calendar.DAY_OF_MONTH) < 11) {
            num += 1;
        } else if (calendar.get(Calendar.DAY_OF_MONTH) < 21) {
            num += 2;
        } else {
            num += 3;
        }
        return num;
    }

    public static int getXun(Calendar calendar) {
        if (calendar.get(Calendar.DAY_OF_MONTH) < 11) {
            return 1;
        } else if (calendar.get(Calendar.DAY_OF_MONTH) < 21) {
            return 2;
        } else {
            return 3;
        }
    }

    public static int getWeek(Calendar calendar) {
        int dayOfYear = calendar.get(Calendar.DAY_OF_YEAR);
        Calendar tempTime = (Calendar) calendar.clone();
        tempTime.set(tempTime.get(Calendar.YEAR), 0, 1, 0, 0, 0);
        int dayOfWeek = tempTime.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek >= 2 && dayOfWeek <= 5) {
            dayOfYear += dayOfWeek - 2;
        } else if (dayOfWeek == 1) {
            dayOfYear -= dayOfWeek;
        } else {
            dayOfYear -= 9 - dayOfWeek;
        }
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int tempRemainder = (dayOfYear - 1) % 7;
        if (month == 11
                && ((day == 29 && tempRemainder < 1)
                || (day == 30 && tempRemainder < 2)
                || (day == 31 && tempRemainder < 3))) {
            return 1;
        }
        //有些天落在上一个年份
        if (dayOfYear < 1) {
            tempTime.add(Calendar.DAY_OF_MONTH, -1);
            return getWeek(tempTime);
        }
        return (dayOfYear - 1) / 7 + 1;
    }

    public static int getDayOfWeek(Calendar calendar) {
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (dayOfWeek == 0) dayOfWeek = 7;
        return dayOfWeek;
    }

    /**
     * 返回结束时间到开始时间的周数(包含结束时间)
     *
     * @param bTime 开始时间
     * @param eTime 结束时间
     * @return
     */
    public static int getWeekNum(Calendar bTime, Calendar eTime) {
        int weeks = 0;
        Calendar tempTime = getFeasibleCalendar(Calendar.WEEK_OF_YEAR, bTime);
        while (tempTime.compareTo(eTime) <= 0) {
            weeks++;
            tempTime.add(Calendar.WEEK_OF_YEAR, 1);
        }
        return weeks;
    }



    public static void setWeek(Calendar calendar, int week) {
        Calendar tempTime = (Calendar) calendar.clone();
        tempTime.set(tempTime.get(Calendar.YEAR), 0, 1, 0, 0, 0);
        int dayOfWeek = getDayOfWeek(tempTime);
        int setDay = week * 7 - 6;
        int bWeek = getWeek(tempTime);
        if (bWeek == 1) {
            setDay -= dayOfWeek - 1;
        } else {
            setDay += 8 - dayOfWeek;
        }
        calendar.set(Calendar.DAY_OF_YEAR, setDay);
    }





    public static int getActualMaximum(int field, Calendar calendar) {
        if (field == Calendar.WEEK_OF_YEAR) {
            calendar = (Calendar) calendar.clone();
            calendar.set(Calendar.MONTH, 11);
            calendar.set(Calendar.DAY_OF_MONTH, 31);
            int maxWeek = getWeek(calendar);
            if (maxWeek == 1) {
                calendar.add(Calendar.WEEK_OF_YEAR, -1);
                return getWeek(calendar);
            } else {
                return maxWeek;
            }
        } else {
            return calendar.getActualMaximum(field);
        }
    }

    public static LocalDateTime date2LocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    public static LocalDate date2LocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalDate();
    }

    public static Calendar date2Calendar(Date date) {
        Calendar tempTime = Calendar.getInstance();
        tempTime.setTime(date);
        return tempTime;
    }

    public static LocalTime date2LocalTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalTime();
    }

    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static Date localDate2Date(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static Date localTime2Date(LocalDate localDate, LocalTime localTime) {
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static Calendar localDateTime2Calendar(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        Calendar sysdate = Calendar.getInstance();
        sysdate.setTime(Date.from(instant));
        return sysdate;
    }




    /**
     * 获取多个时间的组合标志
     *
     * @param time
     * @return
     */
    public static String getTimeMark(Calendar... time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("YYYY-MM-dd HH:mm:ss");
        String str = "";
        for (int i = 0; i < time.length; i++) {
            str += (i > 0 ? ";" : "") + dateFormat.format(time[i].getTime());
        }
        return str;
    }

    /**
     * 时间戳往Calendar转换
     *
     * @param time
     * @return
     */
    public static Calendar longToCalendar(Long time) {
        // String format="YYYY-MM-dd HH:mm:ss";
        //	if(time.equals("null") || format == null  ||  time.equals(""))
        //return null;
        if (time != null && !time.equals("") && format != null && !time.equals("null")) {
            //SimpleDateFormat formatter = new SimpleDateFormat(format);
            Calendar calendar = Calendar.getInstance();
            try {
                Date date = new Date(time);
                //Date bdate = formatter.parse(time);
                calendar.setTime(date);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return calendar;
        } else {
            return null;
        }

    }

    /**
     * 字符串转时间类型，模糊识别
     *
     * @param tempTimeStr
     * @return
     * @throws ParseException
     */
    public static Calendar fuzzyStringToCalendar(String tempTimeStr) throws ParseException {
        if (tempTimeStr == null || tempTimeStr.trim().equals("")) {
            throw new ParseException("查询出错。", 0);
        }
        Calendar tempTime = Calendar.getInstance();
        String[] timeStrArr = tempTimeStr.split("[\\D]+");
        int[] setPeriodTypeArr = new int[]{Calendar.YEAR, Calendar.MONTH, Calendar.DAY_OF_MONTH,
                Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND};
        int setLength = timeStrArr.length;
        if (setLength > setPeriodTypeArr.length) {
            setLength = setPeriodTypeArr.length;
        }
        for (int i = 0; i < setLength; i++) {
            tempTime.set(setPeriodTypeArr[i], Integer.parseInt(timeStrArr[i]));
        }
        int lastInt = setPeriodTypeArr[setLength - 1];
        return getFeasibleCalendar(lastInt, tempTime);
    }

    /**
     * 字符串转时间类型，模糊识别
     *
     * @param tempTimeStr
     * @return
     * @throws ParseException
     */
    public static Date fuzzyStringToDate(String tempTimeStr) throws ParseException {
        if (tempTimeStr == null || tempTimeStr.trim().equals("")) {
            throw new ParseException("查询出错。", 0);
        }
        Calendar tempTime = Calendar.getInstance();
        String[] timeStrArr = tempTimeStr.split("[\\D]+");
        int[] setPeriodTypeArr = new int[]{Calendar.YEAR, Calendar.MONTH, Calendar.DAY_OF_MONTH,
                Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND};
        int setLength = timeStrArr.length;
        if (setLength > setPeriodTypeArr.length) {
            setLength = setPeriodTypeArr.length;
        }
        for (int i = 0; i < setLength; i++) {
            tempTime.set(setPeriodTypeArr[i], Integer.parseInt(timeStrArr[i]));
        }
        int lastInt = setPeriodTypeArr[setLength - 1];
        return getFeasibleCalendar(lastInt, tempTime).getTime();
    }


    //将Date转换为String
    public static String dateToString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);
    }

    /**
     * 获得本月的开始时间
     *
     * @return
     */
    public static Date getMonthStartTime(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        Date dt = null;
        try {
            c.set(Calendar.DATE, 1);
            dt = shortSdf.parse(shortSdf.format(c.getTime()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dt;
    }

    /**
     * 本月的结束时间
     *
     * @return
     */
    public static Date getMonthEndTime(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        Date dt = null;
        try {
            c.set(Calendar.DATE, 1);
            c.add(Calendar.MONTH, 1);
            c.add(Calendar.DATE, -1);
            dt = longSdf.parse(shortSdf.format(c.getTime()) + " 23:59:59.999");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dt;
    }


    /**
     * 获取月旬 三旬: 上旬1-10日 中旬11-20日 下旬21-31日
     *
     * @param date
     * @return
     */
    public static int getTenDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int i = c.get(Calendar.DAY_OF_MONTH);
        if (i < 11)
            return 1;
        else if (i < 21)
            return 2;
        else
            return 3;
    }

    /**
     * 获取所属旬开始时间
     *
     * @param date
     * @return
     */
    public static Date getTenDayStartTime(Date date) {
        int ten = getTenDay(date);
        try {
            if (ten == 1) {
                return getMonthStartTime(date);
            } else if (ten == 2) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-11");
                return shortSdf.parse(df.format(date));
            } else {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-21");
                return shortSdf.parse(df.format(date));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取所属旬结束时间
     *
     * @param date
     * @return
     */
    public static Date getTenDayEndTime(Date date) {
        int ten = getTenDay(date);
        try {
            if (ten == 1) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-10 23:59:59.999");
                return longSdf.parse(df.format(date));
            } else if (ten == 2) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-20 23:59:59.999");
                return longSdf.parse(df.format(date));
            } else {
                return getMonthEndTime(date);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 统计两个时间的时间差
     * 相差几秒几毫秒
     */
    public static String getDistanceTime(Calendar calendar1, Calendar Calendar2) {
        long timeInMillis1 = calendar1.getTimeInMillis();
        long timeInMillis2 = Calendar2.getTimeInMillis();
        long day = 0;//天数差
        long hour = 0;//小时数差
        long min = 0;//分钟数差
        long second = 0;//秒数差
        long diff = timeInMillis2 - timeInMillis1;//秒数差
        String result = null;
        try {
//            day = diff / (24 * 60 * 60 * 1000);
//            hour = (diff / (60 * 60 * 1000) - day * 24);
//            min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
            second = diff / 1000;
//            System.out.println("day="+day+" hour="+hour+" min="+min+" ss="+second%60+" SSS="+diff%1000);
            result = second % 60 + "";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static long getDistanceSecondsTime(Calendar calendar1, Calendar Calendar2) {
        long timeInMillis1 = calendar1.getTimeInMillis();
        long timeInMillis2 = Calendar2.getTimeInMillis();
        long second = 0;//秒数差
        long diff = timeInMillis2 - timeInMillis1;//秒数差
        long result = 1;
        try {
            second = diff / 1000;
            result = second % 60;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取昨天当前时刻的数据
     *
     * @return
     */
    public static String getLastDataString() {
        //获取当前日期
        Date date = new Date();
        //将时间格式化成yyyy-MM-dd HH:mm:ss的格式
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建Calendar实例
        Calendar cal = Calendar.getInstance();
        //设置当前时间
        cal.setTime(date);
        cal.add(Calendar.DATE, -1);
        return DateUtil.dateToString(cal.getTime(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获得当天零时零分零秒
     *
     * @return
     */
    public static Date getDateByToDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取指定日期的前几天
     *
     * @param date
     * @return
     */
    public static String getYesterdayBySize(Date date, int Size) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, -Size);
        return DateUtil.dateToString(c.getTime(), "yyyy-MM-dd HH:mm:ss");
    }


    /**
     * 获取指定日期的前几天
     *
     * @param date
     * @return
     */
    public static Date getYesterdayDateBySize(Date date, int Size) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, -Size);
        return c.getTime();
    }

    /**
     * 获取指定日期的前几个小时
     *
     * @param date
     * @return
     */
    public static String getLastHourBySize(Date date, int Size) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR_OF_DAY, -Size);
        return DateUtil.dateToString(c.getTime(), "yyyy-MM-dd HH:mm:ss");
    }


    /**
     * 获得昨天零时零分零秒
     *
     * @return
     */
    public static Date getYesterdayDateByDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.get(Calendar.DAY_OF_MONTH) - 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static String getNowStrTime() {
        return calendarToString(Calendar.getInstance());
    }


    public static Date localDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date LocalDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Calendar LocalDateTimeToCalendar(LocalDateTime localDateTime) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        return instance;
    }

    public static String LocalDateTimeToString(LocalDateTime localDateTime) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return localDateTime.format(fmt);
    }

    public static String LocalDateToString(LocalDate localDate) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.format(fmt);
    }

    public static LocalDate dateToLocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    // 24 * (differ / dayM) 这里拿到被舍弃的整数，整数是几，就代表相隔几天，一天24小时，那就整数乘以24即可。
    public static int getDifferHour(Date startDate, Date endDate) {
        long dayM = 1000 * 24 * 60 * 60;
        long hourM = 1000 * 60 * 60;
        long differ = endDate.getTime() - startDate.getTime();
        long hour = differ % dayM / hourM + 24 * (differ / dayM);
        return Integer.parseInt(String.valueOf(hour));
    }

    /**
     * 创建建的日历
     * @param year         年
     * @param month        月,从0开始
     * @param date         日
     * @param hourOfDay    小时
     * @param minute       分钟
     * @param second       秒
     * @return
     */
    public static Calendar createCalendar(int year, int month, int date, int hourOfDay, int minute, int second) {
        Calendar tempTime = Calendar.getInstance();
        tempTime.set(Calendar.MILLISECOND, 0);
        tempTime.set(year, month, date, hourOfDay, minute, second);
        return tempTime;
    }


    /**
     * 将GMT(格林威治时间)时间转换成北京时间
     */
    public static Calendar GMTToBJT(Calendar gmtCalendar) {
        // 获取 GMT Calendar 的时间戳
        long timeStamp = gmtCalendar.getTimeInMillis();
        // 创建一个时区为北京的 Calendar
        TimeZone bjtTimeZone = TimeZone.getTimeZone("Asia/Shanghai");
        Calendar bjtCalendar = Calendar.getInstance(bjtTimeZone);
        // 将时间戳设置到 BJ Calendar 上
        bjtCalendar.setTimeInMillis(timeStamp);
        return bjtCalendar;
    }

    /**
     * 根据给定的开始时间和结束时间，返回涉及的年份表名集合。
     *
     * @param btime 开始时间（Calendar 类型）
     * @param etime 结束时间（Calendar 类型）
     * @return 涉及的年份表名集合（List<String>）
     *
     * 使用原因：
     * 当数据库的表是按年份分割时，在查询数据时需要确定涉及的所有年份表。
     * 该方法通过遍历从开始时间到结束时间的年份，将所有涉及的年份表名返回，
     * 以便进行跨年份的数据查询。
     */
    public static List<Integer> getYearRangeTables(Calendar btime, Calendar etime) {
        List<Integer> years = new ArrayList<>();
        int startYear = btime.get(Calendar.YEAR);
        int endYear = etime.get(Calendar.YEAR);

        for (int year = startYear; year <= endYear; year++) {
            years.add(year);
        }
        return years;
    }

    /**
     * 根据给定的年份，以及开始时间和结束时间，返回该年份的开始时间。
     *
     * @param btime 开始时间（Calendar 类型）
     * @param etime 结束时间（Calendar 类型）
     * @param year  需要获取开始时间的年份
     * @return 该年份的开始时间（Calendar 类型）
     *
     * 使用原因：
     * 在跨年份查询时，某个年份的查询需要明确其时间范围的起点。
     * 该方法通过判断该年份是否与开始时间的年份一致，如果一致，则返回传入的开始时间；
     * 否则返回该年份的1月1日0时0分0秒，以确保查询范围的正确性。
     */
    public static Calendar getStartTimeForYear(Calendar btime, Calendar etime, int year) {
        Calendar start = Calendar.getInstance();

        // 判断年份是否与开始时间的年份一致
        if (year == btime.get(Calendar.YEAR)) {
            start.setTime(btime.getTime());
        } else {
            // 设置为该年份的1月1日
            start.set(year, Calendar.JANUARY, 1, 0, 0, 0);
            start.set(Calendar.MILLISECOND, 0);
        }

        return start;
    }

    /**
     * 根据给定的年份，以及开始时间和结束时间，返回该年份的结束时间。
     *
     * @param btime 开始时间（Calendar 类型）
     * @param etime 结束时间（Calendar 类型）
     * @param year  需要获取结束时间的年份
     * @return 该年份的结束时间（Calendar 类型）
     *
     * 使用原因：
     * 在跨年份查询时，某个年份的查询需要明确其时间范围的终点。
     * 该方法通过判断该年份是否与结束时间的年份一致，如果一致，则返回传入的结束时间；
     * 否则返回该年份的12月31日23时59分59秒，以确保查询范围的正确性。
     */
    public static Calendar getEndTimeForYear(Calendar btime, Calendar etime, int year) {
        Calendar end = Calendar.getInstance();

        // 判断年份是否与结束时间的年份一致
        if (year == etime.get(Calendar.YEAR)) {
            end.setTime(etime.getTime());
        } else {
            // 设置为该年份的12月31日
            end.set(year, Calendar.DECEMBER, 31, 23, 59, 59);
            end.set(Calendar.MILLISECOND, 999);
        }

        return end;
    }


    /**
     * 传递时间返回字符串  例如  "十月上旬" "十月下旬"
     *
     * @param calendar
     * @return
     */
    public static String getPeriodDescription(Calendar calendar) {
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH 是从0开始的
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);

        String monthStr = getMonthString(month);
        String period;

        if (dayOfMonth >= 1 && dayOfMonth <= 10) {
            period = "上旬";
        } else if (dayOfMonth >= 11 && dayOfMonth <= 20) {
            period = "中旬";
        } else if (dayOfMonth >= 21) {
            period = "下旬";
        } else {
            throw new IllegalArgumentException("Invalid day of the month: " + dayOfMonth);
        }

        return monthStr + period;
    }


    /**
     * 传递时间返回字符串  例如  "十月上旬" "十月下旬"
     *
     * @param calendar
     * @return
     */
    public static String getXunStr(Calendar calendar) {
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH 是从0开始的
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        String monthStr = (month) + "月";
        String period;
        if (dayOfMonth >= 1 && dayOfMonth <= 10) {
            period = "上旬";
        } else if (dayOfMonth >= 11 && dayOfMonth <= 20) {
            period = "中旬";
        } else if (dayOfMonth >= 21) {
            period = "下旬";
        } else {
            throw new IllegalArgumentException("Invalid day of the month: " + dayOfMonth);
        }

        return monthStr + period;
    }

    public static String getMonthString(Calendar month) {
        return getMonthString(month.get(Calendar.MONTH) + 1);
    }

    public static String getMonthString(int month) {
        switch (month) {
            case 1:
                return "一月";
            case 2:
                return "二月";
            case 3:
                return "三月";
            case 4:
                return "四月";
            case 5:
                return "五月";
            case 6:
                return "六月";
            case 7:
                return "七月";
            case 8:
                return "八月";
            case 9:
                return "九月";
            case 10:
                return "十月";
            case 11:
                return "十一月";
            case 12:
                return "十二月";
            default:
                throw new IllegalArgumentException("Invalid month: " + month);
        }
    }


    public static long getSecondsInPeriod(Calendar calendar) {
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);

        // 定义上旬、中旬和下旬的天数
        int upperDays = 10;
        int middleDays = 10;
        int lowerDays = getDaysInMonth(year, month) - 20;

        long upperSeconds = upperDays * 24 * 60 * 60;
        long middleSeconds = middleDays * 24 * 60 * 60;
        long lowerSeconds = lowerDays * 24 * 60 * 60;

        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        if (dayOfMonth >= 1 && dayOfMonth <= 10) {
            return upperSeconds;
        } else if (dayOfMonth >= 11 && dayOfMonth <= 20) {
            return middleSeconds;
        } else if (dayOfMonth >= 21) {
            return lowerSeconds;
        } else {
            throw new IllegalArgumentException("Invalid day of the month: " + dayOfMonth);
        }
    }

    private static int getDaysInMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }


    /**
     * 根据时间 和 需要的年份  获取数组
     *
     * @param calendar
     * @param beforeYears
     * @param afterYears
     * @return
     */
    public static int[] getYearsBeforeAndAfter(Calendar calendar, int beforeYears, int afterYears) {
        // 获取当前年份
        int currentYear = calendar.get(Calendar.YEAR);

        // 计算结果数组的大小
        int totalYears = beforeYears + afterYears;

        // 构建结果数组
        int[] years = new int[totalYears];

        // 填充前 beforeYears 的年份
        for (int i = 0; i < beforeYears; i++) {
            years[i] = currentYear - beforeYears + i;
        }

        // 填充当前年份以及后 afterYears 的年份
        for (int i = beforeYears; i < totalYears; i++) {
            years[i] = currentYear + (i - beforeYears);
        }

        return years;
    }




    /**
     * 获取指定年月的秒数
     *
     * @param year  年份
     * @param month 月份（0-11）
     * @return 指定月份的秒数
     */
    public static long getSecondsInMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        // 设置年份和月份
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        // 获取该月的天数
        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 计算秒数
        return daysInMonth * 24 * 60 * 60;
    }



    /**
     * 获取指定年和月的开始时间
     * @param year 指定的年份
     * @param month 指定的月份（1-12）
     * @return 返回该月的开始时间，精确到毫秒
     */
    public static Calendar getMonthStartTime(int year, int month) {
        // 获取一个Calendar实例，默认设置为当前日期和时间
        Calendar calendar = Calendar.getInstance();

        // 设置年份为传递的参数
        calendar.set(Calendar.YEAR, year);

        // 设置月份为传递的参数（月份从0开始，因此要减1）
        calendar.set(Calendar.MONTH, month - 1);

        // 设置日期为该月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        // 设置小时为0（午夜）
        calendar.set(Calendar.HOUR_OF_DAY, 0);

        // 设置分钟为0
        calendar.set(Calendar.MINUTE, 0);

        // 设置秒为0
        calendar.set(Calendar.SECOND, 0);

        // 设置毫秒为0
        calendar.set(Calendar.MILLISECOND, 0);

        // 返回配置后的日期对象
        return calendar;
    }

    /**
     * 获取指定年和月的结束时间
     * @param year 指定的年份
     * @param month 指定的月份（1-12）
     * @return 返回该月的结束时间，精确到毫秒
     */
    public static Calendar getMonthEndTime(int year, int month) {
        // 获取一个Calendar实例，默认设置为当前日期和时间
        Calendar calendar = Calendar.getInstance();

        // 设置年份为传递的参数
        calendar.set(Calendar.YEAR, year);

        // 设置月份为传递的参数（月份从0开始，因此要减1）
        calendar.set(Calendar.MONTH, month - 1);

        // 设置日期为该月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

        // 设置小时为23（一天的最后一个小时）
        calendar.set(Calendar.HOUR_OF_DAY, 23);

        // 设置分钟为59
        calendar.set(Calendar.MINUTE, 59);

        // 设置秒为59
        calendar.set(Calendar.SECOND, 59);

        // 设置毫秒为999（该秒的最后一毫秒）
        calendar.set(Calendar.MILLISECOND, 999);

        // 返回配置后的日期对象
        return calendar;
    }


    /**
     * 获取指定年份的开始时间
     *
     * @param year 年份
     * @return 指定年份的开始时间
     */
    public static Calendar getYearStarTime(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, Calendar.JANUARY); // 1月
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 1日
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 0时
        calendar.set(Calendar.MINUTE, 0); // 0分
        calendar.set(Calendar.SECOND, 0); // 0秒
        calendar.set(Calendar.MILLISECOND, 0); // 0毫秒
        return calendar; // 返回 Calendar 对象
    }

    /**
     * 获取指定年份的结束时间
     *
     * @param year 年份
     * @return 指定年份的结束时间
     */
    public static Calendar getYearEndTime(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, Calendar.DECEMBER); // 12月
        calendar.set(Calendar.DAY_OF_MONTH, 31); // 31日
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 23时
        calendar.set(Calendar.MINUTE, 59); // 59分
        calendar.set(Calendar.SECOND, 59); // 59秒
        calendar.set(Calendar.MILLISECOND, 999); // 999毫秒
        return calendar; // 返回 Calendar 对象
    }


    public static Calendar getMonthStartTime(Calendar inputDate) {
        Calendar startDate = (Calendar) inputDate.clone();

        // 将日期设置为当月1日
        startDate.set(Calendar.DAY_OF_MONTH, 1);

        // 将时间部分设为00:00:00
        startDate.set(Calendar.HOUR_OF_DAY, 0);
        startDate.set(Calendar.MINUTE, 0);
        startDate.set(Calendar.SECOND, 0);
        startDate.set(Calendar.MILLISECOND, 0);

        return startDate;
    }

    public static Calendar getMonthEndTime(Calendar inputDate) {
        Calendar endDate = (Calendar) inputDate.clone();

        // 将日期设置为当月最后一天
        endDate.set(Calendar.DAY_OF_MONTH, endDate.getActualMaximum(Calendar.DAY_OF_MONTH));

        // 将时间部分设为23:59:59
        endDate.set(Calendar.HOUR_OF_DAY, 23);
        endDate.set(Calendar.MINUTE, 59);
        endDate.set(Calendar.SECOND, 59);
        endDate.set(Calendar.MILLISECOND, 999);

        return endDate;
    }

    public static Calendar getXunStartTime(Calendar inputDate) {
        Calendar startDate = (Calendar) inputDate.clone();
        int day = startDate.get(Calendar.DAY_OF_MONTH);

        if (day <= 10) {
            // 上旬，开始时间为当月1日
            startDate.set(Calendar.DAY_OF_MONTH, 1);
        } else if (day <= 20) {
            // 中旬，开始时间为当月11日
            startDate.set(Calendar.DAY_OF_MONTH, 11);
        } else {
            // 下旬，开始时间为当月21日
            startDate.set(Calendar.DAY_OF_MONTH, 21);
        }

        // 将时间部分设为00:00:00
        startDate.set(Calendar.HOUR_OF_DAY, 0);
        startDate.set(Calendar.MINUTE, 0);
        startDate.set(Calendar.SECOND, 0);
        startDate.set(Calendar.MILLISECOND, 0);

        return startDate;
    }
    public static Calendar getXunEndTime(Calendar inputDate) {
        Calendar endDate = (Calendar) inputDate.clone();
        int day = endDate.get(Calendar.DAY_OF_MONTH);

        if (day <= 10) {
            // 上旬，结束时间为当月10日
            endDate.set(Calendar.DAY_OF_MONTH, 10);
        } else if (day <= 20) {
            // 中旬，结束时间为当月20日
            endDate.set(Calendar.DAY_OF_MONTH, 20);
        } else {
            // 下旬，结束时间为当月最后一天
            endDate.set(Calendar.DAY_OF_MONTH, endDate.getActualMaximum(Calendar.DAY_OF_MONTH));
        }

        // 将时间部分设为23:59:59
        endDate.set(Calendar.HOUR_OF_DAY, 23);
        endDate.set(Calendar.MINUTE, 59);
        endDate.set(Calendar.SECOND, 59);
        endDate.set(Calendar.MILLISECOND, 999);
        return endDate;
    }

    public static Calendar getDayStartTime(Calendar inputDate) {
        Calendar startDate = (Calendar) inputDate.clone();

        // 将时间部分设为00:00:00
        startDate.set(Calendar.HOUR_OF_DAY, 0);
        startDate.set(Calendar.MINUTE, 0);
        startDate.set(Calendar.SECOND, 0);
        startDate.set(Calendar.MILLISECOND, 0);

        return startDate;
    }

    public static Calendar getDayEndTime(Calendar inputDate) {
        Calendar endDate = (Calendar) inputDate.clone();

        // 将时间部分设为23:59:59
        endDate.set(Calendar.HOUR_OF_DAY, 23);
        endDate.set(Calendar.MINUTE, 59);
        endDate.set(Calendar.SECOND, 59);
        endDate.set(Calendar.MILLISECOND, 999);

        return endDate;
    }
    public static Calendar getHourStartTime(Calendar inputDate) {
        Calendar startDate = (Calendar) inputDate.clone();

        // 将分钟和秒部分设为00:00
        startDate.set(Calendar.MINUTE, 0);
        startDate.set(Calendar.SECOND, 0);
        startDate.set(Calendar.MILLISECOND, 0);

        return startDate;
    }


    public static Calendar getHourEndTime(Calendar inputDate) {
        Calendar endDate = (Calendar) inputDate.clone();

        // 将分钟和秒部分设为59:59
        endDate.set(Calendar.MINUTE, 59);
        endDate.set(Calendar.SECOND, 59);
        endDate.set(Calendar.MILLISECOND, 999);

        return endDate;
    }

    /**
     * 获取指定日期所属旬的开始时间
     * @param year 指定的年份
     * @param month 指定的月份（1-12）
     * @param day 指定的日期（1-31）
     * @return 返回该旬的开始时间，类型为Calendar
     */
    public static Calendar getXunStartCalendar(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();

        // 设置年份和月份
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);

        // 判断日期属于哪个旬，并设置开始时间
        if (day <= 10) { // 上旬
            calendar.set(Calendar.DAY_OF_MONTH, 1);
        } else if (day <= 20) { // 中旬
            calendar.set(Calendar.DAY_OF_MONTH, 11);
        } else { // 下旬
            calendar.set(Calendar.DAY_OF_MONTH, 21);
        }

        // 设置时间为0点0分0秒0毫秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar;
    }

    /**
     * 获取指定日期所属旬的结束时间
     * @param year 指定的年份
     * @param month 指定的月份（1-12）
     * @param day 指定的日期（1-31）
     * @return 返回该旬的结束时间，类型为Calendar
     */
    public static Calendar getXunEndCalendar(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();

        // 设置年份和月份
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);

        // 判断日期属于哪个旬，并设置结束时间
        if (day <= 10) { // 上旬
            calendar.set(Calendar.DAY_OF_MONTH, 10);
        } else if (day <= 20) { // 中旬
            calendar.set(Calendar.DAY_OF_MONTH, 20);
        } else { // 下旬
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        }

        // 设置时间为23点59分59秒999毫秒
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar;
    }



    /**
     * 获取指定日期的开始时间
     * @param year 指定的年份
     * @param month 指定的月份（1-12）
     * @param day 指定的日期（1-31）
     * @return 返回该日期的开始时间，类型为Calendar
     */
    public static Calendar getDayStartCalendar(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();

        // 设置年份、月份和日期
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // 月份从0开始，因此要减1
        calendar.set(Calendar.DAY_OF_MONTH, day);

        // 设置时间为0点0分0秒0毫秒，表示当天的开始时间
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar;
    }

    /**
     * 获取指定日期的结束时间
     * @param year 指定的年份
     * @param month 指定的月份（1-12）
     * @param day 指定的日期（1-31）
     * @return 返回该日期的结束时间，类型为Calendar
     */
    public static Calendar getDayEndCalendar(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();

        // 设置年份、月份和日期
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // 月份从0开始，因此要减1
        calendar.set(Calendar.DAY_OF_MONTH, day);

        // 设置时间为23点59分59秒999毫秒，表示当天的结束时间
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar;
    }

}
