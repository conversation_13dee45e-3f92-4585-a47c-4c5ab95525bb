package com.wcp.redis;

import java.util.Calendar;

/**
 * 
 * <AUTHOR>
 * @date 2022.2.18
 */
public class MemoryTimerData {
	private Calendar activeTime;
	private int expireTime;
	
	public MemoryTimerData(Calendar activeTime, int expireTime) {
		this.activeTime = activeTime;
		this.expireTime = expireTime;
	}
	
	public Calendar getActiveTime() {
		return activeTime;
	}
	public void setActiveTime(Calendar activeTime) {
		this.activeTime = activeTime;
	}
	public int getExpireTime() {
		return expireTime;
	}

	public void setExpireTime(int expireTime) {
		this.expireTime = expireTime;
	}
}
