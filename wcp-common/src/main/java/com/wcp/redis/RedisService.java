package com.wcp.redis;

import com.wcp.data.DataTypeUtil;
import com.wcp.utils.DateUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;


import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Redes调用方法
 * <AUTHOR>
 * @date 2021.6.14
 */
public class RedisService {
	private static RedisTemplate redisTemplate;

	public static void init(RedisTemplate redisTemplate){
		RedisService.redisTemplate=redisTemplate;
	};
	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, int expireTime) {
		redisTemplate.expire(getKey(projectId, key), expireTime, TimeUnit.MILLISECONDS);
	}

	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, Calendar expireTime) {
		long expire = expireTime.getTimeInMillis() - Calendar.getInstance().getTimeInMillis();
		if(expire <= 0) expire = 10;
		redisTemplate.expire(getKey(projectId, key), expire, TimeUnit.MILLISECONDS);
	}

	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param activeTime
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, Calendar activeTime, int expireTime) {
		activeTime = DateUtil.add(Calendar.MILLISECOND, expireTime, activeTime);
		long expire = activeTime.getTimeInMillis() - Calendar.getInstance().getTimeInMillis();
		if(expire <= 0) expire = 10;
		redisTemplate.expire(getKey(projectId, key), expire, TimeUnit.MILLISECONDS);
	}

	/**
	 * 不同的项目存在不同的数据Key中,此外用项目号作为复合健
	 * @param projectId
	 * @param key
	 * @return
	 */
	private static String getKey(String projectId, String key) {
		return projectId + ":" + key;
	}
	/**
	 * 批量删除指定key的值
	 * @param projectId
	 * @param service
	 * @param prefix
	 */
	public static void batchDeleteValue(String projectId, String service, String prefix) {
		Set<String> delKeys = redisTemplate.keys(getKey(projectId, prefix) + "*");
		redisTemplate.delete(delKeys);
	}
	// 下以为直接操作字符串
	/**
	 * 获取指定key的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> T getValue(String projectId, String service, Class<T> clazz, String key) {
		Object tempValue = redisTemplate.opsForValue().get(getKey(projectId, key));
		if(tempValue!=null){
			return DataTypeUtil.transformData(clazz, tempValue.toString());
		}else{
			return null;
		}

	}
	/**
	 * 返回多个keys对应的值
	 * @param projectId
	 * @param service
	 * @param keys
	 * @return
	 */
	public static <T> List<T> getValues(String projectId, String service, Class<T> clazz, String... keys) {
		List<String> tempKeys = new ArrayList<>();
		for (String key : keys) {
			tempKeys.add(getKey(projectId, key));
		}
		List<String> list = redisTemplate.opsForValue().multiGet(tempKeys);
		return DataTypeUtil.transformData(ArrayList.class, list, true, null, clazz);

	}
	/**
	 * 设置值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 */
	public static void setValue(String projectId, String service, String key, Object value) {
		redisTemplate.opsForValue().set(getKey(projectId, key), DataTypeUtil.transformData(String.class, value));

	}
	/**
	 * 设置多个值
	 * @param projectId
	 * @param service
	 * @param keysvalues
	 */
	public static void setValues(String projectId, String service, Object... keysvalues) {
		Class<?> clazz = DataTypeUtil.getTypeClass("String[]");
		String[] tempKeyValues = DataTypeUtil.transformData(clazz, keysvalues);
		Map<String, String> keyValueMap = new HashMap<>();
		for (int i = 0; i < tempKeyValues.length; i += 2) {
			keyValueMap.put(getKey(projectId, tempKeyValues[i]), tempKeyValues[i + 1]);
		}
		redisTemplate.opsForValue().multiSet(keyValueMap);
	}
	/**
	 * 增加字符串到已有字符串上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 */
	public static void appendValue(String projectId, String service, String key, Object value) {
		redisTemplate.opsForValue().append(getKey(projectId, key), DataTypeUtil.transformData(String.class, value));

	}
	/**
	 * 删除指定key的值
	 * @param projectId
	 * @param service
	 * @param key
	 */
	public static void deleteValue(String projectId, String service, String key) {
		redisTemplate.delete(getKey(projectId, key));
	}
	/**
	 * 增加指定整型值到对应的key上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Long incrValue(String projectId, String service, String key, Long value) {
		return redisTemplate.opsForValue().increment(getKey(projectId, key), value);
	}
	/**
	 * 增加指定符号型值到对应的key上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Double incrValue(String projectId, String service, String key, Double value) {
		return redisTemplate.opsForValue().increment(getKey(projectId, key), value);

	}

	// 以下为操作Map的方法
	/**
	 * 返回Map的长度
	 *
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getMapLength(String projectId, String service, String key) {
		return redisTemplate.opsForHash().size(getKey(projectId, key));
	}
	/**
	 * 获取
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static List<String> getMapKeys(String projectId, String service, String key) {
		return new ArrayList<>(redisTemplate.opsForHash().keys(getKey(projectId, key)));
	}
	/**
	 * 返回指定健的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @return
	 */
	public static <T> T getMapValue(String projectId, String service, Class<?> clazz, String key, String mapKey) {
		List<String> tempValues = redisTemplate.opsForHash().multiGet(key, Collections.singletonList(mapKey));
		if (!CollectionUtils.isEmpty(tempValues)) {
			String value = tempValues.get(0);
			return DataTypeUtil.transformData(clazz, value);
		}

		return null;
	}
	/**
	 * 返回指定健字对集合的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKeys
	 * @return
	 */
	public static <T> List<T> getMapValues(String projectId, String service, Class<T> clazz, String key, String... mapKeys) {
		List<Object> tempValues = redisTemplate.opsForHash().multiGet(getKey(projectId, key), Arrays.asList(mapKeys));
		return DataTypeUtil.transformData(ArrayList.class, tempValues, true, null, clazz);
	}
	/**
	 * 返回整个map
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> Map<String, T> getMap(String projectId, String service, Class<T> clazz, String key) {
		Map<Object, Object> tempMap = redisTemplate.opsForHash().entries(getKey(projectId, key));
		return DataTypeUtil.transformData(HashMap.class, tempMap, true, String.class, clazz);
	}
	/**
	 * 设置map到库中
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapValue
	 */
	public static <T> void setMapValue(String projectId, String service, String key, Map<String, T> mapValue) {
		Map<String, String> tempMap = DataTypeUtil.transformData(HashMap.class, mapValue, true, String.class, String.class);
		redisTemplate.opsForHash().putAll(getKey(projectId, key), tempMap);
	}

	/**
	 * 设置值到指定的map中对应的健值上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param mapValue
	 * @param <T>
	 */
	public static <T> void setMapValue(String projectId, String service, String key, String mapKey, T mapValue) {
		String mapValueStr = DataTypeUtil.transformData(String.class, mapValue);
		redisTemplate.opsForHash().put(getKey(projectId, key), mapKey, mapValueStr);
	}
	/**
	 * 增加指定整型值到对应的key和mapKey上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param value
	 * @return
	 */
	public static Long incrMapValue(String projectId, String service, String key, String mapKey, Long value) {
		Long addValue = redisTemplate.opsForHash().increment(getKey(projectId, key), mapKey, value);
		return addValue;
	}
	/**
	 * 增加指定浮点型值到对应的key和mapKey上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param value
	 * @return
	 */
	public static Double incrMapValue(String projectId, String service, String key, String mapKey, Double value) {
		Double addValue = redisTemplate.opsForHash().increment(getKey(projectId, key), mapKey, value);
		return addValue;
	}
	/**
	 * 删除指定的健字和mapKey值
	 * @param projectId
	 * @param service
	 * @param key(projectId, key)
	 * @param mapKey
	 */
	public static void deleteMapValue(String projectId, String service, String key, String mapKey) {
		redisTemplate.opsForHash().delete(getKey(projectId, key), mapKey);
	}
	/**
	 * 删除指定的key和mapKeys
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKeys
	 */
	public static void deleteMapValue(String projectId, String service, String key, String... mapKeys) {
		redisTemplate.opsForHash().delete(getKey(projectId, key), Arrays.asList(mapKeys));
	}

	// 以下为操作List的方法
	/**
	 * 返回列表List的长度
	 *
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getListLength(String projectId, String service, String key) {
		return redisTemplate.opsForList().size(getKey(projectId, key));
	}

	/**
	 * 替换指定位置的值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param index
	 * @param value
	 * @return
	 */
	public static <T> String setListValue(String projectId, String service, String key, int index, Object value) {
		Object valueStr=DataTypeUtil.transformData(String.class, value);
		redisTemplate.opsForList().set(getKey(projectId, key), index, valueStr);
		return "OK";
	}

	/**
	 * 增加值到最后一个值后面
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long addListValue(String projectId, String service, String key, Object value) {
		return redisTemplate.opsForList().rightPush(getKey(projectId, key), DataTypeUtil.transformData(String.class, value));
	}

	/**
	 * 增加值到最后一个值后面
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long addListValue(String projectId, String service, String key, int index, Object value) {
		try {
			String transformedValue = DataTypeUtil.transformData(String.class, value);
			if (index == 0) {
				// 直接添加到列表头部
				return redisTemplate.opsForList().leftPush(getKey(projectId, key), transformedValue);
			} else if (index > 0) {
				// 对于索引大于0的情况，先取出列表，然后重新构建并添加
				List<String> currentList = redisTemplate.opsForList().range(getKey(projectId, key), 0, -1);
				if (!CollectionUtils.isEmpty(currentList)) {
					// 在指定位置插入新值
					currentList.add(index, transformedValue);
					// 清空原列表
					redisTemplate.delete(getKey(projectId, key));
					// 重新添加所有元素到列表
					for (int i = 0; i < currentList.size(); i++) {
						redisTemplate.opsForList().rightPush(getKey(projectId, key), currentList.get(i));
					}
					return currentList.size(); // 返回修改后的列表长度
				} else {
					// 列表为空，直接添加到头部
					return redisTemplate.opsForList().leftPush(getKey(projectId, key), transformedValue);
				}
			} else {
				throw new IllegalArgumentException("Index must be non-negative");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	/**
	 * 增加集合值到最后一个值后面
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static <T> long addListValues(String projectId, String service, String key, List<T> values) {
		// 转换列表中的每个元素为字符串
		List<Object> stringValues = values.stream()
				.map(v -> DataTypeUtil.transformData(String.class, v))
				.collect(Collectors.toList());
		// 批量右推（尾部添加）所有值到列表
		Long result = redisTemplate.opsForList().rightPushAll(getKey(projectId, key), stringValues);
		return result;
	}


	/**
	 * 获取list中 指定位置的值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param index
	 * @return
	 */
	public static <T> T getListValue(String projectId, String service, Class<T> clazz, String key, long index) {
		String result = (String) redisTemplate.opsForList().index(getKey(projectId, key), index);
		if (!ObjectUtils.isEmpty(result)) {
			return DataTypeUtil.transformData(clazz, result);
		} else {
			// 根据具体情况处理null值，这里默认返回null
			return null;
		}
	}

	/**
	 * 获取指定范围的记录,范围为大于等于start,小于end
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static <T> List<T> getListValues(String projectId, String service, Class<T> clazz, String key, long start, long end) {
		// 使用RedisTemplate获取指定索引范围内的列表元素
		List<String> stringValues = redisTemplate.opsForList().range(getKey(projectId, key), start, end);

		// 确保列表不为空再进行转换
		if (!CollectionUtils.isEmpty(stringValues)) {
			// 假设DataTypeUtil.transformData有处理List<String>到List<T>的逻辑
			return DataTypeUtil.transformData(ArrayList.class, stringValues, true, null, clazz);
		} else {
			// 如果列表为空，返回空列表
			return Collections.emptyList();
		}
	}

	/**
	 * 删除List第一个值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static String deleteListValue(String projectId, String service, String key, int index) {
		// 获取操作列表的工具
		ListOperations<String, String> listOps = redisTemplate.opsForList();

		long length = listOps.size(getKey(projectId, key));
		String result = null;

		if (index == 0) {
			// 删除并返回列表的第一个元素
			result = listOps.leftPop(getKey(projectId, key));
		} else if (index == (int)(length - 1)) {
			// 删除并返回列表的最后一个元素
			result = listOps.rightPop(getKey(projectId, key));
		} else {
			// 获取指定索引的元素
			result = listOps.index(getKey(projectId, key), index);
			// 删除指定索引之后的所有元素
			List<String> nextList = listOps.range(getKey(projectId, key), index + 1, -1);
			listOps.trim(getKey(projectId, key), 0, index - 1);
			// 将剩余元素重新压入列表
			for (String item : nextList) {
				listOps.rightPush(getKey(projectId, key), item);
			}
		}
		return result;
	}

	/**
	 * 删除List开始和结束范围内的数据,范围为大于等于start,小于end
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static String deleteListValue(String projectId, String service, String key, long start, long end) {
		// 获取操作列表的工具
		ListOperations<String, String> listOps = redisTemplate.opsForList();

		// 如果结束索引大于等于开始索引，获取需要保留的后续元素
		List<String> nextList = end >= start
				? listOps.range(getKey(projectId, key), end + 1, -1)
				: Collections.emptyList();
		String result = "";
		if (start == 0) {
			// 如果开始索引为0，直接删除整个列表
			redisTemplate.delete(getKey(projectId, key));
			result = "ok";
		} else {
			// 否则，截断列表到开始索引前
			listOps.trim(getKey(projectId, key), 0, start - 1);
			result = "trimmed";
		}
		// 将之前保留的后续元素重新添加到列表末尾
		if (!CollectionUtils.isEmpty(nextList)) {
			for (String item : nextList) {
				listOps.rightPush(getKey(projectId, key), item);
			}
		}
		return result;
	}

	/**
	 * 删除List开始和结束范围内以外的数据,范围为大于等于start,小于end
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static String deleteListValueOutOfRange(String projectId, String service, String key, long start, long end) {
		redisTemplate.opsForList().trim(getKey(projectId, key), start, end);
		return "ok";
	}

	// 以下为操作Set的方法
	/**
	 * 返回Set的长度
	 *
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getSetLength(String projectId, String service, String key) {
		return redisTemplate.opsForSet().size(getKey(projectId, key));

	}

	/**
	 * 向Set中增加值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long addSetValue(String projectId, String service, String key, Object value) {
		String addValueStr = DataTypeUtil.transformData(String.class, value);
		return redisTemplate.opsForSet().add(getKey(projectId, key), addValueStr);
	}

	/**
	 * 向Set中增加数据值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static long addSetValues(String projectId, String service, String key, Object... values) {
		Class<?> clazz = DataTypeUtil.getTypeClass("String[]");
		String[] tempValues = DataTypeUtil.transformData(clazz, values);
		return redisTemplate.opsForSet().add(getKey(projectId, key), tempValues);
	}

	/**
	 * 向Set中增加集合值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static <T> long addSetValues(String projectId, String service, String key, Collection<T> values) {
		Class<?> clazz = DataTypeUtil.getTypeClass("String[]");
		String[] tempArr = DataTypeUtil.transformData(clazz, values);
		return redisTemplate.opsForSet().add(getKey(projectId, key), tempArr);
	}

	/**
	 * 获取Set中所有元素的值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> Set<T> getSetValues(String projectId, String service, Class<T> clazz, String key) {
		// 使用RedisTemplate获取集合中的所有成员
		Set<String> stringResult = redisTemplate.opsForSet().members(getKey(projectId, key));
		// 确保结果不为空再进行转换
		if (!CollectionUtils.isEmpty(stringResult)) {
			// 假定DataTypeUtil.transformData能够将String集合转换为目标类型T的集合
			return DataTypeUtil.transformData(HashSet.class, stringResult, true, null, clazz);
		} else {
			// 如果集合为空，直接返回空集合
			return Collections.emptySet();
		}
	}

	/**
	 * 判断Set中是否包含某值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Boolean containSetValue(String projectId, String service, String key, Object value) {
		String setValueStr = DataTypeUtil.transformData(String.class, value);
		Boolean result = redisTemplate.opsForSet().isMember(getKey(projectId, key), setValueStr);
		return result;
	}

	/**
	 * 删除Set的值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long deleteSetValue(String projectId, String service, String key, Object value) {
		String setValueStr = DataTypeUtil.transformData(String.class, value);
		// 使用RedisTemplate的opsForSet删除成员
		Long result = redisTemplate.opsForSet().remove(getKey(projectId, key), setValueStr);
		return result != null ? result : 0L;
	}

	/**
	 * 删除Set的集合值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static long deleteSetValues(String projectId, String service, String key, Object... values) {
		Class<?> clazz = DataTypeUtil.getTypeClass("String[]");
		String[] tempValues = DataTypeUtil.transformData(clazz, values);
		// 使用RedisTemplate的opsForSet删除多个成员
		Long result = redisTemplate.opsForSet().remove(getKey(projectId, key), tempValues);
		return result != null ? result : 0L;
	}
}
