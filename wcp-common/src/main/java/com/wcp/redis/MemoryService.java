package com.wcp.redis;

import com.wcp.data.DataTypeUtil;
import com.wcp.utils.DateUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 内存缓存，当不用redis，用此缓存代替内存缓存
 * 当配置文件不存在，端口和IP配置有问题时调用内部缓存
 * <AUTHOR>
 * @date 2021.06.19
 */
public class MemoryService {
	// 缓存存储类,keys分别表示:项目点号+":"+服务号;数据key
	private static Map<String, Map<String, Object>> cacheMemory = new ConcurrentHashMap<String, Map<String, Object>>();
	// 数据更新对应的时间
	private static Map<String, Map<String, MemoryTimerData>> cacheTime = new ConcurrentHashMap<String, Map<String, MemoryTimerData>>();

	static {
		// 创建定时清理任务，过期的key自动清理
		memoryClearTimer();
	}
	// 创建定时清理任务，过期的key自动清理
	private static void memoryClearTimer() {
		Timer timer = new Timer();
		timer.scheduleAtFixedRate(new TimerTask() {
			@Override
			public void run() {
				Iterator<Entry<String, Map<String, MemoryTimerData>>> iterator = cacheTime.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, Map<String, MemoryTimerData>> entry = iterator.next();
					String entryKey = entry.getKey();
					Map<String, MemoryTimerData> entryValue = entry.getValue();
					Iterator<Entry<String, MemoryTimerData>> iterator1 = entryValue.entrySet().iterator();
					while (iterator1.hasNext()) {
						Entry<String, MemoryTimerData> entry1 = iterator1.next();
						String tempKey = entry1.getKey();
						MemoryTimerData tempValue = entry1.getValue();
						if(tempValue.getExpireTime() > 0 || tempValue.getActiveTime() != null) {
							Calendar expire = DateUtil.add(Calendar.MILLISECOND, tempValue.getExpireTime(),
									tempValue.getActiveTime());
							if (expire.before(Calendar.getInstance())) {
								iterator.remove();
								Map<String, Object> temp = cacheMemory.get(entryKey);
								if (temp != null)
									temp.remove(tempKey);
							}
						}
					}
				}
			}
		}, 0, 5 * 60 * 1000);
	}
	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, int expireTime) {
		Map<String,MemoryTimerData> result = cacheTime.get(projectId + ":" + service);
		if(result == null) {
			result = new HashMap<String,MemoryTimerData>();
			cacheTime.put(projectId + ":" + service, result);
		}
		result.put(key, new MemoryTimerData(Calendar.getInstance(), expireTime));
	}

	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, Calendar expireTime) {
		Map<String, MemoryTimerData> result = cacheTime.get(projectId + ":" + service);
		if (result == null) {
			result = new HashMap<String, MemoryTimerData>();
			cacheTime.put(projectId + ":" + service, result);
		}
		result.put(key, new MemoryTimerData(Calendar.getInstance(),
				(int) (expireTime.getTimeInMillis() - Calendar.getInstance().getTimeInMillis())));
	}

	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param activeTime
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, Calendar activeTime, int expireTime) {
		Map<String, MemoryTimerData> result = cacheTime.get(projectId + ":" + service);
		if (result == null) {
			result = new HashMap<String, MemoryTimerData>();
			cacheTime.put(projectId + ":" + service, result);
		}
		result.put(key, new MemoryTimerData(activeTime, expireTime));
	}

	/**
	 * 设置当前活动时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param activeTime
	 */
	public static void setActiveTime(String projectId, String service, String key, Calendar activeTime) {
		Map<String, MemoryTimerData> result = cacheTime.get(projectId + ":" + service);
		if (result == null) {
			result = new HashMap<String, MemoryTimerData>();
			cacheTime.put(projectId + ":" + service, result);
		}
		MemoryTimerData tempData = result.get(key);
		if(tempData == null) {
			tempData = new MemoryTimerData(Calendar.getInstance(), 3600 * 1000);
			result.put(key, tempData);
		}
		tempData.setActiveTime(activeTime);
	}

	/**
	 * 返回各类型缓存容器
	 * @param projectId
	 * @param service
	 * @return
	 */
	private static Map<String, Object> getProjectMemory(String projectId, String service) {
		Map<String, Object> result = cacheMemory.get(projectId + ":" + service);
		if(result == null) {
			result = new HashMap<String, Object>();
			cacheMemory.put(projectId + ":" + service, result);
		}
		return result;
	}


	/**
	 * 返回缓存所有的以指定前缀开头的key集合
	 * @param prefix
	 * @return
	 */
	public static Set<String> keys(String projectId, String service, String key, String prefix) {
		Set<String> tempSet = new HashSet<String>();
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		synchronized (cacheMemory) {
			Set<String> keys = cacheMap.keySet();
			Iterator<String> iterator = keys.iterator();
			while (iterator.hasNext()) {
				String tempKey = iterator.next();
				if(tempKey != null && tempKey.startsWith(prefix)) {
					tempSet.add(tempKey);
				}
			}
		}
		return tempSet;
	}
	/**
	 * 批量删除指定key的值
	 * @param projectId
	 * @param service
	 * @param prefix
	 */
	public static void batchDeleteValue(String projectId, String service, String prefix) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		Iterator<Map.Entry<String, Object>> iterator = cacheMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Map.Entry<String, Object> entry = iterator.next();
			if(entry.getKey() != null && entry.getKey().startsWith(prefix)) {
				iterator.remove();
			}
		}
	}
	// 下以为直接操作字符串
	/**
	 * 获取指定key的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> T getValue(String projectId, String service, Class<T> clazz, String key) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		Object tempValue = cacheMap.get(key);
		return DataTypeUtil.transformData(clazz, tempValue);
	}
	/**
	 * 返回多个keys对应的值
	 * @param projectId
	 * @param service
	 * @param keys
	 * @return
	 */
	public static <T> List<T> getValues(String projectId, String service, Class<T> clazz, String... keys) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		List<T> result = new ArrayList<T>();
		for (int i = 0; i < keys.length; i++) {
			result.add(DataTypeUtil.transformData(clazz, cacheMap.get(keys[i])));
		}
		return result;
	}
	/**
	 * 设置值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 */
	public static void setValue(String projectId, String service, String key, Object value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		cacheMap.put(key, value);
	}
	/**
	 * 设置多个值
	 * @param projectId
	 * @param service
	 * @param keysvalues
	 */
	public static void setValues(String projectId, String service, Object... keysvalues) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		for (int i = 0; i < keysvalues.length; i += 2) {
			cacheMap.put(keysvalues[i].toString(), keysvalues[i + 1]);
		}
	}
	/**
	 * 增加字符串到已有字符串上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 */
	public static void appendValue(String projectId, String service, String key, Object value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		String tempValue = (String)cacheMap.get(key);
		if(tempValue == null) tempValue = "";
		tempValue += value == null ? "" : value.toString();
		cacheMap.put(key, tempValue);
	}
	/**
	 * 删除指定key的值
	 * @param projectId
	 * @param service
	 * @param key
	 */
	public static void deleteValue(String projectId, String service, String key) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		cacheMap.remove(key);
	}
	/**
	 * 增加指定整型值到对应的key上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Long incrValue(String projectId, String service, String key, Long value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		Long result = value;
		Object tempPreValue = cacheMap.get(key);
		if(tempPreValue != null) {
			Class<?> clazz = tempPreValue.getClass();
			result = DataTypeUtil.transformData(Long.class, tempPreValue);
			result += value;
			tempPreValue = DataTypeUtil.transformData(clazz, result);
		} else {
			tempPreValue = value;
		}
		cacheMap.put(key, tempPreValue);
		return result;
	}
	/**
	 * 增加指定符号型值到对应的key上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Double incrValue(String projectId, String service, String key, Double value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		Double result = value;
		Object tempPreValue = cacheMap.get(key);
		if(tempPreValue != null) {
			Class<?> clazz = tempPreValue.getClass();
			result = DataTypeUtil.transformData(Double.class, tempPreValue);
			result += value;
			tempPreValue = DataTypeUtil.transformData(clazz, result);
		} else {
			tempPreValue = value;
		}
		cacheMap.put(key, tempPreValue);
		return result;
	}

	// 以下为操作Map的方法
	/**
	 * 返回Map的长度
	 *
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getMapLength(String projectId, String service, String key) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		if(tempValueMap == null) {
			return 0l;
		} else {
			return tempValueMap.size();
		}
	}
	/**
	 * 获取
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static List<String> getMapKeys(String projectId, String service, String key) {
		List<String> tempKeys = new ArrayList<String>();
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		if(tempValueMap == null) {
			return tempKeys;
		} else {
			tempKeys.addAll(tempValueMap.keySet());
		}
		return tempKeys;
	}
	/**
	 * 返回指定健的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T getMapValue(String projectId, String service, Class<T> clazz, String key, String mapKey) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		if(tempValueMap == null) {
			return null;
		} else {
			return DataTypeUtil.transformData(clazz, tempValueMap.get(mapKey));
		}
	}
	/**
	 * 返回指定健字对集合的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKeys
	 * @return
	 */
	public static <T> List<T> getMapValues(String projectId, String service, Class<T> clazz, String key, String... mapKeys) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		List<T> tempValues = new ArrayList<T>();
		if(tempValueMap == null) {
			return tempValues;
		} else {
			for (int i = 0; i < mapKeys.length; i++) {
				tempValues.add(DataTypeUtil.transformData(clazz, tempValueMap.get(mapKeys[i])));
			}
			return tempValues;
		}
	}
	/**
	 * 返回整个map
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> Map<String, T> getMap(String projectId, String service, Class<T> clazz, String key) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		if(tempValueMap == null) {
			return new HashMap<String, T>();
		}
		return DataTypeUtil.transformData(HashMap.class, tempValueMap, true, String.class, clazz);
	}
	/**
	 * 设置map到库中
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapValue
	 */
	public static <T> void setMapValue(String projectId, String service, String key, Map<String, T> mapValue) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		cacheMap.put(key, mapValue);
	}

	/**
	 * 设置值到指定的Map对应的key下
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param mapValue
	 * @param <T>
	 */
	public static <T> void setMapValue(String projectId, String service, String key, String mapKey, T mapValue) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		Map<String, T> tempMap = (Map)cacheMap.get(key);
		if(tempMap == null) {
			tempMap = new HashMap<String, T>();
			cacheMap.put(key, tempMap);
		}
		tempMap.put(mapKey, mapValue);
	}
	/**
	 * 增加指定整型值到对应的key和mapKey上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param value
	 * @return
	 */
	public static Long incrMapValue(String projectId, String service, String key, String mapKey, Long value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		Long result = value;
		Object tempPreValue = tempValueMap.get(key);
		if(tempPreValue != null) {
			Class<?> clazz = tempPreValue.getClass();
			result = DataTypeUtil.transformData(Long.class, tempPreValue);
			result += value;
			tempPreValue = DataTypeUtil.transformData(clazz, result);
		} else {
			tempPreValue = value;
		}
		tempValueMap.put(key, tempPreValue);
		return result;
	}
	/**
	 * 增加指定浮点型值到对应的key和mapKey上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param value
	 * @return
	 */
	public static Double incrMapValue(String projectId, String service, String key, String mapKey, Double value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		Double result = value;
		Object tempPreValue = tempValueMap.get(mapKey);
		if(tempPreValue != null) {
			Class<?> clazz = tempPreValue.getClass();
			result = DataTypeUtil.transformData(Double.class, tempPreValue);
			result += value;
			tempPreValue = DataTypeUtil.transformData(clazz, result);
		} else {
			tempPreValue = value;
		}
		tempValueMap.put(key, tempPreValue);
		return result;
	}
	/**
	 * 删除指定的健字和mapKey值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 */
	public static void deleteMapValue(String projectId, String service, String key, String mapKey) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		if(tempValueMap != null) {
			tempValueMap.remove(mapKey);
		}
	}
	/**
	 * 删除指定的key和mapKeys
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKeys
	 */
	public static void deleteMapValue(String projectId, String service, String key, String... mapKeys) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Map<String, Object> tempValueMap = (Map<String, Object>)cacheMap.get(key);
		if(tempValueMap != null) {
			for (int i = 0; i < mapKeys.length; i++) {
				tempValueMap.remove(mapKeys[i]);
			}
		}
	}

	// 以下为操作List的方法
	/**
	 * 返回列表List的长度
	 *
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getListLength(String projectId, String service, String key) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		List<?> tempValueList = (List<?>)cacheMap.get(key);
		int length = 0;
		if(tempValueList != null) {
			length = tempValueList.size();
		}
		return length;
	}

	/**
	 * 替换指定位置的值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param index
	 * @param value
	 * @return
	 */
	public static <T> String setListValue(String projectId, String service, String key, int index, T value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(tempValueList == null || tempValueList.size() <= index) {
			return "indexOutOfBoundsException";
		}
		tempValueList.set(index, value);
		return "ok";
	}

	/**
	 * 增加值到最后一个值后面
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static <T> long addListValue(String projectId, String service, String key, T value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(tempValueList == null) {
			tempValueList = new ArrayList<Object>();
			cacheMap.put(key, tempValueList);
		}
		tempValueList.add(value);
		return tempValueList.size();
	}

	/**
	 * 增加值到最后一个值后面
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static <T> long addListValue(String projectId, String service, String key, int index, T value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(tempValueList == null) {
			tempValueList = new ArrayList<Object>();
			cacheMap.put(key, tempValueList);
		}
		tempValueList.add(index, value);
		return tempValueList.size();
	}

	/**
	 * 增加集合值到最后一个值后面
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static <T> long addListValues(String projectId, String service, String key, List<T> values) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(tempValueList == null) {
			tempValueList = new ArrayList<Object>();
			cacheMap.put(key, tempValueList);
		}
		tempValueList.addAll(values);
		return tempValueList.size();
	}

	/**
	 * 增加集合值到指定位置
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param index
	 * @param values
	 * @return
	 */
	public static <T> long addListValues(String projectId, String service, String key, long index, List<T> values) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(tempValueList == null) {
			tempValueList = new ArrayList<Object>();
			cacheMap.put(key, tempValueList);
		}
		tempValueList.addAll((int)index, values);
		return tempValueList.size();
	}

	/**
	 * 获取list中 指定位置的值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param index
	 * @return
	 */
	public static <T> T getListValue(String projectId, String service, Class<T> clazz, String key, long index) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(tempValueList == null) {
			return null;
		} else {
			return DataTypeUtil.transformData(clazz, tempValueList.get((int)index));
		}
	}

	/**
	 * 获取指定范围的记录,范围为大于等于start,小于end
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> getListValues(String projectId, String service, Class<T> clazz, String key, long start, long end) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		List<T> result = new ArrayList<T>();
		if(tempValueList == null) {
			return result;
		}
		if(start < 0) start = 0;
		if(end < 0) end = tempValueList.size() - 1;
		for (int i = (int)start; i <= end; i++) {
			result.add(DataTypeUtil.transformData(clazz, tempValueList.get(i)));
		}
		return result;
	}

	/**
	 * 删除List第一个值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static String deleteListValue(String projectId, String service, String key, int index) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(tempValueList != null) {
			tempValueList.remove(index);
		}
		return "ok";
	}

	/**
	 * 删除List开始和结束范围内的数据,范围为大于等于start,小于end
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static String deleteListValue(String projectId, String service, String key, long start, long end) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(start < 0) start = 0;
		if(end < 0) end = tempValueList.size() - 1;
		if(tempValueList != null) {
			for (int i = (int)end; i >= start; i--) {
				tempValueList.remove(i);
			}
		}
		return "ok";
	}

	/**
	 * 删除List开始和结束范围内以外的数据,范围为大于等于start,小于end
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static String deleteListValueOutOfRange(String projectId, String service, String key, long start, long end) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		List<Object> tempValueList = (List<Object>)cacheMap.get(key);
		if(start < 0) start = 0;
		if(end < 0) end = tempValueList.size() - 1;
		if(tempValueList != null) {
			for (int i = (int)end; i >= start; i--) {
				if(i > end && i < start) {
					tempValueList.remove(i);
				}
			}
		}
		return "ok";
	}

	// 以下为操作Set的方法
	/**
	 * 返回Set的长度
	 *
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getSetLength(String projectId, String service, String key) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		if(tempValueSet == null) {
			return 0;
		} else {
			return tempValueSet.size();
		}
	}

	/**
	 * 向Set中增加值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long addSetValue(String projectId, String service, String key, Object value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		if(tempValueSet == null) {
			tempValueSet = new HashSet<Object>();
			cacheMap.put(key, tempValueSet);
		}
		tempValueSet.add(value);
		return tempValueSet.size();
	}

	/**
	 * 向Set中增加数据值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static long addSetValues(String projectId, String service, String key, Object... values) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		if(tempValueSet == null) {
			tempValueSet = new HashSet<Object>();
			cacheMap.put(key, tempValueSet);
		}
		for (int i = 0; i < values.length; i++) {
			tempValueSet.add(values[i]);
		}
		return tempValueSet.size();
	}

	/**
	 * 向Set中增加集合值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static <T> long addSetValues(String projectId, String service, String key, Collection<T> values) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		if(tempValueSet == null) {
			tempValueSet = new HashSet<Object>();
			cacheMap.put(key, tempValueSet);
		}
		tempValueSet.addAll(values);
		return tempValueSet.size();
	}

	/**
	 * 获取Set中所有元素的值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> Set<T> getSetValues(String projectId, String service, Class<T> clazz, String key) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		return DataTypeUtil.transformData(HashSet.class, tempValueSet, true, null, clazz);
	}

	/**
	 * 判断Set中是否包含某值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Boolean containSetValue(String projectId, String service, String key, Object value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		if(tempValueSet == null) {
			return false;
		} else {
			return tempValueSet.contains(value);
		}
	}

	/**
	 * 删除Set的值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long deleteSetValue(String projectId, String service, String key, Object value) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		if(tempValueSet == null) {
			return 0;
		} else {
			tempValueSet.remove(value);
			return tempValueSet.size();
		}
	}

	/**
	 * 删除Set的集合值
	 *
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static long deleteSetValues(String projectId, String service, String key, Object... values) {
		Map<String, Object> cacheMap = getProjectMemory(projectId, service);
		@SuppressWarnings("unchecked")
		Set<Object> tempValueSet = (Set<Object>)cacheMap.get(key);
		if(tempValueSet == null) {
			return 0;
		} else {
			for (int i = 0; i < values.length; i++) {
				tempValueSet.remove(values[i]);
			}
			return tempValueSet.size();
		}
	}


}
