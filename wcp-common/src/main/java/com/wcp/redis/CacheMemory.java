package com.wcp.redis;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * 平台缓存数据，分项目和服务
 * <AUTHOR>
 * @date 2021.06.20
 */
@Configuration
public class CacheMemory {
	private static String host;
	@Value("${spring.redis.host:#{null}}")
	public void confighost(String host){
		CacheMemory.host=host;
	}
	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, int expireTime) {
		if (host != null) {
			RedisService.setExpireTime(projectId, service, key, expireTime);
		} else {
			MemoryService.setExpireTime(projectId, service, key, expireTime);
		}
	}
	
	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, Calendar expireTime) {
		if(host != null) {
			RedisService.setExpireTime(projectId, service, key, expireTime);
		} else {
			MemoryService.setExpireTime(projectId, service, key, expireTime);
		}
	}
	
	/**
	 * 设置过期时间
	 * @param projectId
	 * @param service
	 * @param key
	 * @param activeTime
	 * @param expireTime
	 */
	public static void setExpireTime(String projectId, String service, String key, Calendar activeTime, int expireTime) {
		if(host != null) {
			RedisService.setExpireTime(projectId, service, key, activeTime, expireTime);
		} else {
			MemoryService.setExpireTime(projectId, service, key, activeTime, expireTime);
		}
	}
	
	/**
	 * 返回嵌套的key,以便存储成结构性数据
	 * @param keys
	 * @return
	 */
	public static String getNestKey(String... keys) {
		String tempStr = "";
		for (int i = 0; i < keys.length; i++) {
			tempStr += (i == 0 ? "" : ":") + keys[i];
		}
		return tempStr;
	}
	/**
	 * 批量删除指定key的值
	 * @param projectId
	 * @param service
	 * @param prefix
	 */
	public static void batchDeleteValue(String projectId, String service, String prefix) {
		if(host != null) {
			RedisService.batchDeleteValue(projectId, service, prefix);
		} else {
			MemoryService.batchDeleteValue(projectId, service, prefix);
		}
	}
	// 下以为直接操作字符串
	/**
	 * 获取指定key的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> T getValue(String projectId, String service, Class<T> clazz, String key) {
		if(host != null) {
			return RedisService.getValue(projectId, service, clazz, key);
		} else {
			return MemoryService.getValue(projectId, service, clazz, key);
		}
	}
	/**
	 * 返回多个keys对应的值
	 * @param projectId
	 * @param service
	 * @param keys
	 * @return
	 */
	public static <T> List<T> getValues(String projectId, String service, Class<T> clazz, String... keys) {
		if(host != null) {
			return RedisService.getValues(projectId, service, clazz, keys);
		} else {
			return MemoryService.getValues(projectId, service, clazz, keys);
		}
	}
	/**
	 * 设置值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 */
	public static void setValue(String projectId, String service, String key, Object value) {
		if(host != null) {
			RedisService.setValue(projectId, service, key, value);
		} else {
			MemoryService.setValue(projectId, service, key, value);
		}
	}
	/**
	 * 设置多个值
	 * @param projectId
	 * @param service
	 * @param keysvalues
	 */
	public static void setValues(String projectId, String service, Object... keysvalues) {
		if(host != null) {
			RedisService.setValues(projectId, service, keysvalues);
		} else {
			MemoryService.setValues(projectId, service, keysvalues);
		}
	}
	/**
	 * 增加字符串到已有字符串上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 */
	public static void appendValue(String projectId, String service, String key, Object value) {
		if(host != null) {
			RedisService.appendValue(projectId, service, key, value);
		} else {
			MemoryService.appendValue(projectId, service, key, value);
		}
	}
	/**
	 * 删除指定key的值
	 * @param projectId
	 * @param service
	 * @param key
	 */
	public static void deleteValue(String projectId, String service, String key) {
		if(host != null) {
			RedisService.deleteValue(projectId, service, key);
		} else {
			MemoryService.deleteValue(projectId, service, key);
		}
	}
	/**
	 * 增加指定整型值到对应的key上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Long incrValue(String projectId, String service, String key, Long value) {
		if(host != null) {
			return RedisService.incrValue(projectId, service, key, value);
		} else {
			return MemoryService.incrValue(projectId, service, key, value);
		}
	}
	/**
	 * 增加指定符号型值到对应的key上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Double incrValue(String projectId, String service, String key, Double value) {
		if(host != null) {
			return RedisService.incrValue(projectId, service, key, value);
		} else {
			return MemoryService.incrValue(projectId, service, key, value);
		}
	}
	
	// 以下为操作Map的方法
	/**
	 * 返回Map的长度
	 * 
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getMapLength(String projectId, String service, String key) {
		if(host != null) {
			return RedisService.getMapLength(projectId, service, key);
		} else {
			return MemoryService.getMapLength(projectId, service, key);
		}
	}
	/**
	 * 获取
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static List<String> getMapKeys(String projectId, String service, String key) {
		if(host != null) {
			return RedisService.getMapKeys(projectId, service, key);
		} else {
			return MemoryService.getMapKeys(projectId, service, key);
		}
	}
	/**
	 * 返回指定健的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @return
	 */
	public static <T> T getMapValue(String projectId, String service, Class<T> clazz, String key, String mapKey) {
		if(host != null) {
			return RedisService.getMapValue(projectId, service, clazz, key, mapKey);
		} else {
			return MemoryService.getMapValue(projectId, service, clazz, key, mapKey);
		}
	}
	/**
	 * 返回指定健字对集合的值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKeys
	 * @return
	 */
	public static <T> List<T> getMapValues(String projectId, String service, Class<T> clazz, String key, String... mapKeys) {
		if(host != null) {
			return RedisService.getMapValues(projectId, service, clazz, key, mapKeys);
		} else {
			return MemoryService.getMapValues(projectId, service, clazz, key, mapKeys);
		}
	}
	/**
	 * 返回整个map
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> Map<String, T> getMap(String projectId, String service, Class<T> clazz, String key) {
		if(host != null) {
			return RedisService.getMap(projectId, service, clazz, key);
		} else {
			return MemoryService.getMap(projectId, service, clazz, key);
		}
	}
	/**
	 * 设置map到库中
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapValue
	 */
	public static <T> void setMapValue(String projectId, String service, String key, Map<String, T> mapValue) {
		if(host != null) {
			RedisService.setMapValue(projectId, service, key, mapValue);
		} else {
			MemoryService.setMapValue(projectId, service, key, mapValue);
		}
	}
	/**
	 * 设置值到指定的Map对应的key下
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapValue
	 */
	public static <T> void setMapValue(String projectId, String service, String key, String mapKey, T mapValue) {
		if(host != null) {
			RedisService.setMapValue(projectId, service, key, mapKey, mapValue);
		} else {
			MemoryService.setMapValue(projectId, service, key, mapKey, mapValue);
		}
	}
	/**
	 * 增加指定整型值到对应的key和mapKey上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param value
	 * @return
	 */
	public static Long incrMapValue(String projectId, String service, String key, String mapKey, Long value) {
		if(host != null) {
			return RedisService.incrMapValue(projectId, service, key, mapKey, value);
		} else {
			return MemoryService.incrMapValue(projectId, service, key, mapKey, value);
		}
	}
	/**
	 * 增加指定浮点型值到对应的key和mapKey上
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 * @param value
	 * @return
	 */
	public static Double incrMapValue(String projectId, String service, String key, String mapKey, Double value) {
		if(host != null) {
			return RedisService.incrMapValue(projectId, service, key, mapKey, value);
		} else {
			return MemoryService.incrMapValue(projectId, service, key, mapKey, value);
		}
	}
	/**
	 * 删除指定的健字和mapKey值
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKey
	 */
	public static void deleteMapValue(String projectId, String service, String key, String mapKey) {
		if(host != null) {
			RedisService.deleteMapValue(projectId, service, key, mapKey);
		} else {
			MemoryService.deleteMapValue(projectId, service, key, mapKey);
		}
	}
	/**
	 * 删除指定的key和mapKeys
	 * @param projectId
	 * @param service
	 * @param key
	 * @param mapKeys
	 */
	public static void deleteMapValue(String projectId, String service, String key, String... mapKeys) {
		if(host != null) {
			RedisService.deleteMapValue(projectId, service, key, mapKeys);
		} else {
			MemoryService.deleteMapValue(projectId, service, key, mapKeys);
		}
	}
	
	// 以下为操作List的方法
	/**
	 * 返回列表List的长度
	 * 
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getListLength(String projectId, String service, String key) {
		if(host != null) {
			return RedisService.getListLength(projectId, service, key);
		} else {
			return MemoryService.getListLength(projectId, service, key);
		}
	}

	/**
	 * 替换指定位置的值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param index
	 * @param value
	 * @return
	 */
	public static String setListValue(String projectId, String service, String key, int index, Object value) {
		if(host != null) {
			return RedisService.setListValue(projectId, service, key, index, value);
		} else {
			return MemoryService.setListValue(projectId, service, key, index, value);
		}
	}

	/**
	 * 增加值到最后一个值后面
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long addListValue(String projectId, String service, String key, Object value) {
		if(host != null) {
			return RedisService.addListValue(projectId, service, key, value);
		} else {
			return MemoryService.addListValue(projectId, service, key, value);
		}
	}

	/**
	 * 增加值到最后一个值后面
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long addListValue(String projectId, String service, String key, int index, Object value) {
		if(host != null) {
			return RedisService.addListValue(projectId, service, key, index, value);
		} else {
			return MemoryService.addListValue(projectId, service, key, index, value);
		}
	}

	/**
	 * 增加集合值到最后一个值后面
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static <T> long addListValues(String projectId, String service, String key, List<T> values) {
		if(host != null) {
			return RedisService.addListValues(projectId, service, key, values);
		} else {
			return MemoryService.addListValues(projectId, service, key, values);
		}
	}



	/**
	 * 获取list中 指定位置的值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param index
	 * @return
	 */
	public static <T> T getListValue(String projectId, String service, Class<T> clazz, String key, long index) {
		if(host != null) {
			return RedisService.getListValue(projectId, service, clazz, key, index);
		} else {
			return MemoryService.getListValue(projectId, service, clazz, key, index);
		}
	}

	/**
	 * 获取指定范围的记录,范围为大于等于start,小于end
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static <T> List<T> getListValues(String projectId, String service, Class<T> clazz, String key, long start, long end) {
		if(host != null) {
			return RedisService.getListValues(projectId, service, clazz, key, start, end);
		} else {
			return MemoryService.getListValues(projectId, service, clazz, key, start, end);
		}
	}

	/**
	 * 删除List第一个值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static String deleteListValue(String projectId, String service, String key, int index) {
		if(host != null) {
			return RedisService.deleteListValue(projectId, service, key, index);
		} else {
			return MemoryService.deleteListValue(projectId, service, key, index);
		}
	}

	/**
	 * 删除List开始和结束范围内的数据,范围为大于等于start,小于end
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static String deleteListValue(String projectId, String service, String key, long start, long end) {
		if(host != null) {
			return RedisService.deleteListValue(projectId, service, key, start, end);
		} else {
			return MemoryService.deleteListValue(projectId, service, key, start, end);
		}
	}

	/**
	 * 删除List开始和结束范围内以外的数据,范围为大于等于start,小于end
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public static String deleteListValueOutOfRange(String projectId, String service, String key, long start, long end) {
		if(host != null) {
			return RedisService.deleteListValueOutOfRange(projectId, service, key, start, end);
		} else {
			return MemoryService.deleteListValueOutOfRange(projectId, service, key, start, end);
		}
	}

	// 以下为操作Set的方法
	/**
	 * 返回Set的长度
	 * 
	 * @param projectId 项目点号
	 * @param service   服务标志
	 * @param key       数据key
	 * @return
	 */
	public static long getSetLength(String projectId, String service, String key) {
		if(host != null) {
			return RedisService.getSetLength(projectId, service, key);
		} else {
			return MemoryService.getSetLength(projectId, service, key);
		}
	}

	/**
	 * 向Set中增加值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long addSetValue(String projectId, String service, String key, Object value) {
		if(host != null) {
			return RedisService.addSetValue(projectId, service, key, value);
		} else {
			return MemoryService.addSetValue(projectId, service, key, value);
		}
	}

	/**
	 * 向Set中增加数据值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static long addSetValues(String projectId, String service, String key, Object... values) {
		if(host != null) {
			return RedisService.addSetValues(projectId, service, key, values);
		} else {
			return MemoryService.addSetValues(projectId, service, key, values);
		}
	}

	/**
	 * 向Set中增加集合值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static <T> long addSetValues(String projectId, String service, String key, Collection<T> values) {
		if(host != null) {
			return RedisService.addSetValues(projectId, service, key, values);
		} else {
			return MemoryService.addSetValues(projectId, service, key, values);
		}
	}

	/**
	 * 获取Set中所有元素的值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @return
	 */
	public static <T> Set<T> getSetValues(String projectId, String service, Class<T> clazz, String key) {
		if(host != null) {
			return RedisService.getSetValues(projectId, service, clazz, key);
		} else {
			return MemoryService.getSetValues(projectId, service, clazz, key);
		}
	}

	/**
	 * 判断Set中是否包含某值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static Boolean containSetValue(String projectId, String service, String key, Object value) {
		if(host != null) {
			return RedisService.containSetValue(projectId, service, key, value);
		} else {
			return MemoryService.containSetValue(projectId, service, key, value);
		}
	}

	/**
	 * 删除Set的值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param value
	 * @return
	 */
	public static long deleteSetValue(String projectId, String service, String key, String value) {
		if(host != null) {
			return RedisService.deleteSetValue(projectId, service, key, value);
		} else {
			return MemoryService.deleteSetValue(projectId, service, key, value);
		}
	}

	/**
	 * 删除Set的集合值
	 * 
	 * @param projectId
	 * @param service
	 * @param key
	 * @param values
	 * @return
	 */
	public static long deleteSetValues(String projectId, String service, String key, Object... values) {
		if(host != null) {
			return RedisService.deleteSetValues(projectId, service, key, values);
		} else {
			return MemoryService.deleteSetValues(projectId, service, key, values);
		}
	}
}
