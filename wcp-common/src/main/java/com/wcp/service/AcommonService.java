package com.wcp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.annotation.ServiceSubscriber;
import com.wcp.data.DataTypeUtil;
import com.wcp.data.PackageUtil;
import com.wcp.data.ServiceGlobeInfo;
import com.wcp.data.WebServiceConst;
import com.wcp.http.HttpStatus;
import com.wcp.minio.MinioConfig;
import com.wcp.minio.MinioService;
import com.wcp.utils.StringUtil;
import io.minio.MinioClient;
import org.apache.log4j.Logger;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.*;
import java.util.Map.Entry;

/**
 * 服务接口
 * <AUTHOR>
 */
@Component
public  class AcommonService {
	private static Logger logger = Logger.getLogger(AcommonService.class);

	/** 通用服务分组容器 */
	private static Map<String, Class<?>> serviceGroupMap;
	/** 通用服务容器 */
	private static Map<String, Method> serviceMap;
	/** 通配符容器 */
	private static Map<String, Method> wildcardServiceMap;
	/**
	 * 接口路径和参数
	 */
	public static Map<String,JSONArray> interfaceReuqestMap;
	/**
	 * 类地址信息
	 */
	public static Map<String,JSONObject> urlMap=new HashMap<>();
	@Value("${wcp.excludeModel}")
	public static String excludeModel;
	@Autowired
	private ApplicationContext applicationContext;
	@Autowired
	private MinioClient minioClient;
	@Autowired
	private MinioConfig minioConfig;
	@PostConstruct
	public void initProject() {
		logger.info("###################Start loading service......#############");
		//扫描所有模块
		List<Class<?>> classes=new ArrayList<>();
		//扫描所有类上使用了ApiGroup注解的类
		Map<String, Object> beansWithAnnotation = applicationContext.getBeansWithAnnotation(ApiGroup.class);
		for (Object bean : beansWithAnnotation.values()) {
			//只获取原生类
			Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);
			classes.add(targetClass);
		}
		AcommonService.initialize(classes);
		//初始化minio配置
		MinioService.init(minioClient,minioConfig);
	}

	/**
	 * 返回当前服务路由地址
	 * @return
	 */
	public String getServicePath(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getServicePath();
	}
	/**
	 * 返回当前权限标志
	 * @return
	 */
	public String getWCPID(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getWCPID();
	}
	/**
	 * 返回当前项目点号
	 * @return
	 */
	public String getProjectId(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
        String projectId = "";
        if(serviceGlobeInfo == null) {
            projectId = param.getString("projectId");
        }
		projectId = serviceGlobeInfo.getProjectId();
		if(StringUtil.isEmpty(projectId)) {
			projectId = param.getString("projectId");
		}
		return projectId;
	}
	/**
	 * 返回当前行业点号
	 * @return
	 */
	public String getIndustryId(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getIndustryId();
	}
	/**
	 * 如果是多个行业，则返回数组
	 * @param param
	 * @return
	 */
	public String[] getIndustryIds(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getIndustryId().split(",");
	}
	/**
	 * 返回当前公司点号
	 * @return
	 */
	public String getCompanyId(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		String companyId = serviceGlobeInfo.getCompanyId();
		if(StringUtil.isEmpty(companyId)) {
			companyId = param.getString("companyId");
		}
		return companyId;
	}
	/**
	 * 返回当前调用的设备类型
	 * @return
	 */
	public String getDeviceType(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getDeviceType();
	}
	/**
	 * 返回当前调用请求服务
	 * @return
	 */
	public HttpServletRequest getRequest(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getRequest();
	}
	/**
	 * 返回当前响应服务
	 * @return
	 */
	public HttpServletResponse getResponse(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getResponse();
	}
	/**
	 * 返回session
	 * @return
	 */
	public HttpSession getHttpSession(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getHttpSession();
	}
	/**
	 * 返回调用者IP
	 * @return
	 */
	public String getIp(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getIp();
	}
	/**
	 * 返回调用者用户点号
	 * @return
	 */
	public String getUserId(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getUserId();
	}
	/**
	 * 返回调用者用户名称
	 * @return
	 */
	public String getUserName(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getUserName();
	}
	/**
	 * 返回调用者用户账号
	 * @return
	 */
	public String getUserAccount(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getUserAccount();
	}
	/**
	 * 返回调用者用户类型
	 * @return
	 */
	public String getUserType(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getUserType();
	}
	/**
	 * 返回调用者当前的权限Token标志
	 * @return
	 */
	public String getTokenKey(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getTokenKey();
	}
	/**
	 * 返回调用者当前的权限Token
	 * @return
	 */
	public String getToken(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getToken();
	}
	/**
	 * 是否需要验证登录,值为："ture" or "false"
	 * @return
	 */
	public String getLoginMust(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		if(serviceGlobeInfo.getLoginMust() == null) {
			return "true";
		} else {
			return serviceGlobeInfo.getLoginMust();
		}
	}
	/**
	 * 返回调用者失败返回的登录路由
	 * @return
	 */
	public String getLoginUrl(JSONObject param) {
		ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
		return serviceGlobeInfo.getLoginUrl();
	}

	/**
	 * 返回指定的类型数据
	 * @param param
	 * @param key
	 * @param clazz
	 * @param <T>
	 * @return
	 */
	public <T> T getFixedTypeData(JSONObject param, String key, Class<?> clazz) {
		Object data = param.get(key);
		return DataTypeUtil.transformData(clazz, data);
	}

	/**
	 * 返回指定的类型数据
	 * @param param
	 * @param key
	 * @param className
	 * @param <T>
	 * @return
	 */
	public <T> T getFixedTypeData(JSONObject param, String key, String className) {
		Object data = param.get(key);
		return DataTypeUtil.transformDataBySimpleName(className, data);
	}

	/**
	 * 设置结果不带标志
	 * @param result
	 */
	public void setResultNoMark(Map<String, Object> result) {
		result.put(WebServiceConst.RESULT_NOMARK, true);
	}

	/**
	 * 执行状态设置
	 * @param result
	 * @param success
	 */
	public void excuteStatus(Map<String, Object> result, boolean success) {
		result.put(WebServiceConst.SUCCESS, success);
	}



	public void addPackDataToResult(Map<String, Object> result, String key, Object data) {
		JSONObject dataObject = (JSONObject)result.get("data");
		if(dataObject == null) {
			dataObject = new JSONObject();
			result.put("data", dataObject);
		}
		dataObject.put(key, data);
	}

	public void addDefaultPackDataToResult(Map<String, Object> result, Object data) {
		result.put("data", data);
	}

	public synchronized static void initialize(List<Class<?>> classes)
	{
		if(serviceGroupMap == null) {
			serviceGroupMap = new HashMap<String, Class<?>>();
		}
		if(serviceMap == null) {
			serviceMap = new HashMap<String, Method>();
		}
		if(wildcardServiceMap == null) {
			wildcardServiceMap = new HashMap<String, Method>();
		}
		if(interfaceReuqestMap==null){
			interfaceReuqestMap=new HashMap<>();
		}
		for (Class<?> clazz : classes) {
			ApiGroup annotationGroup = clazz.getAnnotation(ApiGroup.class);
			String gruopServiceKey = "";
			if(annotationGroup != null) {
				gruopServiceKey = annotationGroup.serviceKey();
			}
			Method[] methods = clazz.getMethods();
			for (Method method : methods) {
				ServiceSubscriber annotation = method.getAnnotation(ServiceSubscriber.class);
				if (annotation != null) {
					String serviceKey = annotation.serviceKey();
					serviceKey = serviceKey.replaceAll("\\.", "/");
					if(serviceKey.endsWith("**")) { // 通配符的解析
						serviceKey = serviceKey.substring(0, serviceKey.length() - 2); 
						if(wildcardServiceMap.get(serviceKey) != null) {
							logger.error("接口方法重复,类:" + clazz.getName() + "中的方法:" + annotation.serviceKey() + "已存在.");
						} else {
							wildcardServiceMap.put(serviceKey, method);
						}
					} else {                        // 精确匹配解析
						if (serviceMap.get(serviceKey) != null) {
							logger.error("接口方法重复,类:" + clazz.getName() + "中的方法:" + annotation.serviceKey() + "已存在.");
						} else {
							serviceMap.put(serviceKey, method);
						}
					}
				}
				ApiService annotation1 = method.getAnnotation(ApiService.class);
				String modelName = method.getDeclaringClass().getSimpleName();
				if (annotation1 != null) {
					String serviceKey = annotation1.serviceKey();
					String serviceDesc=annotation1.value();//接口描述
					serviceKey = serviceKey.replaceAll("\\.", "/");
					ApiParam[] apiParams=annotation1.params();
					JSONArray jsonArray=new JSONArray();
					//如果该方法参数不为空
					if(apiParams!=null && apiParams.length>0){
						for (int i=0;i<apiParams.length;i++){
							JSONObject jsonObject=new JSONObject();
							jsonObject.put("value",apiParams[i].value());
							jsonObject.put("name",apiParams[i].name());
							jsonObject.put("clazz",apiParams[i].clazz());
							jsonObject.put("paramType",apiParams[i].paramType());
							jsonObject.put("required",apiParams[i].required());
							jsonArray.add(jsonObject);
						}
					}
					if(!gruopServiceKey.equals("")) {
						serviceKey =  gruopServiceKey + "/" + serviceKey;
					}
					if(serviceKey.endsWith("**")) { // 通配符的解析
						serviceKey = serviceKey.substring(0, serviceKey.length() - 2); 
						if(wildcardServiceMap.get(serviceKey) != null) {
							logger.error("接口方法重复,类:" + clazz.getName() + "中的方法:" + annotation1.serviceKey() + "已存在.");
						} else {
							wildcardServiceMap.put(serviceKey, method);
							if(annotation1.scaner()){
								String url=PackageUtil.classModelMap.get(clazz.getName());
								interfaceReuqestMap.put(serviceKey,jsonArray);
								JSONObject obj=new JSONObject();
								obj.put("url",url);
								obj.put("desc",serviceDesc);
								urlMap.put(serviceKey,obj);
							}

						}
					} else {                        // 精确匹配解析
						if (serviceMap.get(serviceKey) != null) {
							logger.error("接口方法重复,类:" + clazz.getName() + "中的方法:" + annotation1.serviceKey() + "已存在.");
						} else {
							serviceMap.put(serviceKey, method);
							if(annotation1.scaner()){
								String url=PackageUtil.classModelMap.get(clazz.getName());
								interfaceReuqestMap.put(serviceKey,jsonArray);
								JSONObject obj=new JSONObject();
								obj.put("url",url);
								obj.put("desc",serviceDesc);
								urlMap.put(serviceKey,obj);
							}
						}
					}
				}
			}
		}

	}
	/**
	 * 返回所有接口按分组进行归类的数据集合
	 * @return
	 */
	public static JSONArray getGroupServiceList() {
		JSONArray backArray = new JSONArray(); 
		Map<String, JSONObject> tempCacheMap = new HashMap<String, JSONObject>();
		Map<String, Method> handerMap = new HashMap<>();
		handerMap.putAll(serviceMap);
		handerMap.putAll(wildcardServiceMap);
		Iterator<Entry<String, Method>> iterator = handerMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, Method> entry = iterator.next();
			Method method = entry.getValue();
			ApiService apiService = method.getAnnotation(ApiService.class);
			if(apiService == null) continue;
			Class<?> clazz = method.getDeclaringClass();
			ApiGroup apiGroup = clazz.getAnnotation(ApiGroup.class);
			String tempClassify = apiService.classify();
			if(apiGroup != null && apiGroup.classify() != null && !apiGroup.classify().trim().equals("")) {
				tempClassify = apiGroup.classify();
			}
			// 获取分组存储对象
			JSONObject tempGroupObject = tempCacheMap.get(tempClassify);
			if(tempGroupObject == null) {
				if(apiGroup != null) {
					tempGroupObject = DataTypeUtil.transAnnotationToJson(apiGroup);
				} else {
					tempGroupObject = new JSONObject();
					tempGroupObject.put("classify", tempClassify);
					tempGroupObject.put("classifyName", apiService.classifyName());
				}
				tempCacheMap.put(tempClassify, tempGroupObject);
				backArray.add(tempGroupObject);
			}
			JSONArray apiServices = tempGroupObject.getJSONArray("children");
			if(apiServices == null) {
				apiServices = new JSONArray();
				tempGroupObject.put("children", apiServices);
			}
			JSONObject apiServiceObject = DataTypeUtil.transAnnotationToJson(apiService);
			apiServices.add(apiServiceObject);
		}
		return backArray;
	}

	/**
	 * 返回指定服务标志的方法名
	 * @param serviceKey
	 * @return
	 */
	public static Method getServiceMethod(String serviceKey)
	{
		Method method = serviceMap.get(serviceKey);
		if(method == null) {
			Iterator<Entry<String, Method>> iterator = wildcardServiceMap.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, Method> entry = iterator.next();
				if(serviceKey.startsWith(entry.getKey())) {
					method = entry.getValue();
					break;
				}
			}
		}
		return method;
	}
	
	/**
	 * 获取所有接口函数
	 * @return
	 */
	public static Map<String, Method> getServiceMap()
	{
		if(serviceMap==null) {
			serviceMap=new HashMap<>();
		}
		
		return serviceMap;
	}
}
