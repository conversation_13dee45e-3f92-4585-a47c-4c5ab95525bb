package com.wcp.thread;

import com.alibaba.fastjson.JSONObject;
import com.wcp.data.ServiceGlobeInfo;
import com.wcp.data.WebServiceConst;
import com.wcp.utils.StringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description: 本地线程缓存类
 * Author: qianchao
 * Date: 2024/3/8 13:50
 */
public class WcpThreadLocal {
    /**
     * 本地线程存储变量
     */
    private static final ThreadLocal<ServiceGlobeInfo> projectIdThreadLocal = new ThreadLocal<>();
    public static void setThreadLocalData(ServiceGlobeInfo serviceGlobeInfo) {
        projectIdThreadLocal.set(serviceGlobeInfo);
    }
    public static ServiceGlobeInfo getServiceGlobeInfo() {
       return projectIdThreadLocal.get();
    }
    /**
     * 获取项目点号
     * @return
     */
    public static  String getProjectId() {
        ServiceGlobeInfo serviceGlobeInfo=projectIdThreadLocal.get();
        return  serviceGlobeInfo!=null&& StringUtil.isNotEmpty(serviceGlobeInfo.getProjectId())?serviceGlobeInfo.getProjectId():"default";
    }
    /***
     * 获取userId
     * @return
     */
    public static String getUserId(){
        ServiceGlobeInfo serviceGlobeInfo=projectIdThreadLocal.get();
        return  serviceGlobeInfo!=null&& StringUtil.isNotEmpty(serviceGlobeInfo.getUserId())?serviceGlobeInfo.getUserId():null;
    }

    /**
     * 获取用户名
     * @return
     */
    public static String getUserName(){
        ServiceGlobeInfo serviceGlobeInfo=projectIdThreadLocal.get();
        return  serviceGlobeInfo!=null&& StringUtil.isNotEmpty(serviceGlobeInfo.getUserName())?serviceGlobeInfo.getUserName():null;
    }

    /**
     * 账号
     * @return
     */
    public static String getUserAccount(){
        ServiceGlobeInfo serviceGlobeInfo=projectIdThreadLocal.get();
        return  serviceGlobeInfo!=null&& StringUtil.isNotEmpty(serviceGlobeInfo.getUserAccount())?serviceGlobeInfo.getUserAccount():null;
    }

    /**
     * 获取公司id
     * @return
     */
    public static String getCompanyId(){
        ServiceGlobeInfo serviceGlobeInfo=projectIdThreadLocal.get();
        return  serviceGlobeInfo!=null&& StringUtil.isNotEmpty(serviceGlobeInfo.getCompanyId())?serviceGlobeInfo.getCompanyId():null;
    }

    public static HttpServletRequest getRequest(JSONObject param) {
        ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
        return serviceGlobeInfo.getRequest();
    }

    /**
     * 返回当前响应服务
     * @return
     */
    public static HttpServletResponse getResponse(JSONObject param) {
        ServiceGlobeInfo serviceGlobeInfo = param.getObject(WebServiceConst.SERVICE_GLOBE_INFO, ServiceGlobeInfo.class);
        return serviceGlobeInfo.getResponse();
    }

    /**
     * 释放本地线程空间
     */
    public static void clear() {
        projectIdThreadLocal.remove();
    }
}
