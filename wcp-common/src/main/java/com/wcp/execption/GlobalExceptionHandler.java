package com.wcp.execption;

import com.alibaba.fastjson.JSONObject;
import com.wcp.http.HttpStatus;
import com.wcp.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 空值异常
     * @param e
     * @param request
     * @return
     */
    @ExceptionHandler(NullPointerException.class)
    public JSONObject handleNULLException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String errorMessage = String.format("请求地址'%s',发生空对值异常.", requestURI);
        log.error("请求地址'{}',发生空值异常.", errorMessage, e);
        JSONObject jsonObject=new JSONObject();
        jsonObject.put(HttpStatus.CODE, HttpStatus.ERROR);
        jsonObject.put(HttpStatus.MSG, errorMessage+e.getMessage());
        return jsonObject;
    }

    /**
     * 发生SQL异常
     * @param e
     * @param request
     * @return
     */
    @ExceptionHandler(SQLException.class)
    public JSONObject handleSQLException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String errorMessage = String.format("请求地址'%s',发生SQL异常.", requestURI);
        log.error("发生异常: {}", errorMessage, e);
        JSONObject jsonObject=new JSONObject();
        jsonObject.put(HttpStatus.CODE, HttpStatus.ERROR);
        jsonObject.put(HttpStatus.MSG, errorMessage+e.getMessage());
        return jsonObject;
    }
    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public JSONObject handleServiceException(ServiceException e, HttpServletRequest request) {
        //log.error(e.getMessage(), e);

        String requestURI = request.getRequestURI();
        String errorMessage = String.format("请求地址'%s',发生异常", requestURI);
        log.error("发生异常: {}", errorMessage, e);
        Integer code = e.getCode();
        JSONObject jsonObject=new JSONObject();
        if(StringUtil.isNotNull(code)){
            jsonObject.put(HttpStatus.CODE, code);
            jsonObject.put(HttpStatus.MSG, errorMessage+e.getMessage());
        }else{
            jsonObject.put(HttpStatus.CODE, HttpStatus.ERROR);
            jsonObject.put(HttpStatus.MSG, errorMessage+e.getMessage());
        }
        jsonObject.put(HttpStatus.ISSUCCESS, false);
        return  jsonObject;
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public JSONObject handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String className=e.getClass().getSimpleName();
        String errorMessage ="";
        if(className.contains("RuntimeException")){
            errorMessage = String.format("请求地址'%s',发生未知异常", requestURI);
            log.error("发生未知异常: {}", errorMessage, e);
        }else{
            errorMessage = String.format("请求地址'%s',发生异常", requestURI);
            log.error("发生异常: {}", errorMessage, e);

        }
        JSONObject jsonObject=new JSONObject();
        jsonObject.put(HttpStatus.CODE, HttpStatus.ERROR);
        jsonObject.put(HttpStatus.MSG, errorMessage+e.getMessage());
        return jsonObject;
    }



    /**
     * 系统异常
     */
    /*@ExceptionHandler(Exception.class)
    public JSONObject handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e.getMessage());
        String errorMessage = String.format("请求地址'%s',发生系统异常.", requestURI);
        JSONObject jsonObject=new JSONObject();
        jsonObject.put(HttpStatus.CODE, HttpStatus.ERROR);
        jsonObject.put(HttpStatus.MSG, errorMessage+e.getMessage());
        return jsonObject;
    }*/
}
