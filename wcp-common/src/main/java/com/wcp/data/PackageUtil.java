package com.wcp.data;

import com.wcp.utils.StringUtil;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.net.JarURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

public class PackageUtil {
    public static void main(String[] args) {
        String packageName = "com.wcp.core.gis.resources.colorbars";
        List<String> queryFilePaths = queryChildrenFiles(packageName, null);
        for (String filePath : queryFilePaths) {
            System.out.println(filePath);
        }
    }

    public static List<Class<?>> getClasses(String packageName, Class<?> clazz) {
        List<Class<?>> cls = new ArrayList<Class<?>>();
        Set<Class<?>> classes = getClassSet(packageName);
        for (Class<?> clazz1 : classes) {
           // if (clazz1 != null && clazz.isAssignableFrom(clazz1)) {
            if (clazz1 != null) {
                cls.add(clazz1);
            }
        }
        return cls;
    }
    //存储class对应的项目地址
    public static Map<String,String> classModelMap=new HashMap<>();
    /**
     * 获取类加载器
     *
     * @param @return 设定文件
     * @return ClassLoader    返回类型
     * @throws
     * @Title: getClassLoader
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    public static ClassLoader getClassLoader() {
        return Thread.currentThread().getContextClassLoader();
    }

    /**
     * 加载类
     * 需要提供类名与是否初始化的标志，
     * 初始化是指是否执行静态代码块
     *
     * @param @param  className
     * @param @param  isInitialized  为提高性能设置为false
     * @param @return 设定文件
     * @return Class<?>    返回类型
     * @throws
     * @Title: loadClass
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    public static Class<?> loadClass(String className, boolean isInitialized) {

        Class<?> cls = null;
        try {
            cls = Class.forName(className, isInitialized, getClassLoader());
        } catch (Exception e) {
            //e.printStackTrace();
            //throw new RuntimeException(e);
        } catch (NoClassDefFoundError e) {
            // TODO: handle exception
        }
        return cls;
    }

    /**
     * 加载指定包下的所有类
     *
     * @param @param  packageName
     * @param @return 设定文件
     * @return Set<Class < ?>>    返回类型
     * @throws
     * @Title: getClassSet
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    /*public static Set<Class<?>> getClassSet(String packageName) {
        Set<Class<?>> classSet = new HashSet<Class<?>>();
        try {
            Enumeration<URL> urls = getClassLoader().getResources(packageName.replace(".", "/"));
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                if (url != null) {
                    String protocol = url.getProtocol();
                    if (protocol.equals("file")) {
                        String packagePath = URLDecoder.decode(url.getFile(), "UTF-8");
                        // String packagePath =url.getPath().replaceAll("%20",  "");
                        addClass(classSet, packagePath, packageName);

                    } else if (protocol.equals("jar")) {
                        JarURLConnection jarURLConnection = (JarURLConnection) url.openConnection();
                        if (jarURLConnection != null) {
                            JarFile jarFile = jarURLConnection.getJarFile();
                            if (jarFile != null) {
                                Enumeration<JarEntry> jarEntries = jarFile.entries();
                                while (jarEntries.hasMoreElements()) {
                                    JarEntry jarEntry = jarEntries.nextElement();
                                    String jarEntryName = jarEntry.getName();
                                    if (jarEntryName.endsWith(".class")) {
                                        String className = jarEntryName.substring(0, jarEntryName.lastIndexOf("."))
                                                .replaceAll("/", ".");
                                        doAddClass(classSet, className);
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return classSet;
    }*/

    /**
     * 适配通配符路径 com.wcp.*
     * @param packageName
     * @return
     */
    public static Set<Class<?>> getClassSet(String packageName) {
        Set<Class<?>> classSet = new HashSet<>();
        try {
            // 将包名转换成路径形式
            if(packageName.lastIndexOf("*")>0){
                packageName=packageName.substring(0,packageName.length()-2);
            }
            String packagePath = packageName.replace(".", "/");
            Enumeration<URL> urls = getClassLoader().getResources(packagePath);
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                if (url != null) {
                    String protocol = url.getProtocol();
                    if (protocol.equals("file")) {
                        String packageFilePath = URLDecoder.decode(url.getFile(), "UTF-8");
                        addClassWithWildcard(classSet, packageFilePath, packageName,url);
                    } else if (protocol.equals("jar")) {
                        JarURLConnection jarURLConnection = (JarURLConnection) url.openConnection();
                        if (jarURLConnection != null) {
                            JarFile jarFile = jarURLConnection.getJarFile();
                            if (jarFile != null) {
                                Enumeration<JarEntry> jarEntries = jarFile.entries();
                                while (jarEntries.hasMoreElements()) {
                                    JarEntry jarEntry = jarEntries.nextElement();
                                    String jarEntryName = jarEntry.getName();
                                    if (jarEntryName.endsWith(".class")) {
                                        String className = jarEntryName.substring(0, jarEntryName.lastIndexOf("."))
                                                .replaceAll("/", ".");
                                        doAddClass(classSet, className);
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return classSet;
    }

        private static boolean matches(String packageName, String className) {
            // 将通配符 * 替换成正则表达式 .*
            String packageRegex = packageName.replace(".", "\\.").replace("*", ".*");
            return className.matches(packageRegex);
        }
        private static void addClassWithWildcard(Set<Class<?>> classSet, String packagePath, String packageName,URL url) {
            try {
                // 获取包路径下的所有文件
                File packageDirectory = new File(packagePath);
                if (packageDirectory.exists() && packageDirectory.isDirectory()) {
                    File[] files = packageDirectory.listFiles();
                    if (files != null) {
                        for (File file : files) {
                            String fileName = file.getName();
                            if (file.isFile() && fileName.endsWith(".class")) {
                                String className = packageName + "." + fileName.substring(0, fileName.lastIndexOf("."));
                                doAddClass(classSet, className);
                                classModelMap.put(className,url.getPath());
                            } else if (file.isDirectory()) {
                                // 递归处理子目录
                                addClassWithWildcard(classSet, file.getAbsolutePath(), packageName + "." + fileName,url);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    /**
     * 添加文件到SET集合
     *
     * @param @param classSet
     * @param @param packagePath
     * @param @param packageName    设定文件
     * @return void    返回类型
     * @throws
     * @Title: addClass
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    private static void addClass(Set<Class<?>> classSet, String packagePath, String packageName) {
        File[] files = new File(packagePath).listFiles(new FileFilter() {
            @Override
            public boolean accept(File file) {
                return (file.isFile() && file.getName().endsWith(".class") || file.isDirectory());
            }
        });

        for (File file : files) {

            String fileName = file.getName();

            if (file.isFile()) {
                String className = fileName.substring(0, fileName.lastIndexOf("."));

                if (packageName != null && !packageName.equals("")) {

                    className = packageName + "." + className;
                }
                // 添加
                doAddClass(classSet, className);
            } else {
                // 子目录
                String subPackagePath = fileName;
                if (!StringUtil.isEmpty(packagePath)) {
                    subPackagePath = packagePath + "/" + subPackagePath;
                }
                String subPackageName = fileName;
                if (!StringUtil.isEmpty(packageName)) {
                    subPackageName = packageName + "." + subPackageName;
                }
                addClass(classSet, subPackagePath, subPackageName);
            }
        }

    }

    private static void doAddClass(Set<Class<?>> classSet, String className) {
        Class<?> cls = loadClass(className, false);
        classSet.add(cls);
    }

    /**
     * 查询指定包下指定后缀的文件名称
     *
     * @param packageName 指定的包路径
     * @param suffix      文件后纵，null表示不指定文件后缀
     * @return
     */
    public static List<String> queryChildrenFiles(String packageName, String suffix) {
        List<String> filePaths = new ArrayList<String>();
        String rootPath = packageName.replace(".", "/");
        try {
            Enumeration<URL> urls = getClassLoader().getResources(rootPath);
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                if (url != null) {
                    String protocol = url.getProtocol();
                    if (protocol.equals("file")) { // 如果是常规文件
                        String packagePath = URLDecoder.decode(url.getFile(), "UTF-8");
                        sreachChildrenFiles(filePaths, rootPath, packagePath, suffix);
                    } else if (protocol.equals("jar")) {
                        JarURLConnection jarURLConnection = (JarURLConnection) url.openConnection();
                        if (jarURLConnection != null) {
                            JarFile jarFile = jarURLConnection.getJarFile();
                            if (jarFile != null) {
                                Enumeration<JarEntry> jarEntries = jarFile.entries();
                                while (jarEntries.hasMoreElements()) {
                                    JarEntry jarEntry = jarEntries.nextElement();
                                    String jarEntryName = jarEntry.getName();
                                    jarEntryName = jarEntryName.replace("\\", "/");
                                    if (!jarEntry.isDirectory() && jarEntryName.startsWith(rootPath)
                                            && (suffix == null || jarEntryName.endsWith(suffix))) {
                                        if (jarEntryName.matches(rootPath + "/[^/]+")) {
                                            int index = jarEntryName.lastIndexOf("/");
                                            filePaths.add(jarEntryName.substring(index + 1));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return filePaths;
    }

    /**
     * 搜索指定包下的文件
     *
     * @param packageName 指定的包路径
     * @param deepSearch  是否继续向下搜索文件
     * @param suffix      文件后纵，null表示不指定文件后缀
     * @return
     */
    public static List<String> queryFilePaths(String packageName, boolean deepSearch, String suffix) {
        List<String> filePaths = new ArrayList<String>();
        String rootPath = packageName.replace(".", "/");
        try {
            Enumeration<URL> urls = getClassLoader().getResources(rootPath);
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                if (url != null) {
                    String protocol = url.getProtocol();
                    if (protocol.equals("file")) { // 如果是常规文件
                        String packagePath = URLDecoder.decode(url.getFile(), "UTF-8");
                        sreachChildrenPaths(filePaths, rootPath, packagePath, deepSearch, suffix);
                    } else if (protocol.equals("jar")) {
                        JarURLConnection jarURLConnection = (JarURLConnection) url.openConnection();
                        if (jarURLConnection != null) {
                            JarFile jarFile = jarURLConnection.getJarFile();
                            if (jarFile != null) {
                                Enumeration<JarEntry> jarEntries = jarFile.entries();
                                while (jarEntries.hasMoreElements()) {
                                    JarEntry jarEntry = jarEntries.nextElement();
                                    String jarEntryName = jarEntry.getName();
                                    if (!jarEntry.isDirectory() && jarEntryName.startsWith(rootPath)
                                            && (suffix == null || jarEntryName.endsWith(suffix))) {
                                        if (!deepSearch && jarEntryName.matches(rootPath + "/[^/]+")) {
                                            filePaths.add(jarEntryName);
                                        } else if (deepSearch) {
                                            filePaths.add(jarEntryName);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return filePaths;
    }

    /**
     * 搜索指定路径下的文件路径，并把文件路径加入到搜索集合中
     *
     * @param filePaths   路径集合
     * @param packagePath 包路径
     * @param deepSearch  是否往下一级继续搜索
     * @param suffix      指定文件的后缀，如果不指定，则设置为null即可
     */
    public static void sreachChildrenPaths(List<String> filePaths, String rootPackagePath, String packagePath, boolean deepSearch,
                                           String suffix) {
        if (!StringUtil.isNotEmpty(packagePath))
            return;
        File[] lsitFiles = new File(packagePath).listFiles();
        for (File tempFile : lsitFiles) {
            String tempFileName = tempFile.getName();
            if (tempFile.isDirectory() && deepSearch) {
                String childrenDirectoryPath = packagePath + "/" + tempFileName;
                sreachChildrenPaths(filePaths, rootPackagePath, childrenDirectoryPath, deepSearch, suffix);
            } else if (tempFile.isFile() && (suffix == null || tempFileName.endsWith(suffix))) {
                String absolutePath = tempFile.getAbsolutePath();
                absolutePath = absolutePath.replace("\\", "/");
                int index = absolutePath.indexOf(rootPackagePath);
                if (index > -1) {
                    filePaths.add(absolutePath.substring(index));
                }
            }
        }
    }

    /**
     * 搜索指定路径下的文件路径，并把文件路径加入到搜索集合中
     *
     * @param filePaths   路径集合
     * @param packagePath 包路径
     * @param rootPackagePath  是否往下一级继续搜索
     * @param suffix      指定文件的后缀，如果不指定，则设置为null即可
     */
    public static void sreachChildrenFiles(List<String> filePaths, String rootPackagePath, String packagePath,
                                           String suffix) {
        if (!StringUtil.isNotEmpty(packagePath))
            return;
        File[] lsitFiles = new File(packagePath).listFiles();
        for (File tempFile : lsitFiles) {
            String tempFileName = tempFile.getName();
            if (tempFile.isFile() && (suffix == null || tempFileName.endsWith(suffix))) {
                String absolutePath = tempFile.getAbsolutePath();
                absolutePath = absolutePath.replace("\\", "/");
                int index = absolutePath.indexOf(rootPackagePath);
                if (index > -1) {
                    filePaths.add(tempFileName);
                }
            }
        }
    }
}
