package com.wcp.data;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.security.AnyTypePermission;
import com.wcp.utils.ArrayUtil;
import com.wcp.utils.DateUtil;
import com.wcp.utils.FastReflec;
import org.springframework.stereotype.Component;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.lang.annotation.Annotation;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.Map.Entry;


public class DataTypeUtil
{
	private static Map<String, Class<?>> nameClassMap = new HashMap<String, Class<?>>();
	private static Map<Class<?>, String> classNameMap = new HashMap<Class<?>, String>();
	private static Map<Class<?>, Object> defaultValueMap = new HashMap<Class<?>, Object>();
	private static XStream xs = new XStream(new DomDriver("utf-8"));
	static {
		String filePath = "/nameclass.properties";
		InputStream is = DataTypeUtil.class.getResourceAsStream(filePath);
		Reader reader = new InputStreamReader(is);
		Properties properties = new Properties();
		try {
			properties.load(reader);
			Iterator<Map.Entry<Object, Object>> iterator = properties.entrySet().iterator();
			while (iterator.hasNext()) {
				Map.Entry<Object, Object> entry = iterator.next();
				String tempKey = entry.getKey().toString();
				String tempValue = entry.getValue().toString();
				if(tempKey != null && tempValue != null && !tempKey.toString().trim().equals("")
						&& !tempValue.toString().trim().equals("")) {
					Class<?> clazz = null;
					if (tempKey.equals("long")) {
						nameClassMap.put("long", Long.TYPE);
						classNameMap.put(Long.TYPE, "long");
						defaultValueMap.put(Long.TYPE, 0l);
					} else if (tempKey.equals("int")) {
						nameClassMap.put("int", Integer.TYPE);
						classNameMap.put(Integer.TYPE, "int");
						defaultValueMap.put(Integer.TYPE, 0);
					} else if (tempKey.equals("short")) {
						nameClassMap.put("short", Short.TYPE);
						classNameMap.put(Short.TYPE, "short");
						byte tempShort = 0;
						defaultValueMap.put(Short.TYPE, tempShort);
					} else if (tempKey.equals("byte")) {
						nameClassMap.put("byte", Byte.TYPE);
						classNameMap.put(Byte.TYPE, "byte");
						byte tempByte = 0;
						defaultValueMap.put(Byte.TYPE, tempByte);
					} else if (tempKey.equals("float")) {
						nameClassMap.put("float", Float.TYPE);
						classNameMap.put(Float.TYPE, "float");
						defaultValueMap.put(Float.TYPE, 0f);
					} else if (tempKey.equals("double")) {
						nameClassMap.put("double", Double.TYPE);
						classNameMap.put(Double.TYPE, "double");
						defaultValueMap.put(Double.TYPE, 0.0);
					} else if (tempKey.equals("boolean")) {
						nameClassMap.put("boolean", Boolean.TYPE);
						classNameMap.put(Boolean.TYPE, "boolean");
						defaultValueMap.put(Boolean.TYPE, false);
					} else {
						String clazzName = tempValue.toString().trim();
						try {
							clazz = FastReflec.forName(clazzName);
						} catch (ClassNotFoundException e) {
							System.out.println("DataTypeUtil: Cann't find the class '" + clazzName + "'!");
						}
						nameClassMap.put(tempKey.toString().trim(), clazz);
						classNameMap.put(clazz, tempKey.toString().trim());
						defaultValueMap.put(clazz, null);
					}
				}
			}
		} catch (IOException e) {
			System.out.println("DataTypeUtil: Cann't find the file '" + filePath + "'!");
		}
		if(is != null) {
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	/**
	 * 获取泛型类型
	 * @param simpleType
	 * @return
	 */
	public static Class<?>[] getGenericTypeClass(String simpleType) {
		String mainSimpleType = simpleType.trim();
		int childIdx1 = mainSimpleType.indexOf("<");
		int childIdx2 = mainSimpleType.indexOf(">");
		String[] tempArray = new String[0];
		if(childIdx1 >= 0 && childIdx2 > childIdx1) {
			mainSimpleType = mainSimpleType.substring(childIdx1 + 1, childIdx2);
			tempArray = mainSimpleType.split(",");
		}
		Class<?>[] tempClasses = new Class<?>[tempArray.length];
		for (int i = 0; i < tempClasses.length; i++) {
			tempClasses[i] = getTypeClass(tempArray[i].trim());
		}
		return tempClasses;
	}
	/**
	 * 获取泛型类型
	 * @param simpleType
	 * @return
	 */
	public static String[] getGenericType(String simpleType) {
		String mainSimpleType = simpleType.trim();
		int childIdx1 = mainSimpleType.indexOf("<");
		int childIdx2 = mainSimpleType.indexOf(">");
		String[] tempArray = new String[0];
		if(childIdx1 >= 0 && childIdx2 > childIdx1) {
			mainSimpleType = mainSimpleType.substring(childIdx1 + 1, childIdx2);
			tempArray = mainSimpleType.split(",");
		}
		String[] tempTypes = new String[tempArray.length];
		for (int i = 0; i < tempTypes.length; i++) {
			tempTypes[i] = tempArray[i].trim();
		}
		return tempTypes;
	}
	/**
	 * 通过类名返回类的字节码
	 * @param simpleType  类完整路径，有些类可以简写
	 * @return
	 */
	public static Class<?> getTypeClass(String simpleType) {
		String mainSimpleType = simpleType.trim();
		// 如果有泛型，去除泛型
		int childIdx = mainSimpleType.indexOf("<");
		if(childIdx >= 0) {
			mainSimpleType = mainSimpleType.substring(0, childIdx);
		}
		String arrSimpleType = "";
		int arrIdx = mainSimpleType.indexOf("[]");
		if(arrIdx > -1) {
			arrSimpleType = mainSimpleType.substring(arrIdx);
			mainSimpleType = mainSimpleType.substring(0, arrIdx);
		}
		Class<?> clazz = nameClassMap.get(mainSimpleType);
		if(clazz == null) {
			try {
				clazz = FastReflec.forName(mainSimpleType);
			} catch (ClassNotFoundException e) {
				System.out.println("DataTypeUtil: Cann't find the class '" + simpleType + "'!");
			}
		}
		// 说明为外部设置的数据类
		if(arrIdx > -1) {
			int[] dimSize = new int[arrSimpleType.length() / 2];
			clazz = Array.newInstance(clazz, dimSize).getClass();
		}
		return clazz;
	}
	/**
	 * 通过类名返回类的字节码
	 * @param clazz  类完整路径，有些类可以简写
	 * @return
	 */
	public static String getTypeName(Class<?> clazz) {
		if(clazz == null) return "";
		Class<?> mainClass = clazz;
		while (mainClass.isArray()) {
			mainClass = mainClass.getComponentType();
		}
		String className = classNameMap.get(mainClass);
		if(className == null) {
			className = mainClass.getName();
		}
		if(clazz.isArray()) {
			int clazzDim = clazz.getName().split("\\[").length - 1;
			for (int i = 0; i < clazzDim; i++) {
				className += "[]";
			}
		}
		return className;
	}
	/**
	 * 参数是否是基本类型
	 * @param clazz
	 * @return
	 */
	public static boolean isBaseType(Class<?> clazz) {
		if(clazz.equals(Long.TYPE) || clazz.equals(Long.class) || clazz.equals(String.class)
			|| clazz.equals(Integer.TYPE) || clazz.equals(Integer.class)
			|| clazz.equals(Short.TYPE) || clazz.equals(Short.class)
			|| clazz.equals(Byte.TYPE) || clazz.equals(Byte.class)
			|| clazz.equals(Float.TYPE) || clazz.equals(Float.class)
			|| clazz.equals(Double.TYPE) || clazz.equals(Double.class)
			|| clazz.equals(Boolean.TYPE) || clazz.equals(Boolean.class)) {
			return true;
		} else {
			return false;
		}
	}
	/**
	 * 参数是否是时间类型
	 * @param clazz
	 * @return
	 */
	public static boolean isTimeType(Class<?> clazz) {
		if(Calendar.class.isAssignableFrom(clazz) || Date.class.isAssignableFrom(clazz)
			|| Timestamp.class.isAssignableFrom(clazz) || LocalDate.class.isAssignableFrom(clazz)
			|| LocalDateTime.class.isAssignableFrom(clazz) || LocalTime.class.isAssignableFrom(clazz)) {
			return true;
		} else {
			return false;
		}
	}
	
	public static Object getDefaultBaseTypeValue(Class<?> clazz) {
		if(clazz.equals(Long.TYPE)) {
			return 0l;
		} else if(clazz.equals(Integer.TYPE)) {
			return 0;
		} else if(clazz.equals(Short.TYPE)) {
			return Short.parseShort("0");
		} else if(clazz.equals(Byte.TYPE)) {
			return Byte.parseByte("0");
		} else if(clazz.equals(Float.TYPE)) {
			return 0.0f;
		} else if(clazz.equals(Double.TYPE)) {
			return 0.0;
		} else if(clazz.equals(Boolean.TYPE)) {
			return false;
		} else {
			return null;
		}
	}
	@SuppressWarnings("rawtypes")
	public static Map createDefaultMap(Class<?> clazz) {
		try {
			return (Map)clazz.newInstance();
		} catch (Exception e) {
			return new LinkedHashMap<>();
		}
	}
	@SuppressWarnings("rawtypes")
	public static Collection createDefaultCollection(Class<?> clazz) {
		try {
			return (Collection)clazz.newInstance();
		} catch (Exception e) {
			return new ArrayList<>();
		}
	}
	/**
	 * 创建默认数据
	 * @param clazz
	 * @return
	 */
	public static Object createDefaultObject(Class<?> clazz) {
		if(clazz == null) {
			return null;
		} else if(clazz.isArray()) {
			return createDefaultArray(clazz, 0);
		} else if(clazz.isAssignableFrom(Map.class)) {
			return createDefaultMap(clazz);
		} else if(clazz.isAssignableFrom(Collection.class)) {
			return createDefaultCollection(clazz);
		} else if(clazz.isAssignableFrom(Calendar.class)
				|| clazz.isAssignableFrom(Date.class)
				|| clazz.isAssignableFrom(Timestamp.class)
				|| clazz.isAssignableFrom(LocalDate.class)
				|| clazz.isAssignableFrom(LocalDateTime.class)) {
			return transformData(clazz, Calendar.getInstance());
		} else {
			try {
				return clazz.newInstance();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	/**
	 * 创建默认的数据
	 * @param clazz
	 * @param length
	 * @return
	 */
	public static Object createDefaultArray(Class<?> clazz, int... length) {
		//找到该数据最上层字节码描述
		int dimSize = 1;
		Class<?> baseClazz = clazz.getComponentType();
		while (baseClazz.isArray()) {
			baseClazz = baseClazz.getComponentType();
			dimSize += 1;
		}
		int[] tempDimLength = new int[dimSize];
		tempDimLength[0] = length[0];
		Object tempArray = Array.newInstance(baseClazz, tempDimLength);
		int[] nextLength = ArrayUtil.subArray(length, 1, length.length);
		if(nextLength.length == 0) return tempArray;
		for (int i = 0; i < length[0]; i++) {
			Array.set(tempArray, i, transformData(clazz.getComponentType(), createDefaultArray(baseClazz, nextLength), false));
		}
		return tempArray;
	}
	/**
	 * 按给定的数据创建数组
	 * @param clazz
	 * @param value
	 * @param length
	 * @return
	 */
	public static Object createDefaultValueArray(Class<?> clazz, Object value, int... length) {
		//找到该数据最上层字节码描述
		int dimSize = 1;
		Class<?> baseClazz = clazz.getComponentType();
		while (baseClazz.isArray()) {
			baseClazz = baseClazz.getComponentType();
			dimSize += 1;
		}
		Object tempValue = transformData(baseClazz, value, false);
		int[] tempDimLength = new int[dimSize];
		tempDimLength[0] = length[0];
		Object tempArray = Array.newInstance(baseClazz, tempDimLength);
		int[] nextLength = ArrayUtil.subArray(length, 1, length.length);
		if(nextLength.length == 0) {
			for (int i = 0; i < length[0]; i++) {
				Array.set(tempArray, i, tempValue);
			}
			return tempArray;
		}
		for (int i = 0; i < length[0]; i++) {
			Array.set(tempArray, i, transformData(clazz.getComponentType(), createDefaultArray(baseClazz, nextLength), false));
		}
		return tempArray;
	}

	/**
	 * 通过简单的类型定义转换数据
	 * @param dataType
	 * @param data
	 * @return
	 */
	public static <T> T transformDataBySimpleName(String dataType, Object data) {
		Class<?> clazz = getTypeClass(dataType);
		if(clazz == null) return null;
		return transformData(clazz, data);
	}
	/**
	 * 通过简单的类型定义转换数据
	 * @param dataType
	 * @param data
	 * @param newCopy     是否强制转换
	 * @return
	 */
	public static <T> T transformDataBySimpleName(String dataType, Object data, boolean newCopy) {
		Class<?> clazz = getTypeClass(dataType);
		if(clazz == null) return null;
		return transformData(clazz, data, newCopy);
	}
	
	@SuppressWarnings("unchecked")
	public static <T> T transformData(Class<?> clazz, Object data) {
		return (T)transformData(clazz, data, false, null, null);
	}
	
	public static <T> T transformData(Class<?> clazz, Object data, boolean isCopy) {
		return transformData(clazz, data, isCopy, null, null);
	}
	
	public static boolean isJSONType(String str) {
	    boolean result = false;
	    if (str != null && !str.trim().equals("")) {
	        str = str.trim();
	        if (str.startsWith("{") && str.endsWith("}")) {
	            result = true;
	        } else if (str.startsWith("[") && str.endsWith("]")) {
	            result = true;
	        }
	    }
	    return result;
	}
	

	public static boolean isXmlDocument(String xmlStr) {
		String tempXmlStr = xmlStr.trim();
		if(!tempXmlStr.startsWith("<") && !tempXmlStr.endsWith(">")) {
			return false;
		}
		boolean flag = true;
		try {
			DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = documentBuilderFactory.newDocumentBuilder();
		//	builder.parse(xmlStr);

			builder.parse(new InputSource(new StringReader(xmlStr)));
		} catch (Exception e) {
			e.printStackTrace();
			flag = false;
		}
		return flag;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> T transformData(Class<?> clazz, Object data, boolean isCopy, Class<?> keyClass, Class<?> valueClass) {
		//如果传入数据为空，还回所给类型默认值
		if(data == null || (clazz != null && isBaseType(clazz) && data.toString().trim().equals(""))) {
//			if(clazz != null && clazz.equals(String.class) && data.toString().trim().equals("")) {
//				return (T)data.toString();
//			}
			return (T)defaultValueMap.get(clazz);
		}
		//如果父类没有给出数据类型，则直接返回，或查看是否是标志类
		if(clazz == null) {
			if(Map.class.isAssignableFrom(data.getClass())) {
				Map map = (Map)data;
				Object tempType = map.get("type");
				Object tempData = map.get("data");
				if(tempType != null) {
					Class<?> tempClazz = getTypeClass(tempType.toString());
					//如果此数据不是字节码，则认为是Map里面的一个元素，直接返回即可
					if(tempClazz == null) return (T)data;
					return transformData(tempClazz, tempData, isCopy, null, null);
				}
			}
			return (T)data;
		}
		//如果数据是转换类型的子类，者直接返回
//		if(!Collection.class.isAssignableFrom(clazz) && !clazz.isArray() &&
//				!Map.class.isAssignableFrom(clazz) && clazz.isAssignableFrom(data.getClass())) {
//			return data;
//		}
		if(clazz.isAssignableFrom(data.getClass()) && !isCopy) {
			return (T)data;
		}
		if(data instanceof String) {
			// 看字符串是否是json数据
			try {
				if(isJSONType((String)data)) {
					 Object tempObject = JSON.parse((String)data);
				     return transformData(clazz, tempObject, false, keyClass, valueClass);
				}
		    } catch (Exception e) {}
			// 看字符串是否是xml文档数据
			try {
				if(isXmlDocument((String)data)) {
					// 设置默认的安全性
					XStream.setupDefaultSecurity(xs);
					xs.addPermission(AnyTypePermission.ANY);
					 Object tempObject = xs.fromXML((String)data);
				     return transformData(clazz, tempObject, false, keyClass, valueClass);
				}
		    } catch (Exception e) {
				e.printStackTrace();
			}
		}
		//集合转换为数组
		if(clazz.isArray()) {
			String clazzName = getTypeName(clazz);
			if(data instanceof String) {
				try {
					data = JSONArray.parseArray((String)data);
				} catch (Exception e) {
					throw new RuntimeException("DataTypeUtil: Cann't transform non array type to " + clazzName + ". ");
				}
			}
			if(!data.getClass().isArray() && !Collection.class.isAssignableFrom(data.getClass())) {
				throw new RuntimeException("DataTypeUtil: Cann't transform non array type to " +clazzName + ". ");
			}
			//找到该数据最上层字节码描述
			int dimSize = 1;
			Class<?> baseClazz = clazz.getComponentType();
			while (baseClazz.isArray()) {
				baseClazz = baseClazz.getComponentType();
				dimSize += 1;
			}
			int[] tempDimLength = new int[dimSize];
			//数组向数组转换
			if(data.getClass().isArray()) {
				int arrayLength = Array.getLength(data);
				tempDimLength[0] = arrayLength;
				Object tempArray = Array.newInstance(baseClazz, tempDimLength);
				for (int i = 0; i < arrayLength; i++) {
					Array.set(tempArray, i, transformData(clazz.getComponentType(), Array.get(data, i), isCopy, null, null));
				}
				return (T)tempArray;
			} else if(Collection.class.isAssignableFrom(data.getClass())) {
				//集合向数组转换
				Collection<?> collection = (Collection<?>)data;
				int arrayLength = collection.size();
				tempDimLength[0] = arrayLength;
				Object tempArray = Array.newInstance(baseClazz, tempDimLength);
				int idx = 0;
				for (Object tempV : collection) {
					Array.set(tempArray, idx++, transformData(clazz.getComponentType(), tempV, isCopy, null, null));
				}
				return (T)tempArray;
			}
		}
		//其它集合转换为Collection
		if(Collection.class.isAssignableFrom(clazz)) {
			String clazzName = getTypeName(clazz);
			if(data instanceof String) {
				try {
					data = JSONArray.parseArray((String)data);
				} catch (Exception e) {
					throw new RuntimeException("DataTypeUtil: Cann't new collection to " + clazzName + ". ");
				}
			}
			Collection<Object> tempCollection = null;
			try {
				tempCollection = (Collection<Object>) clazz.newInstance();
			} catch (Exception e) {
				throw new RuntimeException("DataTypeUtil: Cann't new collection " + clazzName +  ".");
			}
			if(data.getClass().isArray()) {
				int arrayLength = Array.getLength(data);
				for (int i = 0; i < arrayLength; i++) {
					tempCollection.add(transformData(valueClass, Array.get(data, i), isCopy, null, null));
				}
			} else if(Collection.class.isAssignableFrom(data.getClass())) {
				//集合向数组转换
				Collection<?> collection = (Collection<?>)data;
				for (Object tempV : collection) {
					tempCollection.add(transformData(valueClass, tempV, isCopy, null, null));
				}
			}
			return (T)tempCollection;
		}
		//其它Map、对象向Map转换
		if(Map.class.isAssignableFrom(clazz)) {
			String clazzName = getTypeName(clazz);
			if(data instanceof String) {
				try {
					data = JSONObject.parse((String)data);
				} catch (Exception e) {
					throw new RuntimeException("DataTypeUtil: Cann't new map to " + clazzName + ".");
				}
			}
			Map<Object, Object> tempMap = null;
			try {
				tempMap = (Map<Object, Object>) clazz.newInstance();
			} catch (Exception e) {
				throw new RuntimeException("DataTypeUtil: Cann't new map " + clazzName +  ".");
			}
			if(Map.class.isAssignableFrom(data.getClass())) {
				Map map = (Map)data;
				Iterator<Entry> iterator = map.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry entry = iterator.next();
					Object key = entry.getKey();
					Object value = entry.getValue();
					tempMap.put(transformData(keyClass, key, isCopy, null, null), transformData(valueClass, value, isCopy, null, null));
				}
			} else {
				Field[] fields = data.getClass().getDeclaredFields();
				for (int i = 0; i < fields.length; i++) {
					Field field = fields[i];
					/*if(field.getAnnotation(FieldIgnore.class) != null) {
						continue;
					}*/
					int mod = field.getModifiers();
					if (Modifier.isAbstract(mod) || Modifier.isFinal(mod)
							|| Modifier.isNative(mod) || Modifier.isStatic(mod))
						continue;
					try {
						field.setAccessible(true);
					} catch (SecurityException e) {
						continue;
					}
					Object tempV = null;
					try {
						 tempV = field.get(data);
					} catch (Exception e) {}
					if(tempV == null) continue;
					tempMap.put(field.getName(), transformData(valueClass, tempV, isCopy, null, null));
				}
			}
			return (T)tempMap;
		}
		//如果要转换的是时间类型，则调用时间类型进行转换
		if(isTimeType(clazz)) {
			if(!(data instanceof String)) {
				data = DateUtil.timeToString(data);
			}
			return (T)DateUtil.stringToTime(data, clazz);
		}
		//其它类型的转换
		if (clazz.equals(String.class)) {
			if(isTimeType(data.getClass())) {
				return (T)DateUtil.timeToString(data);
			} else if(isBaseType(data.getClass())) {
				return (T)data.toString();
			} else {
				// 默认转换为xml文档
				return (T)xs.toXML(data);
			}
		} else if (clazz.equals(Double.class) || clazz == Double.TYPE) {
			return (T)(Double)Double.parseDouble(data.toString());
		} else if (clazz.equals(Float.class) || clazz == Float.TYPE) {
			return (T)(Float)Float.parseFloat(data.toString());
		} else if (clazz.equals(Byte.class) || clazz == Byte.TYPE) {
			String tempStr = data.toString();
			int tempIdx = tempStr.indexOf(".");
			if(tempIdx > -1) {
				tempStr = tempStr.substring(0, tempIdx);
			}
			return (T)(Byte)Byte.parseByte(tempStr);
		} else if (clazz.equals(Short.class) || clazz == Short.TYPE) {
			String tempStr = data.toString();
			int tempIdx = tempStr.indexOf(".");
			if(tempIdx > -1) {
				tempStr = tempStr.substring(0, tempIdx);
			}
			return (T)(Short)Short.parseShort(tempStr);
		} else if (clazz.equals(Integer.class) || clazz == Integer.TYPE) {
			String tempStr = data.toString();
			int tempIdx = tempStr.indexOf(".");
			if(tempIdx > -1) {
				tempStr = tempStr.substring(0, tempIdx);
			}
			return (T)(Integer)Integer.parseInt(tempStr);
		} else if (clazz.equals(Long.class) || clazz == Long.TYPE) {
			String tempStr = data.toString();
			int tempIdx = tempStr.indexOf(".");
			if(tempIdx > -1) {
				tempStr = tempStr.substring(0, tempIdx);
			}
			return (T)(Long)Long.parseLong(tempStr);
		}  else if (clazz.equals(Boolean.class) || clazz == Boolean.TYPE) {
			if(data.toString().toLowerCase().trim().equals("0") || data.toString().toLowerCase().trim().equals("false")) {
				return (T)(Boolean)false;
			} else {
				return (T)(Boolean)true;
			}
		} else if (clazz.equals(BigDecimal.class)) {
			return (T)new BigDecimal(data.toString());
		}  else if (clazz.equals(Character.class) || clazz == Character.TYPE) {
			return (T)(Character)data.toString().charAt(0);
		} else {
			try {
				Object tempObj = clazz.newInstance();
				Field[] fields = clazz.getDeclaredFields();
				//Map往对象转
				if(data instanceof Map) {
					Map map = (Map)data;
					for (int i = 0; i < fields.length; i++) {
						Field field = fields[i];
						int mod = field.getModifiers();
						if (Modifier.isAbstract(mod) || Modifier.isFinal(mod)
								|| Modifier.isNative(mod) || Modifier.isStatic(mod))
							continue;
						try {
							field.setAccessible(true);
						} catch (SecurityException e) {
							continue;
						}
						Object tempV = map.get(field.getName());
						if(tempV != null) {
							field.set(tempObj, transformData(field.getType(), tempV, isCopy, null, null));
						}
					}
				} else {
				//对象往对象转
					Class<?> dataClazz = data.getClass();
					for (int i = 0; i < fields.length; i++) {
						Field field = fields[i];
						int mod = field.getModifiers();
						if (Modifier.isAbstract(mod) || Modifier.isFinal(mod)
								|| Modifier.isNative(mod) || Modifier.isStatic(mod))
							continue;
						try {
							Field dataField = dataClazz.getDeclaredField(field.getName());
							if(dataField == null) continue;
							try {
								dataField.setAccessible(true);
								field.setAccessible(true);
							} catch (SecurityException e) {
								continue;
							}
							field.set(tempObj, transformData(field.getType(), dataField.get(data), isCopy, null, null));
						} catch (Exception e) {}
					}
				}
				return (T)tempObj;
			} catch (Exception e) {
				String clazzName = getTypeName(clazz);
				throw new RuntimeException("DataTypeUtil: Cann't new " + clazzName +  ".");
			}
		}
	}
	/**
	 * 转换注解成JSON
	 * @param apiAnnotation
	 * @return
	 */
	public static JSONObject transAnnotationToJson(Annotation apiAnnotation) {
		JSONObject backObject = new JSONObject();
		Method[] methods = apiAnnotation.annotationType().getDeclaredMethods();
		for (int i = 0; i < methods.length; i++) {
			Class<?> clazz = methods[i].getReturnType();
			try {
				Object tempBack = methods[i].invoke(apiAnnotation);
				if(clazz.isArray() && !DataTypeUtil.isBaseType(clazz.getComponentType())) {
					JSONArray childArray = new JSONArray();
					int tempLength = Array.getLength(tempBack);
					for (int j = 0; j < tempLength; j++) {
						Object arrayValue = Array.get(tempBack, j);
						if(arrayValue instanceof Annotation) {
							childArray.add(transAnnotationToJson((Annotation)arrayValue));
						} else {
							childArray.add(arrayValue);
						}
					}
					backObject.put(methods[i].getName(), childArray);
				} else if(clazz.equals(Class.class)) {
					backObject.put(methods[i].getName(), ((Class<?>)tempBack).getName());
				} else {
					backObject.put(methods[i].getName(), tempBack);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return backObject;
	}

	/**
	 * 返回指定的类型数据
	 * @param param
	 * @param key
	 * @param clazz
	 * @param <T>
	 * @return
	 */
	public static  <T> T getFixedTypeData(JSONObject param, String key, Class<?> clazz) {
		Object data = param.get(key);
		return DataTypeUtil.transformData(clazz, data);
	}


}
