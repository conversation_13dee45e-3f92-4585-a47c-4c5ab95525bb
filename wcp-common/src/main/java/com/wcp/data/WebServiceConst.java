package com.wcp.data;

/**
 * 服务接口相关的常量定义
 * <AUTHOR>
 * @date 2020.05.01
 */
public class WebServiceConst {
	/** 请求 */
	public static final String REQUEST = "request";
	/** 响应 */
	public static final String RESPONSE = "response";
	/** session*/
	public static final String HTTPSESSION = "httpSession";
	/** 消息标记，把所有的调用信息存在些标记上 */
	public static final String RESULT_CODE = "wcpResultCode";
	/** 消息标记，把所有的调用信息存在些标记上 */
	//public static final String MESSAGE = "message";
	/** 出错消息标记 */
	public static final String EXCEPTION = "exception";
	/** 用户重定向*/
	public static final String REDIRECT = "redirect";
	/** 成功标记 */
	public static final String SUCCESS = "success";
	/** 错误代码 */
	public static final String ERRORCODE = "errorCode";
	/** 用户ID */
	public static final String USER_ID = "userId";
	/** 用户账号 */
	public static final String USER_ACCOUNT = "userAccount";
	/** 用户账号 */
	public static final String USER_COMPANY = "userCompany";
	/** 用户名称 */
	public static final String USER_NAME = "userName";
	/** 用户类型 */
	public static final String USER_TYPE = "userType";
	/** 退出登录 */
	public static final String LOGIN_OUT = "loginOut";
	/** 调用服务时的反馈信息 */
	public static final String SERVICE_FEEDBACK = "serviceFeedback";
	/** 登录是必须的：1：必须登录；0：不用登录 */
	public static final String LOGIN_MUST = "loginMust";
	/** 登录路由 */
	public static final String LOGIN_URL = "loginUrl";
	/** 结果不需要添加标志 */
	public static final String RESULT_NOMARK = "resultNomark";

	/** 行业标志 */
	public static final String INDUSTRY_SIGN = "industrySign";
	/** 公司标志 */
	public static final String COMPANY_SIGN = "companySign";
	/** 项目标志 */
	public static final String PROJECT_SIGN = "projectSign";
	
	/** 平台区分项目的点号，但是在平台中有时候与PROJD不一样 */
	public static final String WCPID = "WCPID";
	/** 行业ID */
	public static final String INDUSTRY_JD = "industryId";
	/** 公司ID */
	public static final String COMPANY_JD = "companyId";
	/** 项目ID */
	public static final String PROJECT_JD = "projectId";
	/** 项目名称 */
	public static final String PROJNAME = "projName";
	/** 功能ID */
	public static final String FUNC_ID = "funcId";
	/** 返回列*/
	public static final String COLUMN = "column";
	/** 返回数据*/
	public static final String DATA = "data";
	/** IP地址*/
	public static final String IP = "ip";
	/** mac地址*/
	public static final String MAC = "mac";
	/** 服务地址*/
	public static final String SERVICEPATH = "servicePath";
	/** 当前页*/
	public static final String PAGENOW = "pageNow";
	/** 总页数*/
	public static final String PAGESIZE = "pageSize";
	/** 获取页数*/
	public static final String PAGECOUNT = "pageCount";
	/** 项目密码编译算法KEY */
	public static final String PASSWORDKEY = "fucheng666";
	/** 项目用户账户 */
	public static final String PROJECTUSER = "projectUser";
	/** 项目用户名账户 */
	public static final String PROJECTUSERACCOUNT = "projectUserAccount";
	/** 项目用户名名称 */
	public static final String PROJECTUSERNAME = "projectUserName";
	/** 项目用户名图标 */
	public static final String PROJECTUSERICON = "projectUserIcon";
	/** 项目用户类型 */
	public static final String PROJECTUSERTYPE = "projectUserType";
	/** 项目登录SESSION标志 */
	public static final String PROJECTSESSION = "projectSession";
	/** 权限调用服务名称 */
	public static final String SERVICE_NAME = "serviceName";
	/** 资源点号 */
	public static final String RESOURCE_ID = "resourceId";
	/** 权限要求集合 */
	public static final String AUTHORITY_ITEM = "authorityItem";
	/** 资源类型 */
	public static final String RESOURCE_TYPE = "resourceType";
	
	public static final String SERVLET_PATH = "servletPath";
	/** 设备号 */
	public static final String DEVICE_ID = "deviceId";
	/** 设备类型 */
	public static final String DEVICE_TYPE = "deviceType";
	/** 权限验证票据 */
	public static final String TICKET = "ticket";
	/** TOKEN的标志 */
	public static final String TOKEN_KEY = "tokenKey";
	/** TOKEN的私密信息,需要加密的信息 */
	public static final String TOKEN_PRIVATE_INFO = "tokenPrivateInfo";
	/** 服务全局信息 */
	public static final String SERVICE_GLOBE_INFO = "serviceGlobeInfo";
	/** 服务流数据生成器 */
	public static final String SERVICE_BODY_BUILDER = "serviceBodyBuilder";
	/** 是否登录 */
	public static final String LOGIN = "login";
	/** 权限验证通过 */
	public static final String SSO_PASSED = "ssoPassed";

	public static final String SSO_SERVICE = "ssoService";
	
	public static final String COOKIE_NAME = "cookieName";

	public static final String EXCLUSIONS = "exclusions";
	
	/******** 以下是钉钉需要获取的标志信息 ********/
	/** 钉钉前段登录获取得到的CODE */
	public static final String DINGDING_AUTH_CODE = "dingdingAuthCode";
	/** 钉钉登录获取的钉钉内部接口token */
	public static final String DINGDING_TOKEN = "dingdingToken";
	/** 钉钉登录获取的钉钉内部接口token的时间，两小时内要获取一次，以Long的形式存储 */
	public static final String DINGDING_TOKEN_TIME = "dingdingTokenTime";
	/** 登录时间 */
	public static final String LOGIN_TIME = "loginTime";
	/** 钉钉内部的组织点号 */
	public static final String DINGDING_CORP_ID = "dingdingCorpId";
	/** 应用凭证：应用代理点号 */
	public static final String DINGDING_AGENT_ID = "dingdingAgentId";
	/** 应用凭证：应用秘钥 */
	public static final String DINGDING_APP_SECRET = "dingdingAppSecret";
	/** 应用凭证：应用秘钥加盐 */
	public static final String DINGDING_APP_KEY = "dingdingAppKey";
	/** 应用凭证：数据秘钥 */
	public static final String DINGDING_AES_SECRET = "dingdingAesSecret";
	/** 应用凭证：数据秘钥加盐 */
	public static final String DINGDING_AES_KEY = "dingdingAesKey";
	/** 钉钉监听事件，列表，可以监听多个事件类型 */
	public static final String DINGDING_CALL_BACK_TAG = "dingdingCallBackTag";
	/** 公网地址 */
	public static final String PUBLIC_URL = "publicUrl";
	/** 钉钉公网地址 */
	public static final String DINGDING_URL = "https://oapi.dingtalk.com";

	/**token鉴权*/
	public static final  String AUTHENTICATION="Authentication";
	/** 权限验证通过 */
	
	/** 权限集合定义 */
	public static class Authority {
		// 全局权限
		public static final String AUTHORITY_WHOLE = "AUTHORITY_WHOLE";
		// 调用或读取权限
		public static final String AUTHORITY_READ = "AUTHORITY_READ";
		// 更新或修改权限
		public static final String AUTHORITY_UPDATE = "AUTHORITY_UPDATE";
		// 删除权限
		public static final String AUTHORITY_DELETE = "AUTHORITY_DELETE";
		// 函数调用
		public static final String AUTHORITY_CALL = "AUTHORITY_CALL";
	}
}
