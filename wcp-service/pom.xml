<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wcp</artifactId>
        <groupId>com.wcp</groupId>
        <version>3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>wcp-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-common</artifactId>
            <version>3.0</version>
        </dependency>
        <!-- database调用-->
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-database</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-sso</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-core</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>com.wcp</groupId>
            <artifactId>wcp-workflow</artifactId>
            <version>3.0</version>
        </dependency>
        <!-- Spring Boot Starter Security -->
      <!--  <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        &lt;!&ndash; Spring Boot Starter OAuth2 Client &ndash;&gt;
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>-->
    </dependencies>

<!--    <build>
        &lt;!&ndash;打包后的jar名称&ndash;&gt;
        <finalName>wcp-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <outputDirectory>${project.build.directory}/wcp-service</outputDirectory>
                </configuration>
            </plugin>
            &lt;!&ndash;添加配置跳过测试&ndash;&gt;
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            &lt;!&ndash; 复制资源文件 &ndash;&gt;
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/wcp-service</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        &lt;!&ndash;排除本地配置文件&ndash;&gt;
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**/*.yml</exclude>
                </excludes>
            </resource>
        </resources>

    </build>-->
    <build>
        <!--打包后的jar名称-->
        <finalName>wcp-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <!--MANIFEST.MF 中 Class-Path 加入前缀！用命令java -jar运行jar时就不用-Dloader.path指定外部资源路径了-->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!--jar包名字是否包含唯一版本标识-->
                            <useUniqueVersions>false</useUniqueVersions>
                            <!--指定含main方法的主类入口-->
                            <mainClass>com.wcp.WcpApplication</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!--MANIFEST.MF 中 Class-Path 加入资源文件目录！用命令java -jar时就不用-Dloader.path指定外部资源路径了 -->
                            <Class-Path>resources/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <!-- 打包时从jar包里排除资源文件 -->
                    <excludes>
                        <exclude>*.yml</exclude>
                    </excludes>
                    <!-- 指定项目打成jar包输出位置 -->
                    <outputDirectory>${project.build.directory}/wcp-service</outputDirectory>
                </configuration>
            </plugin>
            <!-- 拷贝依赖jar包！插件maven-jar-plugin只是打包排除文件，
            而把依赖jar包拷贝到外部lib目录就需要maven-dependency-plugin插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/wcp-service/lib/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--拷贝资源文件！ 插件maven-jar-plugin只负责打包时排除文件，
                而把资源文件拷贝到外部resource目录就需要maven-dependency-plugin插件-->
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>

                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>true</filtering>

                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}/wcp-service/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>