import com.wcp.WcpApplication;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.utils.AESUtil;
import com.wcp.utils.SecurityUtil;
import com.wcp.utils.StringUtil;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

/**
 * Description: TODO
 * Author: qianchao
 * Date: 2024/3/8 15:31
 */
@SpringBootTest(classes = WcpApplication.class)
public class DataBaseTest {
    @Test
    public void InserSPDB() {
       /* String username="SYSDBA";
        String password="Admin@123";
        String projectId="9ba2450dba0d47368947da448fc3cf74";
        String dbType="dm";
        String service="JEECG-CLOUD";
        String dbName="JEECG-CLOUD";
        String alias="达梦数据库";//数据库别名
        String driver="dm.jdbc.driver.DmDriver";

        String url="jdbc:dm://**************:8236?schema=HFS";
        String insertSPDB="INSERT INTO WCP_SP_DB (PROJECT,SERVICE,DB_TYPE,DB_NAME,ALIAS,DRIVER,URL,USER_NAME,USER_PWD) VALUES (?,?,?,?,?,?,?,?,?)";
        String userPwd = AESUtil.encrypt(password);
        Map<String,Object> map=new HashMap<>();
        map.put("PROJECT", projectId);
        map.put("SERVICE",service);
        map.put("DB_TYPE",dbType);
        map.put("DB_NAME",dbName);
        map.put("ALIAS",alias);
        map.put("DRIVER",driver);
        map.put("URL",url);
        map.put("USER_NAME",username);
        map.put("USER_PWD",userPwd);
        WcpDataBase.execute("wcp",insertSPDB,new Object[]{projectId,service,dbType,dbName,alias,driver,url,username,userPwd});
        //System.out.println(WcpDataBase.insertMap("wcp",insertSPDB,map,null)?"插入成功":"插入失败");
        System.out.println(AESUtil.encrypt("dgbdfdb"));*/
        System.out.println(AESUtil.encrypt("Aa@123456"));
    }

    @Test
    public void test2(){
        List<Map<String, Object>> updateTimeList = new ArrayList<>();
        Map<String, Object> updateTimeMap = new HashMap<>();
        updateTimeMap.put("NAME", "HYDRO_BS_DATA_DEF");
        updateTimeMap.put("TIME", Calendar.getInstance());
        updateTimeMap.put("ACCOUNT", "");
        updateTimeMap.put("DESCR", "平台自动读取");
        updateTimeList.add(updateTimeMap);
        String insStr = "INSERT INTO WCP_BS_UPDATE (NAME, TIME, ACCOUNT, DESCR) VALUES (?,?,?,?)";
        WcpDataBase.insertOrUpdate("wcp", insStr, updateTimeList, null);
    }


}
