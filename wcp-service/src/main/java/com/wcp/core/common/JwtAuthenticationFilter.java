package com.wcp.core.common;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wcp.annotation.ApiService;
import com.wcp.http.HttpStatus;
import com.wcp.redis.CacheMemory;
import com.wcp.service.AcommonService;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.StringUtil;
import com.wcp.utils.TokenUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Description: 用户权限过滤器
 * Author: qianchao
 * Date: 2024/2/18 16:34
 */
@Data
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    @Value("${spring.sso.openPermissions}")
    private boolean openPermissions;
    @Value("${spring.sso.iam}")
    private String iam;
    @Value("${spring.sso.oauth2.client-id:}")
    private String clientId;
    @Value("${spring.sso.oauth2.client-secret:}")
    private String clientSecret;

    @Value("${spring.sso.oauth2.check-token-uri:}")
    private String checkTokenUri;
    @Autowired
    private TokenUtil tokenUtil;
    private List<Pattern> exclusionList = new ArrayList<Pattern>();
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String serviceKey = request.getServletPath();
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accep,Content-Type,XFILENAME,XFILECATEGORY,XFILESIZE");
        response.setHeader("X-Content-Type-Options", "nosniff");
        response.setHeader("X-XSS-Protection", "1; mode=block");
        response.setHeader("Content-Security-Policy", "default-src 'self'; script-src 'self'; frame-ancestors 'self'; object-src 'none'");
        response.setHeader("Referrer-Policy","no-referrer");
        response.setHeader("x-frame-options","DENY");
        response.setHeader("X-Permitted-Cross-Domain-Policies","master-only");
        response.setHeader("X-Download-Options","noopen");
        //需要过滤排除的服务不进行验证,暂时不使用
        /*if (isExcluded(serviceKey)) {
            //放行
            filterChain.doFilter(request, response);
            return;
        }*/
        if(serviceKey.startsWith("/stream")) {
            serviceKey=serviceKey.substring(7);
        }
        Method method = AcommonService.getServiceMethod(serviceKey.substring(1));
        if(method == null) {
            writeJsonResponse(response,HttpStatus.BAD_REQUEST,"未找到服务接口,请输入正确的接口路由.");
            return;
        }
        ApiService apiService = method.getAnnotation(ApiService.class);
        if(apiService != null) {
            // 私有接口、不限制、平台、某行业、某公司、某项目四个属性，分别用-1、0、1、2、3、4表示
            //原平台signType=1的接口已拆分到wcp-platform模块中了
            if(apiService.signType() == 0) { // 不限制直接放行
                //放行
                filterChain.doFilter(request, response);
                return;
            }
            else if(apiService.signType() == 2) {//项目服务，signType=2 使用权限接口拦截
                String token=request.getHeader("Authorization");
                //需要验证用户权限
                if(openPermissions ){
                    //先判断是否是自身的token
                    if(tokenUtil.isValidToken(token) && StringUtil.isNotEmpty(token)){
                        filterChain.doFilter(request, response);
                    }
                    //todo auth单点
                    if("auth".equals(iam)){
                        Map<String,Object> tokenMap=checkToken(token);
                        if(tokenMap!=null && Integer.parseInt(tokenMap.get("status").toString())==HttpStatus.SUCCESS){
                            filterChain.doFilter(request, response);
                        }
                    }
                    writeJsonResponse(response,HttpStatus.UNAUTHORIZED,"未授权");
                    //todo cas 单点...
                    /*if(tokenUtil.isValidToken(token) && StringUtil.isNotEmpty(token)){
                        //todo 如果开启了权限校验 这里验证token
                        String refreshToken=tokenUtil.refreshToken(token);
                        if(StringUtil.isNotEmpty(refreshToken)){
                            response.setHeader("Refresh-Token",refreshToken);
                        }
                        //接口拦截
                        Map<String, Object> tokenValues=tokenUtil.getTokenValues(token);
                        String account = (String) tokenValues.get("ACCOUNT");
                        String projectId = request.getHeader("projectId");
                        filterChain.doFilter(request, response);
                       *//* if("admin".equals(account)){
                            filterChain.doFilter(request, response);
                            return;
                        }
                        //不拦截的接口
                        List<String> whiteList = Lists.newArrayList("auth/user/updatePassword","auth/user/queryMe","auth/user/getRouters","hydroMultiple/queryOverallCombData");
                        List<String>  cacheList=CacheMemory.getValue(projectId,"wcp",List.class,account+"-"+projectId);
                        if(cacheList!=null && cacheList.size()>0){
                            whiteList.addAll(cacheList);
                        }
                        if(whiteList.contains(serviceKey.substring(1))){
                            filterChain.doFilter(request, response);
                        }else{
                            response.setContentType("text/html;charset=utf-8");
                            JSONObject object=new JSONObject();
                            object.put(HttpStatus.CODE, HttpStatus.FORBIDDEN);
                            object.put(HttpStatus.MSG, "请求访问受限");
                            PrintWriter out = response.getWriter();
                            out.print(object);
                        }*//*
                    } else{
                        *//*response.setContentType("text/html;charset=utf-8");
                        JSONObject object=new JSONObject();
                        object.put("code", HttpStatus.UNAUTHORIZED);
                        PrintWriter out = response.getWriter();
                        out.print(object);*//*
                        writeJsonResponse(response,HttpStatus.UNAUTHORIZED,"未授权");
                    }*/

                }else{
                    //项目没有开启权限配置，yml文件可以开启或关闭权限校验
                    filterChain.doFilter(request, response);
                }
            }
        }
    }

    private void writeJsonResponse(HttpServletResponse response,int code,String msg) throws IOException {
        if (response.isCommitted()) return;
        response.resetBuffer();
        response.setContentType("application/json;charset=utf-8");
        response.setStatus(code);
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        PrintWriter out = response.getWriter();
        out.print(object.toJSONString());
        out.flush();
    }

    /**
     * JWT方式从这个方法中获取token，可以是用url中也可以是从hander中
     * @param request
     * @return
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        return request.getHeader("token");
    }

    /**
     * 在这个方法中解析token
     * @param token
     */
    private void authenticateUser(String token) {
        // 验证令牌的有效性，解析令牌中的用户信息
        // 创建认证对象，并将其设置到SecurityContext中
        // 例如，可以使用Spring Security的AuthenticationManager和Authentication对象
    }
    private boolean isExcluded(String uri) {
        for (Pattern exclusion : exclusionList) {
            Matcher matcher = exclusion.matcher(uri);
            if (matcher.matches()) {
                return true;
            }
        }
        return false;
    }
    public static Map<String, Object> checkToken(String accessToken) {
        // 1. 拼接 Basic Auth
        String auth = Base64.getEncoder().encodeToString(("cidea5689aa5dfc41a4b55dd336caa555c6:sideea09ba3bc874aeb8eecbfd2d4288cb9").getBytes(StandardCharsets.UTF_8));

        // 2. 构造 form 参数
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("token", accessToken);

        // 3. 构造请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization", "Basic " + auth);

        // 4. 发起 POST 请求
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
        RestTemplate restTemplate = new RestTemplate();
        try {
            ResponseEntity<Map> response = restTemplate.postForEntity("http://171.43.138.202:9099/tias/oauth/check_token", request, Map.class);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> resul=response.getBody();
                resul.put("status",response.getStatusCodeValue());
                return resul;
            }
            else {
                throw new RuntimeException("token 校验失败，响应状态：" + response.getStatusCode());
            }
        }catch (HttpClientErrorException | HttpServerErrorException e) {
            // token 无效时返回的是 400 错误，body 是 JSON
            String errorBody = e.getResponseBodyAsString();
            JSONObject errJson=JSONObject.parseObject(errorBody);
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("status", e.getStatusCode().value());
            errorMap.put("error", errJson.getString("error"));
            return errorMap;

        } catch (Exception e) {
            throw new RuntimeException("请求失败", e);
        }

    }

    public static void main(String[] args) {
        System.out.println(checkToken("d0d4c28c-8bda-455f-813a-4802543cea23"));
    }
}
