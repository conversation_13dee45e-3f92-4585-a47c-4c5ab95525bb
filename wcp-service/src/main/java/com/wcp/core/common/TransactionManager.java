package com.wcp.core.common;

import com.wcp.db.jdbc.WcpConnection;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;

/**
 * @Date 2019/9/9 2:27
 * by mocar
 */
@Component
@Aspect
public class TransactionManager {

    @Pointcut("@annotation(com.wcp.annotation.ApiTransactional)")
    private void transactionPointcut(){
        System.out.println("<<<<<<<<<<<<<<");
    }
 
    @Before("transactionPointcut()")
    public void beginTransaction(){
        WcpConnection.openTransaction();//开启事务
    }
    @AfterReturning("transactionPointcut()")
    public void commit(){
        try {
            WcpConnection.transactionCommit();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    @AfterThrowing("transactionPointcut()")
    public void rollback(){
        WcpConnection.transactionRollback();
    }

    @After("transactionPointcut()")
    public void release(){
        WcpConnection.closeTransaction();
    }
}