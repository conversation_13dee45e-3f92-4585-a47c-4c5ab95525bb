package com.wcp.core.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wcp.data.DataTypeUtil;
import com.wcp.data.WebServiceConst;
import com.wcp.utils.DateUtil;
import org.apache.log4j.Logger;

import javax.servlet.ServletRequest;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * Json工具类
 *
 * <AUTHOR>
 * @date 2019.8.28
 */
public class JsonUtil {

    private static Logger logger = Logger.getLogger(JsonUtil.class);

    /**
     * 校验josn格式
     * @param jsonObject 入参
     * @return  返回
     */
    public static Boolean verifyJsonFormat(String jsonObject){
        try {
            JSONObject.parseObject(jsonObject);
            return false;
        } catch (Exception e) {
            logger.error("josn格式错误  "+ e.getMessage());
            e.printStackTrace();
            return true;
        }
    }


    /**
     * JSONArray转String
     *
     * @param param
     * @return
     */
    public static String jsonArrayToString(JSONArray param) {
        if (param != null && param.size() > 0) {
            return param.toString();
        }
        return null;
    }

    /**
     * JSONArray转List
     *
     * @param param
     * @return
     */
    public static List<Object> jsonArrayToList(JSONArray param) {
        ArrayList<Object> arrayList = new ArrayList<>();
        if (param != null && param.size() > 0) {
            for (int i = 0; i < param.size(); i++) {
                arrayList.add(param.get(i));
            }
        }
        return arrayList;
    }

    /**
     * 对象列表转换成json，并格式化时间
     *
     * @param <T>
     * @param list
     * @return
     */
    public static <T> JSONArray listToJsonFormatTime(List<T> list) {
        JSONArray jsonArray = new JSONArray();
        if (list == null || list.size() == 0) return jsonArray;
        Class<?> tempClazz = list.get(0).getClass();
        for (int i = 0; i < list.size(); i++) {
            Object tempObject = list.get(i);
            JSONObject object = new JSONObject();
            Field[] fields = tempClazz.getDeclaredFields();
            for (int j = 0; j < fields.length; j++) {
                int mod = fields[j].getModifiers();
                if (Modifier.isAbstract(mod) || Modifier.isFinal(mod) || Modifier.isNative(mod) || Modifier.isStatic(mod))
                    continue;
                try {
                    fields[j].setAccessible(true);
                    Object tempValue = fields[j].get(tempObject);
                    if (DataTypeUtil.isTimeType(fields[j].getDeclaringClass())) {
                        tempValue = DateUtil.timeToString(tempValue);
                    }
                    object.put(fields[j].getName(), tempValue);
                } catch (Exception e) {
                }
            }
            jsonArray.add(object);
        }
        return jsonArray;
    }

    /**
     * 对象列表转换成MapList，并格式化时间
     *
     * @param <T>
     * @param list
     * @return
     */
    public static <T> List<Map<String, Object>> listObjectToMapListFormatTime(List<T> list) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
        if (list == null || list.size() == 0) return mapList;
        Class<?> tempClazz = list.get(0).getClass();
        for (int i = 0; i < list.size(); i++) {
            Object tempObject = list.get(i);
            Map<String, Object> object = new HashMap<String, Object>();
            Field[] fields = tempClazz.getDeclaredFields();
            for (int j = 0; j < fields.length; j++) {
                int mod = fields[j].getModifiers();
                if (Modifier.isAbstract(mod) || Modifier.isFinal(mod) || Modifier.isNative(mod) || Modifier.isStatic(mod))
                    continue;
                try {
                    fields[j].setAccessible(true);
                    Object tempValue = fields[j].get(tempObject);
                    if (DataTypeUtil.isTimeType(fields[j].getType())) {
                        tempValue = DateUtil.timeToString(tempValue);
                    }
                    object.put(fields[j].getName(), tempValue);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            mapList.add(object);
        }
        return mapList;
    }

    /**
     * 检验指定的key集合是否全部在对象中存在
     *
     * @param jsonObject     对象
     * @param permitEmptyStr 是否允许空的字符串
     * @param keys           检验的数据集合
     * @return
     */
    public static boolean checkExistData(JSONObject jsonObject, boolean permitEmptyStr, String... keys) {
        if (keys == null || keys.length == 0) {
            return true;
        }
        if (jsonObject == null || jsonObject.size() == 0) {
            return false;
        }
        for (int i = 0; i < keys.length; i++) {
            Object data = jsonObject.get(keys[i]);
            if (data == null) {
                return false;
            }
            if (!permitEmptyStr && data.toString().trim().equals("")) {
                return false;
            }
        }
        return true;
    }

    /**
     * 批量添加key和value对应的健值
     *
     * @param keyValues
     * @return
     */
    public static JSONObject createJsonObject(String... keyValues) {
        JSONObject tempObject = new JSONObject();
        if (keyValues.length > 0 && keyValues.length % 2 == 0) {
            for (int i = 0; i < keyValues.length; i += 2) {
                tempObject.put(keyValues[i], keyValues[i + 1]);
            }
        }
        return tempObject;
    }

    /**
     * 设置服务反馈数据
     * @param result
     * @param key
     * @param value
     */
    public static void setServiceFeedBackValue(Map<String, Object> result, String key, Object value) {
        JSONObject tempObject = (JSONObject)result.get(WebServiceConst.SERVICE_FEEDBACK);
        if(tempObject == null) {
            tempObject = new JSONObject();
            result.put(WebServiceConst.SERVICE_FEEDBACK, tempObject);
        }
        tempObject.put(key, value);
    }

    /**
     * 数组转换成JSON列表
     * @param object
     * @return
     */
    public static JSONArray arrayToJSONArray(Object object) {
        JSONArray jsonArray = new JSONArray();
        if(object.getClass().isArray()) {
            int length = Array.getLength(object);
            for (int i = 0; i < length; i++) {
                jsonArray.add(Array.get(object, i));
            }
        }
        return jsonArray;
    }

    /**
     * 解析在url中，queryString中的数据
     * @param request
     * @return
     */
    public static JSONObject analysisQueryString(ServletRequest request) {
        JSONObject queryObject = new JSONObject();
        if(request == null) return queryObject;
        Map<String, String[]> paramMap = request.getParameterMap();
        Iterator<Map.Entry<String, String[]>> iterator = paramMap.entrySet().iterator();
        while(iterator.hasNext()) {
            Map.Entry<String, String[]> entry = iterator.next();
            String key = entry.getKey();
            String[] value = entry.getValue();
            if(value == null || value.length == 0) {
                queryObject.put(key, null);
            } else if(value.length == 1) {
                queryObject.put(key, value[0]);
            } else if(value.length > 1) {
                queryObject.put(key, JsonUtil.arrayToJSONArray(value));
            }
        }
        return queryObject;
    }

    public static void mergeJSONFilterRepeat(JSONObject jsonObject, JSONObject filterObject) {
        if(jsonObject == null || filterObject == null) return;
        Iterator<Map.Entry<String, Object>> iterator = filterObject.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            if(!jsonObject.containsKey(entry.getKey())) {
                jsonObject.put(entry.getKey(), entry.getValue());
            }
        }
    }
}
