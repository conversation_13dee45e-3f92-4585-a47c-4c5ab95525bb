package com.wcp.core.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * Redis消息配置类。
 * 用于配置Redis消息监听器容器和消息处理器。
 * 根据Redis的配置条件（如主机地址是否存在），动态创建和配置相关的Bean。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
@Configuration
public class RedisMessageConfig {

    @Value("${spring.redis.host:#{null}}")
    private String host;

    @Autowired(required = false)
    @Qualifier("customRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 创建Redis消息监听器容器。
     * 容器负责管理不同的消息监听器，并将消息路由到相应的处理器。
     *
     * @param listenerAdapterOne 用于处理data_base频道消息的监听器适配器。
     * @param listenerAdapterTwo 用于处理channel1频道消息的监听器适配器。
     * @return 配置好的RedisMessageListenerContainer实例。
     */
    @Bean
    @Conditional(RedisCondition.class)
    RedisMessageListenerContainer container(MessageListenerAdapter listenerAdapterOne,
                                            MessageListenerAdapter listenerAdapterTwo) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisTemplate.getConnectionFactory());
        container.addMessageListener(listenerAdapterOne, new PatternTopic("data_base"));
        container.addMessageListener(listenerAdapterTwo, new PatternTopic("channel1"));
        return container;
    }

    /**
     * 创建消息监听器适配器。
     * 将RedisDataBaseMessageSubscriber的onMessage方法绑定到data_base频道。
     *
     * @param subscriberOne Redis数据库消息订阅者。
     * @return 配置好的MessageListenerAdapter实例。
     */
    @Bean
    @Conditional(RedisCondition.class)
    MessageListenerAdapter listenerAdapterOne(RedisDataBaseMessageSubscriber subscriberOne) {
        return new MessageListenerAdapter(subscriberOne, "onMessage");
    }

    /**
     * 创建消息监听器适配器。
     * 将RedisMessageSubscriber的onMessage方法绑定到channel1频道。
     *
     * @param subscriberTwo Redis消息订阅者。
     * @return 配置好的MessageListenerAdapter实例。
     */
    @Bean
    @Conditional(RedisCondition.class)
    MessageListenerAdapter listenerAdapterTwo(RedisMessageSubscriber subscriberTwo) {
        return new MessageListenerAdapter(subscriberTwo, "onMessage");
    }

    /**
     * 创建Redis数据库消息订阅者的Bean。
     * @return 新的RedisDataBaseMessageSubscriber实例。
     */
    @Bean
    @Conditional(RedisCondition.class)
    public RedisDataBaseMessageSubscriber subscriberOne() {
        return new RedisDataBaseMessageSubscriber();
    }

    /**
     * 创建Redis消息订阅者的Bean。
     * @return 新的RedisMessageSubscriber实例。
     */
    @Bean
    @Conditional(RedisCondition.class)
    public RedisMessageSubscriber subscriberTwo() {
        return new RedisMessageSubscriber();
    }
}
