package com.wcp.core.redis;

import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;

import java.nio.charset.StandardCharsets;

/**
 * Redis消息订阅者。
 * 实现了MessageListener接口，用于接收和处理通过Redis发布的消息。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
public class RedisMessageSubscriber implements MessageListener {

    /**
     * 当接收到Redis消息时调用此方法。
     * 方法将接收到的消息从字节转换为字符串，然后进行处理。
     * 此示例中仅打印消息到控制台，但可根据需求进行扩展，如消息解析和业务逻辑处理。
     *
     * @param message 接收到的Redis消息。
     * @param pattern 订阅的模式（此示例中未使用）。
     */
    public void onMessage(final Message message, final byte[] pattern) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        // 处理消息，例如打印或进一步处理
        System.out.println("RedisMessageSubscriber onMessage message: " + messageBody);
    }
}
