package com.wcp.core.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类。
 * 该类负责配置并提供自定义的RedisTemplate。
 * 当Redis主机配置存在时，该配置类才会激活。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
@Configuration
public class RedisConfig {

    @Value("${spring.redis.host:#{null}}")
    private String host;

    /**
     * 创建并配置一个自定义的RedisTemplate。
     * 如果Redis主机配置不存在，此方法将返回null。
     * 该方法配置了Redis键值的序列化方式。
     *
     * @param factory Redis连接工厂，用于创建Redis连接。
     * @return 返回配置好的RedisTemplate实例，或在Redis主机未配置时返回null。
     */
    @Bean
    @Conditional(RedisCondition.class)
    public RedisTemplate<String, String> customRedisTemplate(RedisConnectionFactory factory) {
        if (host == null) {
            return null;
        }
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        return template;
    }
}
