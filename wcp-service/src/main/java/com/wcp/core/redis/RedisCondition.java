package com.wcp.core.redis;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * Redis 条件类，用于在 Spring 配置中基于特定条件（如环境属性）动态激活或禁用某个 Bean 的创建。
 * 这个条件类检查是否存在有效的 Redis 主机配置。
 * 如果 'spring.redis.host' 属性存在且非空，则满足条件，Bean 将被创建。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
public class RedisCondition implements Condition {

    /**
     * 检查Redis主机配置是否存在并有效。
     *
     * @param context Spring上下文，提供环境信息。
     * @param metadata 注解类型的元数据。
     * @return 如果Redis主机配置存在且有效，返回true；否则返回false。
     */
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String redisHost = context.getEnvironment().getProperty("spring.redis.host");
        return redisHost != null && !redisHost.isEmpty();
    }
}
