package com.wcp.core.redis;

import com.alibaba.fastjson.JSON;
import com.wcp.db.jdbc.WcpConnection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;

import java.nio.charset.StandardCharsets;


/**
 * Redis数据库消息订阅者类。
 * 用于处理从Redis频道接收的消息，并根据消息内容更新数据库连接信息。
 * 这是一个Redis消息监听器的实现，专注于数据库连接更新的场景。
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
@Slf4j
public class RedisDataBaseMessageSubscriber implements MessageListener {

    /**
     * 当收到Redis消息时调用此方法。
     * 方法从Redis消息中解析数据库刷新信息，并根据这些信息更新数据库连接。
     * 如果在处理消息的过程中发生任何异常，异常将被捕获并打印堆栈跟踪。
     *
     * @param message 接收到的Redis消息。 其中有
     *                originalProject 修改前数据源 项目ID
     *                originalService 修改前数据源 服务
     *                project 修改后数据源 项目ID
     *                service 修改后数据源 服务
     * @param pattern 订阅的模式（不在此方法中使用）。
     */
    public void onMessage(final Message message, final byte[] pattern) {
        try {
            String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
            RedisDataBaseRefreshInfo refreshInfo = JSON.parseObject(messageBody, RedisDataBaseRefreshInfo.class);
            String originalProject = refreshInfo.getOriginalProject();
            String originalService = refreshInfo.getOriginalService();
            String project = refreshInfo.getProject();
            String service = refreshInfo.getService();
            //从连接池中删除数据源
            WcpConnection.removeConnectionManager(originalProject, originalService);
            log.info("project: " + originalProject + "   service: " + originalService + "  数据源更新");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
