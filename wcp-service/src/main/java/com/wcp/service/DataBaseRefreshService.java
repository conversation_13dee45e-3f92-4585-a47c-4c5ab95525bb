package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpConnection;
import com.wcp.http.HttpStatus;
import com.wcp.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
/**
 * 数据源Service。
 * 对数据源进行操作
 *
 * <AUTHOR>
 * @date 2024-01-19
 * @email <EMAIL>
 */
@ApiGroup(value = "数据源Service", serviceKey = "dataBase", classify = "dataBase", classifyName = "数据源服务")
@Slf4j
public class DataBaseRefreshService{

    @ApiService(value = "刷新数据源", serviceKey = "refresh", signType = 0)
    public void refresh(Map<String, Object> result, JSONObject object) {
        String originalProject = object.getString("originalProject");
        String originalService = object.getString("originalService");
        String project = object.getString("project");
        String service = object.getString("service");


        if (StringUtil.isEmptyAll(originalProject, originalService)) {
            result.put(HttpStatus.MSG, "请输入数据源");
            result.put("success", false);
            return;
        }

        if (StringUtil.isEmptyAll(project, service)) {
            WcpConnection.removeConnectionManager(originalProject, originalService);
            log.info("project: " + originalProject + "   service: " + originalService + "  数据源更新");
        } else {

        }
        result.put(HttpStatus.MSG, "刷新数据源成功成功");
        result.put("success", true);
    }
}
