package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiService;
import org.dromara.warm.flow.core.dto.FlowParams;
import org.dromara.warm.flow.core.entity.Instance;
import org.dromara.warm.flow.core.enums.SkipType;
import org.dromara.warm.flow.core.listener.Listener;
import org.dromara.warm.flow.core.listener.ListenerVariable;
import org.dromara.warm.flow.core.service.InsService;
import org.dromara.warm.flow.core.service.TaskService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 1. @Description TODO 工作流测试
 * 2. <AUTHOR>
 * 3. @Date 2025/5/21 9:32
 */
@ApiGroup(value = "工作流测试", serviceKey = "flowservice", classify = "flowservice", classifyName = "工作流测试")

public class FlowService implements Listener {
    @Resource
    private InsService insService;

    @Resource
    private TaskService taskService;
    @ApiService(value = "提交请假流程", serviceKey = "submitFlow", signType = 0, notes = "提交请假流程")
    public  void startFlow(Map<String, Object> result, JSONObject object) {
        // 流程变量
        Map<String, Object> variable = new HashMap<>();
        // 办理人表达式替换  【按需传】
        variable.put("handle.market", Arrays.asList("jiangshangzhou","zhaoguimin","duanfengping"));

        //test1员工提交请假流程申请 handler穿
        FlowParams flowParams=FlowParams.build().flowCode("SJSB")
                .handler("test1").variable(variable).tenantId("9ba2450dba0d47368947da448fc3cf74");
        Instance instance = insService.start("202505236666",flowParams);
    }




    /**
     * 执行完成
     * @param listenerVariable
     */
    @Override
    public void notify(ListenerVariable listenerVariable) {
        System.out.println("当前节点名称:"+listenerVariable.getNode().getNodeName());
        System.out.println("当前节点名称:"+listenerVariable.getNode().getNodeType());
        System.out.println("流程状态:"+listenerVariable.getInstance().getFlowStatus());
        System.out.println("处理器:"+listenerVariable.getNode().getListenerType()+"处理类型:"+listenerVariable.getNode().getListenerPath());
    }
}
