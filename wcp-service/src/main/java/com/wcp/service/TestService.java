package com.wcp.service;
import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.db.jdbc.WcpDataBase;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.AESUtil;
import com.wcp.utils.StringUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@ApiGroup(value = "测试service", serviceKey = "demo", classify = "group", classifyName = "测试服务")
public class TestService{

    public static Set<String> sets=new HashSet<>();
    @ApiService(value = "查询用户", serviceKey = "getInfo", signType = 0, notes = "查询用户", params = {
            @ApiParam(value = "缓存地址", name = "dataPath", clazz = String.class, paramType = "body", required = true),
            @ApiParam(value = "缓存数据", name = "dataValue", clazz = Object.class, paramType = "body", required = true) })
    public void getInfo(Map<String, Object> result, JSONObject object){

       /* HttpServletRequest request = (HttpServletRequest)WcpThreadLocal.getRequest(object);
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> importMap = multiRequest.getFileMap();
        Iterator<Map.Entry<String, MultipartFile>> iterator1 = importMap.entrySet().iterator();
        while (iterator1.hasNext()) {
            Map.Entry<String, MultipartFile> entry1 = iterator1.next();
            String fileName = entry1.getKey();
            System.out.println(fileName+"===="+entry1.getValue());
        }*/
   /*     byte[] bytes = String.valueOf("hello word;你好,世界!").getBytes(StandardCharsets.UTF_8);
        // 创建一个字节数组输入流
        InputStream inputStream = new ByteArrayInputStream(bytes);
        MinioService.uploadFile(inputStream,"020b7cdcb90d4e96a262d41cac7ebfc9/aaa.txt");
        Calendar calendar=MinioService.getFileLastModified("020b7cdcb90d4e96a262d41cac7ebfc9/aaa.txt");
        result.put("time",DateUtil.calendarToString(calendar));*/
        //CacheMemory.setValue("1111","wcp","test1","bbbb");
       // result.put("data",CacheMemory.getValue("1111","wcp",String.class,"test1"));
     /*   Object[][] params = new Object[1][];
        params[0]=new Object[]{"1013","DD","TEST",null,105.550000,30.500000,null};
        WcpDataBase.execute("wcp","INSERT INTO WCP_HY_OBJECT(ID,TYPE,NAME,REGION,LOT,LAT,ALT) VALUES(?,?,?,?,?,?,?)",params);*/
       /* List<Map<String, Object>> queryMap = WcpDataBase.queryMap("wcp", "SELECT ID,NAME,CREATETIME,INTRODUCE,PROJECT FROM WCP_GA_ROLE WHERE PROJECT= ? ORDER BY NAME",
                new Object[]{"27c7df81b0864772a315531128b663b9"}, "id-ID;name-NAME;project-PROJECT");*/
        //List<Map<String, Object>> nodeList =WcpDataBase.queryMap("wcp","SELECT PROJECT,ROUTER,ID,TITLE,ICON,PT,ACCOUNT,HPOS,VPOS,ADAPOS,ISVERIFY,REROUTER FROM WCP_GC_PAGE LIMIT 5",null, "projectId-PROJECT;router-ROUTER;graphId-ID;title-TITLE;icon-ICON;pubTime-PT;account-ACCOUNT;hpos-HPOS;vpos-VPOS;adapos-ADAPOS;isVerify-ISVERIFY;reRouter-REROUTER");
        //List<Map<String, Object>> nodeList =WcpDataBase.queryMap("wcp","SELECT PROJECT,ROUTER,ID,TITLE,ICON,PT,ACCOUNT,HPOS,VPOS,ADAPOS,ISVERIFY,REROUTER FROM WCP_GC_PAGE LIMIT 5",null, null);
        //String sql = "SELECT PROJECT AS projectId,ROUTER AS router,ID,TITLE,ICON,PT,ACCOUNT,HPOS,VPOS,ADAPOS,ISVERIFY,REROUTER FROM WCP_GC_PAGE WHERE ROUTER = ? ORDER BY ROUTER";
        /*String sql="SELECT COUNT(*) AS COUNT FROM WCP_GA_USER";
        List<Map<String, Object>> nodeList = WcpDataBase.queryMap("wcp", sql, null, null);*/
        //result.put("data", nodeList);
        //Map<String, Object> map=X
        // result.put("project", acommonService.getProjectId());

       /* QueryWrapper wrapper=new QueryWrapper();
        wrapper.setTableName("WCP_SP_USER");
        //wrapper.select("ACCOUNT","USER_NAME");
        List<Map<String, Object>> re=WcpDataBase.queryMap("wcp",wrapper);*/
        try{
            List<Map<String,Object>> par=new ArrayList<>();
            for(int i=1;i<=2000;i++){
                Map<String,Object> map=new HashMap<>();
                map.put("FLOOD_ID", i);
                map.put("SUBBASIN_ID","66666");
                map.put("STCD","6666");
                map.put("TIME",Calendar.getInstance());
                map.put("RAIN",0.0);
                map.put("FLOW",71.5);
                map.put("LEVEL", 527.07);
                map.put("AREA_RAIN",2.0E-4);
                par.add(map);
            }

            WcpDataBase.insertMap("wcp","INSERT INTO WCP_HY_FLOOD_VALUE(FLOOD_ID, SUBBASIN_ID, STCD, TIME, RAIN, FLOW, LEVEL, AREA_RAIN)  VALUES(?,?,?,?,?,?,?,?)",par,null);

            //  MinioService.copyFolder("fcstraineval/08a0b2c0b6654b819789856641a0fe71","fcstraineval/1111");
           /* Map<String, Object> map=WcpDataBase.queryMapByPaging("wcp", "SELECT ACCOUNT ,USER_NAME FROM WCP_SP_USER  WHERE ACCOUNT=? and USER_NAME=?",
                    new Object[]{"admin","超级管理员"}, "account-ACCOUNT;userName-USER_NAME",1,2,null);
            result.put("user",map);*/
           /* List<Map<String,Object>> par=new ArrayList<>();
            Map<String,Object> map=new HashMap<>();
            map.put("JOB_LOG_ID",276);
            map.put("JOB_NAME","qwewre");
            map.put("JOB_GROUP","bbbb");
            map.put("INVOKE_TARGET","ccc");
            par.add(map);
            WcpDataBase.insertOrUpdate("wcp","INSERT INTO WCP_JOB_LOG(JOB_LOG_ID,JOB_NAME,JOB_GROUP,INVOKE_TARGET) VALUES(?,?,?,?)",par,null);*/
          /*  List<Map<String,Object>> listMap=new ArrayList<>();
            Map<String,Object> map=new HashMap<>();
            map.put("USER_ID","555");
            map.put("ACCOUNT","555");
            map.put("USER_NAME","444");
            listMap.add(map);
            Map<String,Object> map1=new HashMap<>();
            map1.put("USER_ID","444");
            map1.put("ACCOUNT","444");
            map1.put("USER_NAME","444");
            listMap.add(map1);
            System.out.println(WcpDataBase.insertMap("wcp","INSERT INTO WCP_SP_USER(USER_ID,ACCOUNT, USER_NAME) VALUES(?,?,?)",listMap,null));*/
          /*  ServiceGlobeInfo serviceGlobeInfo=WcpThreadLocal.getServiceGlobeInfo();
            Runnable task = () -> {
                // 执行线程逻辑
                WcpThreadLocal.setThreadLocalData(serviceGlobeInfo);
                String projectId = WcpThreadLocal.getProjectId();
                System.out.println("Project ID: " + projectId);
                // 清理ThreadLocal
                WcpThreadLocal.clear();
            };
            new Thread(task).start();*/

          /*  result.put("realdb",WcpDataBase.querySingleMap("realdb", "SELECT * FROM WCP_SP_USER", null, null));
            result.put("dm",WcpDataBase.querySingleMap("dm", "SELECT * FROM WCP_SP_USER", null, null));*/
          //  result.put("realdb",WcpDataBase.querySingleMap("realdb", "SELECT * FROM WCP_SP_USER", null, null));
          //  List<Map> treeNodeList = WcpDataBase.query("wcp", "SELECT ACCOUNT as count ,USER_NAME FROM WCP_SP_USER", null, Map.class);
          //  result.put("realdb",WcpThreadLocal.getUserAccount());
            //throw  new ServiceException("查询异常", HttpStatus.NO_CONTENT);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
    @ApiService(value = "上传文件", serviceKey = "upload", signType = 0)
    public void upload(Map<String, Object> result, JSONObject object) throws Exception {
        List<MultipartFile> multipartFileList= (List<MultipartFile>) object.get("dem");
        multipartFileList.forEach(t->{
            System.out.println(t.getOriginalFilename());
        });
    }
    public static void main(String[] args) {
        System.out.println(AESUtil.decrypt("rVcqOnzEXZDP6tNX+rdMOA=="));
    }
}
