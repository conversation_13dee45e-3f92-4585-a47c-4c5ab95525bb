package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiTimer;
import com.wcp.annotation.ApiTimerJob;
import org.springframework.stereotype.Component;


/**
 * 1. @Description TODO
 * 2. <AUTHOR>
 * 3. @Date 2024/6/24 15:07
 */
@ApiGroup(value = "定时任务", serviceKey = "task", classify = "task", classifyName = "定时任务")
public class TaskDemo {

    public void runTask1(JSONObject param) throws Exception
    {
        System.out.println("执行定时任务1:"+param);
    }

}
