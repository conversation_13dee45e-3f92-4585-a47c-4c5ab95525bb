package com.wcp.service;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiGroup;
import com.wcp.annotation.ApiParam;
import com.wcp.annotation.ApiService;
import com.wcp.execption.ServiceException;
import com.wcp.http.HttpStatus;
import com.wcp.quartz.domain.WcpJob;

import java.util.*;

/**
 * 1. @Description 测试接口
 * 2. <AUTHOR>
 * 3. @Date 2025/2/13 10:06
 */
@ApiGroup(value = "测试接口", serviceKey = "test", classify = "group", classifyName = "测试接口")
public class PlmService {

    public static Set<String> sets=new HashSet<>();
    @ApiService(value = "错误接口", serviceKey = "error", signType = 0, notes = "错误接口")
    public void error(Map<String, Object> result, JSONObject object){
        throw new ServiceException("业务接口出现错误");
    }

    @ApiService(value = "成功接口", serviceKey = "success", signType = 0, notes = "成功接口")
    public void success(Map<String, Object> result, JSONObject object){
        // 创建测试数据
        List<Map<String, Object>> dataList = new ArrayList<>();

        Map<String, Object> record1 = new HashMap<>();
        record1.put("id", 1);
        record1.put("name", "Alice");
        record1.put("age", 30);
        dataList.add(record1);

        Map<String, Object> record2 = new HashMap<>();
        record2.put("id", 2);
        record2.put("name", "Bob");
        record2.put("age", 25);
        dataList.add(record2);
        result.put(HttpStatus.DATA,dataList);
    }

    @ApiService(value = "延迟响应", serviceKey = "sleep", signType = 0, notes = "错误接口")
    public void sleep(Map<String, Object> result, JSONObject object){
        try {
            Thread.sleep(5000);
            // 创建测试数据
            List<Map<String, Object>> dataList = new ArrayList<>();

            Map<String, Object> record1 = new HashMap<>();
            record1.put("id", 1);
            record1.put("name", "Alice");
            record1.put("age", 30);
            dataList.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("id", 2);
            record2.put("name", "Bob");
            record2.put("age", 25);
            dataList.add(record2);
            result.put(HttpStatus.DATA,dataList);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
