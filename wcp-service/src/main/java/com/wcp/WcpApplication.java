package com.wcp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;


/**
 * @Description wcp应用程序启动入口
 * <AUTHOR>
 * @Date 2023/6/21 下午 5:30
 * @Version 3.0
 */


//@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
//@ComponentScan(basePackages = "com.wcp.*") // 替换成你的包名
public class WcpApplication {
    public static void main(String[] args) {
       SpringApplication.run(WcpApplication.class, args);
    }
}
