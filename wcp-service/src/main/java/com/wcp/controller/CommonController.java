package com.wcp.controller;

import com.alibaba.fastjson.JSONObject;
import com.wcp.annotation.ApiService;
import com.wcp.core.util.JsonUtil;
import com.wcp.data.ServiceGlobeInfo;
import com.wcp.data.WebServiceConst;
import com.wcp.http.HttpStatus;
import com.wcp.redis.CacheMemory;
import com.wcp.service.AcommonService;

import com.wcp.thread.WcpThreadLocal;
import com.wcp.utils.IpUtil;

import com.wcp.utils.StringUtil;
import com.wcp.utils.TokenUtil;
import org.apache.http.entity.ContentType;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 应用统一入口
 */
@Controller
@RequestMapping(value = "/")
public class CommonController {
    private static Logger logger = Logger.getLogger(CommonController.class);
    private static ApplicationContext context;
    @Autowired
    private TokenUtil tokenUtil;
    @Value("${spring.sso.header}")
    private String header;
    @Value("${spring.sso.openPermissions}")
    private boolean openPermissions;
    @Value("${spring.sso.iam}")
    private String iam;

    @Autowired
    public void setApplicationContext(ApplicationContext context) {
        this.context = context;
    }

    @Autowired
    private AcommonService acommonService;

    /**
     * 接口服务调用，用指定的服务路由进行分发
     *
     * @param jsonMap     数据传输JSON，常规标志参考WebServiceConst
     * @param request
     * @param response
     * @param httpSession
     * @return
     */
    @RequestMapping(value = "/**", produces = "application/json;charset=UTF-8")
    @ResponseBody
    @CrossOrigin
    public Object distributedService(@RequestBody(required = false) Map<String, Object> jsonMap,
									 HttpServletRequest request,
									 HttpServletResponse response,
									 HttpSession httpSession) throws IllegalAccessException, InstantiationException, IllegalArgumentException,
            InvocationTargetException {
        // 如果请求参数为空，初始化为空的HashMap
        if (jsonMap == null) jsonMap = new HashMap<String, Object>();
        // 创建返回结果对象
        JSONObject result = new JSONObject();
        // 创建全局服务信息
        // 初始化默认用户ID为访客
        String userId = "guestId";
        // 从请求头获取设备类型信息
        String deviceType = request.getHeader(WebServiceConst.DEVICE_TYPE);
        // 从请求头获取认证token
        String token = request.getHeader(header);
        // 从请求头获取项目ID，如果为空则使用默认值
        String projectId = StringUtil.isNotEmpty(request.getHeader("projectid")) ? request.getHeader("projectid") : "default";
        // 创建全局服务信息对象
        ServiceGlobeInfo serviceGlobeInfo = new ServiceGlobeInfo();
        // 设置设备类型
        serviceGlobeInfo.setDeviceType(deviceType);
        // 设置HTTP请求对象
        serviceGlobeInfo.setRequest(request);
        // 设置HTTP响应对象
        serviceGlobeInfo.setResponse(response);
        // 设置服务路径（Web应用根路径）
        serviceGlobeInfo.setServicePath(request.getSession().getServletContext().getRealPath("/"));
        // 设置HTTP会话对象
        serviceGlobeInfo.setHttpSession(httpSession);
        // 获取并设置客户端IP地址
        serviceGlobeInfo.setIp(IpUtil.getIpAddr(request));
        // 设置项目ID
        serviceGlobeInfo.setProjectId(projectId);
        // 如果token不为空且有效，则解析token获取用户信息
        if (token != null && tokenUtil.isValidToken(token)) {
            // 解析token获取用户声明信息
            Map<String, Object> tokenClaim = tokenUtil.getTokenValues(token);
            // 从token中提取用户ID并设置
            serviceGlobeInfo.setUserId(tokenClaim.get("ID") != null ? tokenClaim.get("ID").toString() : null);
            // 从token中提取用户账号并设置
            serviceGlobeInfo.setUserAccount(tokenClaim.get("ACCOUNT") != null ? tokenClaim.get("ACCOUNT").toString() : null);
            // 从token中提取用户名并设置
            serviceGlobeInfo.setUserName(tokenClaim.get("USERNAME") != null ? tokenClaim.get("USERNAME").toString() : null);
        }
        // 如果开启权限校验且使用auth认证方式
        if (openPermissions && "auth".equals(iam)) {
            // todo 不同的项目可能字段不一样，根据时间的情况修改
            // 从缓存中获取用户信息
            Map<String, Object> values = CacheMemory.getValue(projectId, "wcp", Map.class, token);
            // 从缓存中设置用户ID
            serviceGlobeInfo.setUserId(values.get("userId").toString());
            // 从缓存中设置用户账号
            serviceGlobeInfo.setUserAccount(values.get("username").toString());
            // 从缓存中设置用户昵称
            serviceGlobeInfo.setUserName(values.get("nickName").toString());
        }

        // 如果WCPID为空，设置默认值
        if (serviceGlobeInfo.getWCPID() == null) {
            serviceGlobeInfo.setWCPID("default");
        }
        // 将请求参数转换为JSONObject
        JSONObject object = new JSONObject(jsonMap);
        // 解析请求URL中的查询字符串参数
        JSONObject jsonObject = JsonUtil.analysisQueryString(request);
        // 合并JSON对象，过滤重复参数
        JsonUtil.mergeJSONFilterRepeat(object, jsonObject);
        // 如果参数中没有用户ID，则设置默认用户ID
        if (object.getString("userId") == null) {
            object.put("userId", userId);
        }
        // 如果参数中没有项目ID，则设置默认项目ID
        if (object.getString("projectId") == null) {
            object.put("projectId", projectId);
        }
        // 将全局服务信息添加到参数对象中
        object.put(WebServiceConst.SERVICE_GLOBE_INFO, serviceGlobeInfo);
        // 将服务信息设置到当前线程本地存储
        WcpThreadLocal.setThreadLocalData(serviceGlobeInfo);
        // 将原始参数设置到请求属性中
        request.setAttribute("param", jsonMap);
        // 获取请求的服务路径
        String serviceKey = request.getServletPath();
        // 去掉路径前的斜杠
        serviceKey = serviceKey.substring(1);
        // 根据服务路径获取对应的处理方法
        Method method = AcommonService.getServiceMethod(serviceKey);
        // 如果找到对应的服务方法
        if (method != null) {
            // 获取方法所属的类
            Class<?> clazz = method.getDeclaringClass();
            //	AcommonService service = getCommonService(clazz);
            // 获取方法上的ApiService注解
            ApiService apiService = method.getAnnotation(ApiService.class);
            // 通过反射调用服务方法，传入result和object参数
            Object backValue = method.invoke(context.getBean(clazz), result, object);
            // 如果方法有返回值，直接返回
            if (!Void.TYPE.equals(method.getReturnType())) {
                return backValue;
            }
            // 释放本地线程空间
            WcpThreadLocal.clear();
            // 默认情况下 method.invoke调用没有报错，表示反射的方法正常执行成功了,直接加上成功状态码
            // 根据ApiService注解配置设置响应状态
            if (apiService != null && apiService.nomark()) {
                // 如果注解标记为不需要状态码，则设置成功状态
                result.put(HttpStatus.CODE, HttpStatus.SUCCESS);
                result.put(HttpStatus.ISSUCCESS, true);
            } else {
                // 否则设置为失败状态
                result.put(HttpStatus.ISSUCCESS, false);
            }
				/*System.out.println(Void.TYPE+">>>>>"+method.getReturnType());
				if(!Void.TYPE.equals(method.getReturnType())) {
					return backValue;
				}else if(!nomark) {
					result.put("code",HttpStatus.SUCCESS);
				}*/
				/*if (result.get(WebServiceConst.RESULT_NOMARK) != null
						&& (boolean) result.get(WebServiceConst.RESULT_NOMARK)) {
					result.remove(WebServiceConst.RESULT_NOMARK);
				} else if(!nomark && result.get(WebServiceConst.SUCCESS) == null) {
					result.put(WebServiceConst.SUCCESS, true);
				}*/
            // 异常处理块（目前为空实现）
            try {
            } catch (Exception e) {
                // 打印异常堆栈信息
                e.printStackTrace();
				/*if (result.get(WebServiceConst.RESULT_NOMARK) != null
						&& (boolean) result.get(WebServiceConst.RESULT_NOMARK)) {
					result.remove(WebServiceConst.RESULT_NOMARK);
				} else if(!nomark) {
					result.put(WebServiceConst.SUCCESS, false);
					result.put(WebServiceConst.MESSAGE, "服务执行失败,未知错误!");
					String msg = StringUtil.getStackTraceInfo(e);
					JsonUtil.setServiceFeedBackValue(result, WebServiceConst.EXCEPTION, msg);
				}*/
                // 记录错误日志
                logger.error("服务执行失败,未知错误!", e);
            }
        }
        // 返回处理结果
        return result;
    }


    /**
     * 用于文件处理
     *
     * @param request
     * @param response
     * @param httpSession
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     */
    @RequestMapping(value = "/stream/**")
    @ResponseBody
    @CrossOrigin
    public Object distributedFileService(HttpServletRequest request, HttpServletResponse response, HttpSession httpSession) throws IllegalAccessException, InstantiationException, IllegalArgumentException,
            InvocationTargetException {
        JSONObject result = new JSONObject();
        // 创建全局服务信息
        String userId = "guestId";
        String deviceType = request.getHeader(WebServiceConst.DEVICE_TYPE);
        String token = request.getHeader("token");
        String projectId = StringUtil.isNotEmpty(request.getHeader("projectId")) ? request.getHeader("projectId") : "default";
        ServiceGlobeInfo serviceGlobeInfo = new ServiceGlobeInfo();
        serviceGlobeInfo.setDeviceType(deviceType);
        serviceGlobeInfo.setRequest(request);
        serviceGlobeInfo.setResponse(response);
        serviceGlobeInfo.setServicePath(request.getSession().getServletContext().getRealPath("/"));
        serviceGlobeInfo.setHttpSession(httpSession);
        serviceGlobeInfo.setIp(IpUtil.getIpAddr(request));
        serviceGlobeInfo.setProjectId(projectId);
        if (token != null && tokenUtil.isValidToken(token)) {
            Map<String, Object> tokenClaim = tokenUtil.getTokenValues(token);
            serviceGlobeInfo.setUserId(tokenClaim.get("ID").toString());
            serviceGlobeInfo.setUserAccount(tokenClaim.get("ACCOUNT").toString());
            serviceGlobeInfo.setUserName(tokenClaim.get("USERNAME").toString());
        }
        if (serviceGlobeInfo.getWCPID() == null) {
            serviceGlobeInfo.setWCPID("default");
        }
        JSONObject object = JsonUtil.analysisQueryString(request);
        // 上传文件统一封装到object中
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> importMap = multiRequest.getFileMap();
        Iterator<Map.Entry<String, MultipartFile>> fileIt = importMap.entrySet().iterator();
        while (fileIt.hasNext()) {
            Map.Entry<String, MultipartFile> entry = fileIt.next();
            String fielName = entry.getKey();
            List<MultipartFile> files = multiRequest.getFiles(fielName);
            // 如果只上传了一个文件，则返回的是一个文件对象，否则返回的是一个文件数组对象
            if (files != null && files.size() == 1) {
                object.put(fielName, files.get(0));
            } else {
                object.put(fielName, files);
            }
        }

        if (object.getString("userId") == null) {
            object.put("userId", userId);
        }
        if (object.getString("projectId") == null) {
            object.put("projectId", projectId);
        }
        object.put(WebServiceConst.SERVICE_GLOBE_INFO, serviceGlobeInfo);
        WcpThreadLocal.setThreadLocalData(serviceGlobeInfo);
        String serviceKey = request.getServletPath();
        if (serviceKey.startsWith("/stream")) {
            serviceKey = serviceKey.substring(8);
        } else {
            serviceKey = serviceKey.substring(1);
        }
        Method method = AcommonService.getServiceMethod(serviceKey);
        if (method != null) {
            Class<?> clazz = method.getDeclaringClass();
            //	AcommonService service = getCommonService(clazz);
            ApiService apiService = method.getAnnotation(ApiService.class);
            Object backValue = method.invoke(context.getBean(clazz), result, object);
            if (!Void.TYPE.equals(method.getReturnType())) {
                return backValue;
            }
            // 释放本地线程空间
            WcpThreadLocal.clear();
            // 默认情况下 method.invoke调用没有报错，表示反射的方法正常执行成功了,直接加上成功状态码
            if (apiService != null && apiService.nomark()) {
                result.put("code", HttpStatus.SUCCESS);
            }

            try {
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("服务执行失败,未知错误!", e);
            }
        }
        return result;
    }
}
