server:
  port: 9091
  servlet:
    context-path: /hfds
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: Aa@123456
  redis:
    host: ***************
    port: 8379
    password: Admin@123
  sso:
    secretKey: 'wcp-sso-by-andylu-uzhitingnizhenbangcereshuzhiti'
    issuer: 'ebi_platform'
    token-expire-time-app: 604800000 # 7 * 24 * 60 * 60 * 1000  app token有效期默认7天
    token-expire-time-web: 60  #60分钟
    header: Authorization
    iam: auth #单点登录类型(cas,auth等)这个参数必须将openPermissions改为true才会生效, 如果使用平台配置的登录iam值应为login
    openPermissions: false #是否开启发布系统权限校验 debug可以改成false
    isCache: false #这个是用来切换token方式， true为缓存模式，false为不使用缓存   使用缓存可是实现防虫登录，强制用户下线
    captcha: false # 开启验证码
    #单点登录服务地址
    oauth2:
      client-id: cidea5689aa5dfc41a4b55dd336caa555c6
      client-secret: sideea09ba3bc874aeb8eecbfd2d4288cb9
      authorization-grant-type: authorization_code
      redirect-uri: http://*************:5174
      token-uri: http://**************:9099/tias/oauth/token
      user-info-uri: http://**************:9099/tias/authorize/getUserInfo
      check-token-uri: http://**************:9099/tias/oauth/check_token
  minio:
    endpoint: http://**************:9000
    accessKey: zonghe666
    secretKey: zonghe666
    bucketName: zonghe
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB

wcp:
  excludeModel: #不希望被scanRequests扫描到接口在这里排除加载的模块，默认为空
  debugSQL: true #是否打印sql调试，默认为true
  quartz-auto-start: false #定时器是否自动启动，默认为false
